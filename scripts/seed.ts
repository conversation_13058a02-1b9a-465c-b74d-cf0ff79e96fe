/**
 * Database seeder for users and profiles (idempotent).
 * - Creates three users (admin, grandmaster, athlete) with deterministic emails.
 * - Ensures profiles are upserted with correct roles and full names.
 * - Optionally seeds a sample submission for the athlete.
 *
 * Usage: npm run db:seed
 *
 * Requires:
 *   - SUPABASE_SERVICE_ROLE_KEY
 *   - NEXT_PUBLIC_SUPABASE_URL
 */

import crypto from 'node:crypto'
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
dotenv.config({ path: '.env.local' })

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!SUPABASE_URL || !SERVICE_ROLE_KEY) {
  console.error(
    `Missing SUPABASE_SERVICE_ROLE_KEY or NEXT_PUBLIC_SUPABASE_URL in environment.
    ${SUPABASE_URL}, ${SERVICE_ROLE_KEY}`,
  )
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: { autoRefreshToken: false, persistSession: false },
})

export type UserSeed = {
  email: string
  password: string
  role: 'admin' | 'grandmaster' | 'athlete'
  fullName: string
}

const users: UserSeed[] = [
  {
    email: '<EMAIL>',
    password: 'Password123!',
    role: 'admin',
    fullName: 'Admin User',
  },
  {
    email: '<EMAIL>',
    password: 'Password123!',
    role: 'grandmaster',
    fullName: 'Grand Master',
  },
  {
    email: '<EMAIL>',
    password: 'Password123!',
    role: 'athlete',
    fullName: 'Jane Athlete',
  },
]

const placeholders: Record<
  UserSeed['role'],
  {
    avatar_url: string
    country: string
    gender: string
    weight_category: 'under_95' | 'over_95'
    titles: string[]
    social_links: Record<string, string>
  }
> = {
  admin: {
    avatar_url: 'https://i.pravatar.cc/150?img=1',
    country: 'USA',
    gender: 'male',
    weight_category: 'over_95',
    titles: ['Admin Champion'],
    social_links: {
      twitter: 'https://twitter.com/admin',
      linkedin: 'https://linkedin.com/in/admin',
    },
  },
  grandmaster: {
    avatar_url: 'https://i.pravatar.cc/150?img=2',
    country: 'UK',
    gender: 'female',
    weight_category: 'under_95',
    titles: ['Grandmaster Champion'],
    social_links: {
      instagram: 'https://instagram.com/gm',
      twitter: 'https://twitter.com/gm',
    },
  },
  athlete: {
    avatar_url: 'https://i.pravatar.cc/150?img=3',
    country: 'Canada',
    gender: 'other',
    weight_category: 'under_95',
    titles: ['Rookie Champion'],
    social_links: {
      instagram: 'https://instagram.com/athlete',
      twitter: 'https://twitter.com/athlete',
    },
  },
}

export async function ensureUserAndProfile(user: UserSeed) {
  // 1. Create user (or get existing)
  let userId: string | undefined
  let baseUsername = user.email
    .split('@')[0]
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '')
  if (baseUsername.length < 4) {
    baseUsername += crypto
      .randomBytes(2)
      .toString('hex')
      .slice(0, 4 - baseUsername.length)
  }
  const username = baseUsername
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: user.email,
      password: user.password,
      email_confirm: true,
      user_metadata: { role: user.role, full_name: user.fullName },
    })
    if (error) {
      // If user exists, fetch their ID
      if (error.status === 422) {
        // User already exists: list all users and find by email
        const { data: listData, error: listErr } =
          await supabase.auth.admin.listUsers()
        if (listErr) throw listErr
        const existingUser = listData.users.find((u) => u.email === user.email)
        if (!existingUser) throw new Error('User not found after duplicate')
        userId = existingUser.id
      } else {
        throw error
      }
    } else {
      userId = data?.user?.id
    }
  } catch (err) {
    console.error(`Failed to create/find user ${user.email}:`, err)
    return
  }
  if (!userId) {
    console.error(`Could not resolve user ID for ${user.email}`)
    return
  }

  // 2. Ensure unique username (append suffix if needed)
  let finalUsername = username
  let suffix = 0
  while (true) {
    const { data } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', finalUsername)
      .maybeSingle()
    if (!data) break
    if (data.id === userId) break // Already correct
    suffix += 1
    finalUsername = `${username}_${suffix}`
    if (suffix > 1000) {
      finalUsername = `user_${userId}`
      break
    }
  }

  // 3. Upsert profile with placeholders
  const placeholder = placeholders[user.role]
  const { error: upsertErr } = await supabase.from('profiles').upsert(
    {
      id: userId,
      username: finalUsername,
      full_name: user.fullName,
      role: user.role,
      avatar_url: placeholder.avatar_url,
      country: placeholder.country,
      gender: placeholder.gender,
      weight_category: placeholder.weight_category,
      titles: placeholder.titles,
      social_links: placeholder.social_links,
    },
    { onConflict: 'id' },
  )
  if (upsertErr) {
    console.error(`Failed to upsert profile for ${user.email}:`, upsertErr)
  } else {
    console.log(`Seeded user: ${user.email} (${user.role})`)
  }
}

async function main() {
  for (const user of users) {
    await ensureUserAndProfile(user)
  }

  // Optionally: seed a submission for athlete
  // (Uncomment and adjust if submissions table and exercises exist)
  const { data: athlete } = await supabase
    .from('profiles')
    .select('id')
    .eq('username', 'athlete')
    .maybeSingle()
  const { data: exercise } = await supabase
    .from('exercises')
    .select('id')
    .eq('title', 'Pronation Lift')
    .maybeSingle()
  if (athlete && exercise) {
    const { error: subErr } = await supabase.from('submissions').insert([
      {
        user_id: athlete.id,
        exercise_id: exercise.id,
        video_url: 'https://www.youtube.com/watch?v=athlete_submission',
        weight_lifted: 25,
        status: 'pending',
        notes: 'Seeded submission',
        submitted_at: new Date().toISOString(),
      },
    ])
    if (subErr) {
      console.error('Error seeding sample submission for athlete:', subErr)
    } else {
      console.log('Seeded sample submission for athlete.')
    }
  }
  console.log('Seeding complete.')
  process.exit(0)
}

if (process.env.NODE_ENV !== 'test') {
  main().catch((err) => {
    console.error('Seeding failed:', err)
    process.exit(1)
  })
}
