#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the RLS policies in the Supabase database
 *
 * Usage: npx scripts/test-rls-policies.mjs
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Supabase URL or anon key not found in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Test users with different roles
const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'password123',
  },
  grandmaster: {
    email: '<EMAIL>',
    password: 'password123',
  },
  athlete: {
    email: '<EMAIL>',
    password: 'password123',
  },
}

// Test functions
async function testAsAnonymous() {
  console.log('\n🔍 Testing as anonymous user...')

  // Test SELECT on users
  console.log('\nTesting SELECT on users...')
  let { data, error } = await supabase.from('users').select('*').limit(5)
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} users`,
  )

  // Test SELECT on exercises
  console.log('\nTesting SELECT on exercises...')
  ;({ data, error } = await supabase.from('exercises').select('*').limit(5))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} exercises`,
  )

  // Test SELECT on approved submissions
  console.log('\nTesting SELECT on approved submissions...')
  ;({ data, error } = await supabase
    .from('submissions')
    .select('*')
    .eq('status', 'approved')
    .limit(5))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} approved submissions`,
  )

  // Test SELECT on all submissions (should fail)
  console.log('\nTesting SELECT on all submissions (should fail)...')
  ;({ data, error } = await supabase.from('submissions').select('*').limit(5))
  console.log(
    error
      ? `✅ Expected error: ${error.message}`
      : `❌ Unexpected success: Retrieved ${data.length} submissions`,
  )

  // Test SELECT on medals
  console.log('\nTesting SELECT on medals...')
  ;({ data, error } = await supabase.from('medals').select('*').limit(5))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} medals`,
  )

  // Test INSERT on users (should fail)
  console.log('\nTesting INSERT on users (should fail)...')
  ;({ data, error } = await supabase.from('users').insert([
    {
      email: '<EMAIL>',
      name: 'Test User',
      gender: 'male',
      weight_category: 'under_95kg',
      role: 'athlete',
    },
  ]))
  console.log(
    error
      ? `✅ Expected error: ${error.message}`
      : `❌ Unexpected success: ${JSON.stringify(data)}`,
  )
}

async function testAsUser(role) {
  console.log(`\n🔍 Testing as ${role}...`)

  // Sign in as the test user
  const { error: authError } = await supabase.auth.signInWithPassword(
    testUsers[role],
  )

  if (authError) {
    console.error(`❌ Error signing in as ${role}: ${authError.message}`)
    return
  }

  console.log(`✅ Signed in as ${role}`)

  // Get the user's ID
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('email', testUsers[role].email)
    .single()

  if (userError) {
    console.error(`❌ Error getting ${role} user ID: ${userError.message}`)
    return
  }

  const userId = userData.id

  // Test operations based on role
  if (role === 'admin') {
    await testAdminOperations(userId)
  } else if (role === 'grandmaster') {
    await testGrandmasterOperations(userId)
  } else if (role === 'athlete') {
    await testAthleteOperations(userId)
  }

  // Sign out
  await supabase.auth.signOut()
  console.log(`✅ Signed out as ${role}`)
}

async function testAdminOperations(userId) {
  let data
  let error

  // Test SELECT on all submissions
  console.log('\nTesting SELECT on all submissions...')
  ;({ data, error } = await supabase.from('submissions').select('*').limit(5))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} submissions`,
  )

  // Test INSERT on exercises
  console.log('\nTesting INSERT on exercises...')
  ;({ data, error } = await supabase
    .from('exercises')
    .insert([
      {
        title: 'Test Exercise',
        description: 'Test description',
        video_tutorial_url: 'https://example.com/video',
        created_by: userId,
      },
    ])
    .select())
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Inserted exercise with ID ${data[0].id}`,
  )

  // If exercise was created, test UPDATE and DELETE
  if (data && data.length > 0) {
    const exerciseId = data[0].id

    // Test UPDATE on exercises
    console.log('\nTesting UPDATE on exercises...')
    ;({ data, error } = await supabase
      .from('exercises')
      .update({ description: 'Updated description' })
      .eq('id', exerciseId)
      .select())
    console.log(
      error ? `❌ Error: ${error.message}` : '✅ Success: Updated exercise',
    )

    // Test DELETE on exercises
    console.log('\nTesting DELETE on exercises...')
    ;({ data, error } = await supabase
      .from('exercises')
      .delete()
      .eq('id', exerciseId))
    console.log(
      error ? `❌ Error: ${error.message}` : '✅ Success: Deleted exercise',
    )
  }
}

async function testGrandmasterOperations(userId) {
  let data
  let error

  // Test SELECT on all submissions
  console.log('\nTesting SELECT on all submissions...')
  ;({ data, error } = await supabase.from('submissions').select('*').limit(5))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} submissions`,
  )

  // Test INSERT on exercises (should fail)
  console.log('\nTesting INSERT on exercises (should fail)...')
  ;({ data, error } = await supabase.from('exercises').insert([
    {
      title: 'Test Exercise',
      description: 'Test description',
      video_tutorial_url: 'https://example.com/video',
      created_by: userId,
    },
  ]))
  console.log(
    error
      ? `✅ Expected error: ${error.message}`
      : `❌ Unexpected success: ${JSON.stringify(data)}`,
  )

  // Test UPDATE on submissions
  console.log('\nTesting UPDATE on submissions...')
  ;({ data, error } = await supabase.from('submissions').select('*').limit(1))

  if (data && data.length > 0) {
    const submissionId = data[0].id
    ;({ data, error } = await supabase
      .from('submissions')
      .update({ status: 'approved' })
      .eq('id', submissionId)
      .select())
    console.log(
      error
        ? `❌ Error: ${error.message}`
        : '✅ Success: Updated submission status',
    )
  } else {
    console.log('⚠️ No submissions found to update')
  }
}

async function testAthleteOperations(userId) {
  let data
  let error

  // Test SELECT on own submissions
  console.log('\nTesting SELECT on own submissions...')
  ;({ data, error } = await supabase
    .from('submissions')
    .select('*')
    .eq('user_id', userId))
  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Retrieved ${data.length} own submissions`,
  )

  // Test SELECT on all submissions (should only see approved and own)
  console.log(
    '\nTesting SELECT on all submissions (should only see approved and own)...',
  )
  ;({ data, error } = await supabase.from('submissions').select('*').limit(10))

  if (!error && data) {
    const approvedOrOwn = data.every(
      (sub) => sub.status === 'approved' || sub.user_id === userId,
    )
    console.log(
      approvedOrOwn
        ? `✅ Success: All ${data.length} submissions are either approved or own`
        : '❌ Error: Some submissions are neither approved nor own',
    )
  } else {
    console.log(`❌ Error: ${error.message}`)
  }

  // Test INSERT on submissions
  console.log('\nTesting INSERT on submissions...')

  // First, get an exercise ID
  const { data: exerciseData, error: exerciseError } = await supabase
    .from('exercises')
    .select('id')
    .limit(1)

  if (exerciseError || !exerciseData || exerciseData.length === 0) {
    console.log(
      '❌ Error: Could not find an exercise to use for submission test',
    )
    return
  }

  const exerciseId = exerciseData[0].id

  // Insert a submission
  ;({ data, error } = await supabase
    .from('submissions')
    .insert([
      {
        user_id: userId,
        exercise_id: exerciseId,
        video_url: 'https://example.com/video',
        weight_lifted: 100,
        status: 'pending',
      },
    ])
    .select())

  console.log(
    error
      ? `❌ Error: ${error.message}`
      : `✅ Success: Inserted submission with ID ${data[0].id}`,
  )

  // If submission was created, test UPDATE
  if (data && data.length > 0) {
    const submissionId = data[0].id

    // Test UPDATE on own pending submission
    console.log('\nTesting UPDATE on own pending submission...')
    ;({ data, error } = await supabase
      .from('submissions')
      .update({ weight_lifted: 110 })
      .eq('id', submissionId)
      .select())
    console.log(
      error ? `❌ Error: ${error.message}` : '✅ Success: Updated submission',
    )

    // Test UPDATE on someone else's submission (should fail)
    console.log(
      "\nTesting UPDATE on someone else's submission (should fail)...",
    )
    ;({ data, error } = await supabase
      .from('submissions')
      .update({ status: 'approved' })
      .neq('user_id', userId)
      .limit(1))
    console.log(
      error
        ? `✅ Expected error: ${error.message}`
        : `❌ Unexpected success: ${JSON.stringify(data)}`,
    )
  }
}

// Main function
async function main() {
  console.log('🔒 Testing RLS policies...')

  // Test as anonymous user
  await testAsAnonymous()

  // Test as each role
  for (const role of Object.keys(testUsers)) {
    await testAsUser(role)
  }

  console.log('\n✅ RLS policy tests completed!')
}

// Run the main function
main().catch((error) => {
  console.error('❌ An error occurred:', error)
  process.exit(1)
})
