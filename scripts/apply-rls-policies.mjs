#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply the optimized RLS policies to the Supabase database
 *
 * Usage: npx scripts/apply-rls-policies.mjs
 */

import { execSync } from 'node:child_process'
import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

// Get the directory name
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

// Function to execute shell commands
function executeCommand(command) {
  console.log(`\n> ${command}`)
  try {
    const output = execSync(command, { cwd: rootDir, stdio: 'inherit' })
    return output
  } catch (err) {
    console.error(`Error executing command: ${command}`)
    console.error(err.message)
    process.exit(1)
  }
}

// Main function
async function main() {
  console.log('🔒 Applying optimized RLS policies to the Supabase database...')

  // Get the RLS policies file path
  const rlsPoliciesFilePath = path.join(
    rootDir,
    'supabase',
    'migrations',
    '20250316_optimized_rls_policies.sql',
  )

  if (!fs.existsSync(rlsPoliciesFilePath)) {
    console.error(`❌ RLS policies file not found: ${rlsPoliciesFilePath}`)
    process.exit(1)
  }

  // Apply the RLS policies using psql
  console.log('\n🔄 Applying RLS policies...')

  // Use the Supabase local database connection
  executeCommand(
    `PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres -f ${rlsPoliciesFilePath}`,
  )

  console.log('\n✅ RLS policies applied successfully!')

  // Show the applied policies
  console.log('\n📊 Current RLS policies:')
  executeCommand(
    `PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres -c "SELECT tablename, policyname, permissive, roles, cmd, qual, with_check FROM pg_policies WHERE schemaname = 'public' ORDER BY tablename, cmd;"`,
  )
}

// Run the main function
main().catch((error) => {
  console.error('❌ An error occurred:', error)
  process.exit(1)
})
