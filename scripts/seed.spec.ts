import { beforeEach, describe, expect, it, vi } from 'vitest'
import type { UserSeed } from './seed'

// Stub builder and Supabase client
const createUserMock = vi.fn()
const listUsersMock = vi.fn()
const maybeSingleMock = vi.fn()
const upsertMock = vi.fn()

const builder = {
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  maybeSingle: maybeSingleMock,
  upsert: upsertMock,
}

const supabaseStub = {
  auth: { admin: { createUser: createUserMock, listUsers: listUsersMock } },
  from: vi.fn(() => builder),
}

// Mock supabase-js before importing the seed module
vi.mock('@supabase/supabase-js', () => ({
  createClient: () => supabaseStub,
}))

describe('ensureUserAndProfile', () => {
  const user: UserSeed = {
    email: '<EMAIL>',
    password: 'Password123!',
    role: 'admin',
    fullName: 'Test User',
  }

  beforeEach(() => {
    vi.resetAllMocks()
    // Default: no username collision
    maybeSingleMock.mockResolvedValue({ data: null })
  })

  it('handles createUser duplicate error by listing users', async () => {
    createUserMock.mockResolvedValue({
      data: undefined,
      error: { status: 422 },
    })
    listUsersMock.mockResolvedValue({
      data: { users: [{ id: 'user-1', email: user.email }] },
      error: null,
    })
    upsertMock.mockResolvedValue({ error: null })

    const { ensureUserAndProfile } = await import('./seed')
    await ensureUserAndProfile(user)

    expect(listUsersMock).toHaveBeenCalled()
    expect(upsertMock).toHaveBeenCalledWith(
      expect.objectContaining({ id: 'user-1' }),
      { onConflict: 'id' },
    )
  })

  it('resolves username collision by suffix', async () => {
    createUserMock.mockResolvedValue({
      data: { user: { id: 'user-2' } },
      error: null,
    })
    maybeSingleMock
      .mockResolvedValueOnce({ data: { id: 'other-id' } })
      .mockResolvedValueOnce({ data: null })
    upsertMock.mockResolvedValue({ error: null })

    const { ensureUserAndProfile } = await import('./seed')
    await ensureUserAndProfile(user)

    const baseUsername = user.email.split('@')[0].toLowerCase()
    expect(upsertMock).toHaveBeenCalledWith(
      expect.objectContaining({ username: `${baseUsername}_1` }),
      { onConflict: 'id' },
    )
  })
})
