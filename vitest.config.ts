/// <reference types="vitest" />
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths' // Import the plugin
import { defineConfig } from 'vitest/config'

export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths(), // Add the plugin
  ],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './vitest.setup.ts', // Optional: For global setup like mocks
    // Include files matching pattern(s)
    include: [
      'src/**/*.test.{ts,tsx}',
      '__tests__/**/*.spec.{ts,tsx}',
      '__tests__/**/*.test.{ts,tsx}',
    ],
    // Exclude files matching pattern(s)
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
    ],
    // Mock environment variables (use placeholder values)
    env: {
      NEXT_PUBLIC_SUPABASE_ENV: 'local',
      NEXT_PUBLIC_SUPABASE_URL: 'http://localhost:54321',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: 'mock-anon-key',
      NEXT_PUBLIC_SUPABASE_URL_PROD: 'https://mock.supabase.co',
      NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD: 'mock-prod-anon-key',
      SUPABASE_SERVICE_ROLE_KEY: 'mock-service-key',
      SUPABASE_SERVICE_ROLE_KEY_PROD: 'mock-prod-service-key',
    },
  },
})
