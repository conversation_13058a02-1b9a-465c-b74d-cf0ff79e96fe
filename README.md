# Armwrestling Power Arena

A web app to track, analyze, and improve armwrestling performance. Built with Next.js 14+, Supabase, and a modular architecture.

## Features

- Exercise submissions and rankings
- Profile management with role-based access
- Medal system and audit logging
- PWA support and offline mode

## Quick Start

```bash
git clone https://github.com/your-username/arm_pwr_arena.git
cd arm_pwr_arena
npm install
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to get started.
