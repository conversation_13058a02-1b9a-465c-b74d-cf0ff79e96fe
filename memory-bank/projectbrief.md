# Project Brief

*   **Project Name:** arm_pwr_arena
*   **Core Goal:** To provide a Progressive Web App (PWA) where armwrestlers can submit performance videos for specific exercises, get evaluated by experts, earn points/medals, and compete in global rankings.
*   **Key Features:** 
    *   User authentication (Email/Password, Social) & profile management.
    *   Role-based access control (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>).
    *   Curated exercise library with details and video tutorials.
    *   Video submission system (via YouTube, TikTok, Instagram links) with rate limiting.
    *   Expert evaluation system (Grandmasters/Admins) with private feedback.
    *   Points and medal allocation based on performance.
    *   Realtime notification system (e.g., for evaluation results).
    *   Global ranking system with filtering.
    *   Admin interface for user and exercise management.
    *   Progressive Web App (PWA) features: Installability, Offline support.
*   **Target Audience:** Armwrestlers (all levels), Armwrestling Coaches/Experts (potential Grandmasters), Administrators.
*   **Success Metrics:** [User adoption, submission volume, evaluation turnaround time, ranking engagement, PWA installation rate - placeholders, need confirmation]

*This document is the foundation for the project. It defines the core requirements and goals, serving as the source of truth for the project scope.* 