import type { UserRole } from '@/libs/permissions/types'
import type { User } from '@supabase/supabase-js'

/**
 * Re-export UserRole for convenience
 */
export type { UserRole }

/**
 * Unified user profile interface
 * Combines data from auth.users (Supabase managed) + public.profiles (custom table)
 * Used consistently across client and server
 */
export interface UserProfile {
  id: string // from auth.users.id
  email: string // from auth.users.email
  username: string // from public.profiles.username
  role: UserRole // from public.profiles.role
  full_name?: string | null // from public.profiles.full_name
  avatar_url?: string | null // from public.profiles.avatar_url
  country?: string | null // from public.profiles.country
  gender?: 'male' | 'female' | null // from public.profiles.gender
  weight_category?: 'under_95kg' | 'over_95kg' | null // from public.profiles.weight_category
  titles?: string[] | null // from public.profiles.titles
  social_links?: Record<string, string> | null // from public.profiles.social_links
  total_points: number // from public.profiles.total_points
  last_submission_at?: string | null // from public.profiles.last_submission_at
  created_at: string // from public.profiles.created_at
  updated_at: string // from public.profiles.updated_at
}

/**
 * Authentication error types for better error handling
 */
export type AuthErrorType =
  | 'EMAIL_ALREADY_REGISTERED'
  | 'INVALID_CREDENTIALS'
  | 'EMAIL_NOT_CONFIRMED'
  | 'WEAK_PASSWORD'
  | 'INVALID_EMAIL'
  | 'RATE_LIMITED'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR'

/**
 * Enhanced authentication response with typed errors
 */
export interface EnhancedAuthResponse {
  success: boolean
  error?: string | null
  errorType?: AuthErrorType
  needsConfirmation?: boolean
  data?: {
    message?: string
    user?: User
    profile?: UserProfile
  }
}

/**
 * Unified authentication context interface
 * Single source of truth for all authentication state and methods
 */
export interface AuthContextType {
  // Authentication state
  user: User | null
  profile: UserProfile | null
  userRole: UserRole | null
  loading: boolean
  isAuthenticated: boolean

  // Core authentication methods
  signInWithEmail: (
    email: string,
    password: string,
  ) => Promise<EnhancedAuthResponse>
  signUpWithEmail: (
    email: string,
    password: string,
  ) => Promise<EnhancedAuthResponse>
  signInWithSocial: (
    provider: 'google' | 'facebook' | 'apple',
  ) => Promise<EnhancedAuthResponse>
  signOut: () => Promise<void>

  // Password management
  resetPassword: (email: string) => Promise<EnhancedAuthResponse>
  updatePassword: (newPassword: string) => Promise<EnhancedAuthResponse>

  // Profile management
  updateProfile: (data: Partial<UserProfile>) => Promise<EnhancedAuthResponse>
  refreshProfile: () => Promise<void>
}

/**
 * Auth context value type (alias for convenience)
 */
export type AuthContextValue = AuthContextType
