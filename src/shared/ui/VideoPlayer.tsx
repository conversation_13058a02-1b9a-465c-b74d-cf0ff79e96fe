'use client'

import { VideoOff } from 'lucide-react'
import { useEffect, useState } from 'react'

/**
 * Extracts video ID from URL using the provided regular expression
 * @param url Video URL
 * @param regExp Regular expression to extract the video ID
 * @param groupIndex Index of the capturing group containing the ID (default: 1)
 * @param requiredLength Required length of the ID (optional)
 * @returns Video ID if valid, empty string otherwise
 */
function getVideoId(
  url: string,
  regExp: RegExp,
  groupIndex = 1,
  requiredLength?: number,
): string {
  if (!url) return ''

  const match = url.match(regExp)
  const id = match?.[groupIndex] ?? ''

  // Validate ID length if required
  if (requiredLength !== undefined && id.length !== requiredLength) {
    return ''
  }

  return id
}

/**
 * Extracts YouTube video ID from various URL formats
 * @param url YouTube URL in any format
 * @returns Video ID if valid, empty string otherwise
 */
function getYouTubeVideoId(url: string): string {
  return getVideoId(
    url,
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/,
    2,
    11,
  )
}

/**
 * Extracts TikTok video ID from URL
 * @param url TikTok URL
 * @returns Video ID if valid, empty string otherwise
 */
function getTikTokVideoId(url: string): string {
  return getVideoId(url, /^.*tiktok\.com\/.*\/video\/(\d+)/)
}

/**
 * Extracts Instagram post ID from URL
 * @param url Instagram URL
 * @returns Post ID if valid, empty string otherwise
 */
function getInstagramPostId(url: string): string {
  return getVideoId(url, /^.*instagram\.com\/p\/([a-zA-Z0-9_-]+)/)
}

/**
 * Determines the video platform from the URL
 * @param url Video URL
 * @returns Platform name ('youtube', 'tiktok', 'instagram', or 'unknown')
 */

function getVideoPlatform(
  url: string,
): 'youtube' | 'tiktok' | 'instagram' | 'unknown' {
  if (!url) return 'unknown'

  const platformMap: Record<string, 'youtube' | 'tiktok' | 'instagram'> = {
    'youtube.com': 'youtube',
    'youtu.be': 'youtube',
    'tiktok.com': 'tiktok',
    'instagram.com': 'instagram',
  }

  const domain = Object.keys(platformMap).find((key) => url.includes(key))
  return domain ? platformMap[domain] : 'unknown'
}

interface VideoPlayerProps {
  /** URL of the video to embed */
  url: string
  /** Optional CSS class name for additional styling */
  className?: string
}

/**
 * VideoPlayer component for embedding videos from various platforms
 * Supports YouTube, TikTok, and Instagram with fallbacks for unsupported platforms
 */
export function VideoPlayer({ url, className = '' }: VideoPlayerProps) {
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Reset error state when URL changes
    setError(null)
    setLoading(true)

    // Validate URL
    if (!url) {
      setError('No video URL provided')
      setLoading(false)
      return
    }

    // Check if URL is from a supported platform
    const platform = getVideoPlatform(url)
    if (platform === 'unknown') {
      setError(
        'Unsupported video platform. Please use YouTube, TikTok, or Instagram.',
      )
      setLoading(false)
    } else {
      setLoading(false)
    }
  }, [url])

  // Handle loading state
  if (loading) {
    return (
      <div
        className={`flex aspect-video w-full items-center justify-center rounded-xl border bg-gray-100 dark:bg-gray-800 ${className}`}
      >
        <div className="animate-pulse text-gray-500">Loading video...</div>
      </div>
    )
  }

  // Handle error state
  if (error) {
    return (
      <div
        className={`flex aspect-video w-full flex-col items-center justify-center rounded-xl border border-dashed bg-gray-100 shadow-inner dark:border-gray-700 dark:bg-gray-800 ${className}`}
      >
        <VideoOff
          className="mb-3 h-12 w-12 text-gray-400 dark:text-gray-500"
          aria-hidden="true"
        />
        <span className="px-4 text-center font-medium text-gray-500 dark:text-gray-400">
          {error}
        </span>
        <button
          type="button"
          onClick={() => setError(null)}
          className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-hover"
        >
          Retry
        </button>
      </div>
    )
  }

  // Determine which platform to use
  const platform = getVideoPlatform(url)

  // YouTube embed
  if (platform === 'youtube') {
    const videoId = getYouTubeVideoId(url)

    if (!videoId) {
      return (
        <div
          className={`flex aspect-video w-full flex-col items-center justify-center rounded-xl border border-dashed bg-gray-100 shadow-inner dark:border-gray-700 dark:bg-gray-800 ${className}`}
        >
          <VideoOff
            className="mb-3 h-12 w-12 text-gray-400 dark:text-gray-500"
            aria-hidden="true"
          />
          <span className="px-4 text-center font-medium text-gray-500 dark:text-gray-400">
            Invalid YouTube URL
          </span>
          <button
            type="button"
            onClick={() => setError(null)}
            className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-hover"
          >
            Retry
          </button>
        </div>
      )
    }

    return (
      <div
        className={`aspect-video w-full overflow-hidden rounded-xl border bg-black shadow-lg dark:border-gray-700 ${className}`}
      >
        <iframe
          width="100%"
          height="100%"
          src={`https://www.youtube.com/embed/${videoId}`}
          title="YouTube video player"
          style={{ border: 'none' }}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        />
      </div>
    )
  }

  // TikTok embed (fallback to link for now as iframe embedding is complex)
  if (platform === 'tiktok') {
    return (
      <div
        className={`flex aspect-video w-full flex-col items-center justify-center rounded-xl border bg-gray-100 shadow-lg dark:border-gray-700 dark:bg-gray-800 ${className}`}
      >
        <p className="mb-4 text-center text-gray-700 dark:text-gray-300">
          TikTok videos can be viewed directly on TikTok
        </p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-hover"
        >
          Open TikTok Video
        </a>
      </div>
    )
  }

  // Instagram embed (fallback to link for now)
  if (platform === 'instagram') {
    return (
      <div
        className={`flex aspect-video w-full flex-col items-center justify-center rounded-xl border bg-gray-100 shadow-lg dark:border-gray-700 dark:bg-gray-800 ${className}`}
      >
        <p className="mb-4 text-center text-gray-700 dark:text-gray-300">
          Instagram posts can be viewed directly on Instagram
        </p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-hover"
        >
          Open Instagram Post
        </a>
      </div>
    )
  }

  // Fallback for any other case
  return (
    <div
      className={`flex aspect-video w-full flex-col items-center justify-center rounded-xl border border-dashed bg-gray-100 shadow-inner dark:border-gray-700 dark:bg-gray-800 ${className}`}
    >
      <VideoOff
        className="mb-3 h-12 w-12 text-gray-400 dark:text-gray-500"
        aria-hidden="true"
      />
      <span className="px-4 text-center font-medium text-gray-500 dark:text-gray-400">
        Video Unavailable
      </span>
      <button
        type="button"
        onClick={() => setError(null)}
        className="rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-hover"
      >
        Retry
      </button>
    </div>
  )
}
