'use client'

import { getSupabaseCredentials } from '@/libs/supabase/credentials'
import type {
  AuthContextType,
  EnhancedAuthResponse,
  UserProfile,
  UserRole,
} from '@/shared/types/auth'
import { createBrowserClient } from '@supabase/ssr'
import type { Session, User } from '@supabase/supabase-js'
import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'
import { getAuthErrorMessage, signInSchema, signUpSchema } from './validation'

/**
 * Secure redirect URL generation
 * Prevents open redirect vulnerabilities
 */
function getSecureRedirectUrl(): string {
  if (typeof window === 'undefined') {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL
    if (!baseUrl) {
      throw new Error('NEXT_PUBLIC_SITE_URL environment variable is required')
    }
    return `${baseUrl}/auth/callback`
  }
  return `${window.location.origin}/auth/callback`
}

/**
 * Main authentication context
 * Single source of truth for all authentication state
 */
export const AuthContext = createContext<AuthContextType | undefined>(undefined)

/**
 * Authentication provider component
 * Manages all authentication state and methods
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)

  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

  /**
   * Fetches the user's profile from the database
   * Combines data from auth.users (email) + public.profiles (username, role, etc.)
   * @param userId The user's ID
   * @returns The user's profile or null if not found
   */
  const fetchUserProfile = useCallback(
    async (userId: string): Promise<UserProfile | null> => {
      try {
        // Fetch from profiles table (our custom data)
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()

        if (profileError) {
          console.error('Error fetching user profile:', profileError)
          return null
        }

        // Get auth user data (email, etc.)
        const {
          data: { user: authUser },
        } = await supabase.auth.getUser()

        if (!authUser) {
          console.error('No authenticated user found')
          return null
        }

        // Combine both sources of data
        const combinedProfile: UserProfile = {
          ...profileData,
          email: authUser.email || '',
          // Ensure we have the required fields with defaults
          username: profileData.username || '',
          role: profileData.role || 'athlete',
          total_points: profileData.total_points || 0,
          created_at: profileData.created_at || new Date().toISOString(),
          updated_at: profileData.updated_at || new Date().toISOString(),
        }

        return combinedProfile
      } catch (error) {
        console.error('Unexpected error fetching user profile:', error)
        return null
      }
    },
    [supabase],
  )

  /**
   * Refreshes the user's profile data
   */
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (!user) return

    try {
      const userProfile = await fetchUserProfile(user.id)
      if (userProfile) {
        setProfile(userProfile)
        setUserRole(userProfile.role)
      }
    } catch (error) {
      console.error('Error refreshing profile:', error)
    }
  }, [user, fetchUserProfile])

  /**
   * Updates the user's profile in the database
   * @param data The profile data to update
   * @returns Success or error response
   */
  const updateProfile = useCallback(
    async (data: Partial<UserProfile>): Promise<EnhancedAuthResponse> => {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }

      try {
        const { error } = await supabase
          .from('profiles')
          .update(data)
          .eq('id', user.id)

        if (error) throw error

        // Refresh the profile after update
        await refreshProfile()

        return { success: true }
      } catch (error) {
        console.error('Error updating profile:', error)
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }
    },
    [user, supabase, refreshProfile],
  )

  // Initialize auth state when component mounts
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true)

      try {
        // Get the current session
        const {
          data: { session },
        } = await supabase.auth.getSession()
        setUser(session?.user || null)

        // If user is authenticated, fetch their profile
        if (session?.user) {
          const userProfile = await fetchUserProfile(session.user.id)
          setProfile(userProfile)
          setUserRole(userProfile?.role || null)
        } else {
          setProfile(null)
          setUserRole(null)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }

      // Set up auth state change listener
      const { data: authListener } = supabase.auth.onAuthStateChange(
        async (event: string, session: Session | null) => {
          setUser(session?.user || null)

          if (session?.user) {
            const userProfile = await fetchUserProfile(session.user.id)
            setProfile(userProfile)
            setUserRole(userProfile?.role || null)
          } else {
            setProfile(null)
            setUserRole(null)
          }
        },
      )

      // Clean up listener on unmount
      return () => {
        authListener.subscription.unsubscribe()
      }
    }

    initializeAuth()
  }, [supabase.auth, fetchUserProfile])

  /**
   * Signs the user out
   */
  const signOut = async (): Promise<void> => {
    await supabase.auth.signOut()
  }

  /**
   * Signs in with a social provider
   * @param provider The social provider to use
   * @returns Success or error response
   */
  const signInWithSocial = async (
    provider: 'google' | 'facebook' | 'apple',
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: getSecureRedirectUrl(),
        },
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error(`${provider} auth error:`, error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Signs in with email and password
   * @param email The user's email
   * @param password The user's password
   * @returns Success or error response
   */
  const signInWithEmail = async (
    email: string,
    password: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      // Input validation
      const validation = signInSchema.safeParse({ email, password })
      if (!validation.success) {
        const firstError = validation.error.errors[0]
        return {
          success: false,
          error: firstError.message,
          errorType:
            firstError.path[0] === 'email'
              ? 'INVALID_EMAIL'
              : 'INVALID_CREDENTIALS',
        }
      }

      const { error } = await supabase.auth.signInWithPassword({
        email: validation.data.email,
        password: validation.data.password,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Email sign in error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Signs up with email and password
   * @param email The user's email
   * @param password The user's password
   * @returns Success or error response
   */
  const signUpWithEmail = async (
    email: string,
    password: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      // Input validation
      const validation = signUpSchema.safeParse({ email, password })
      if (!validation.success) {
        const firstError = validation.error.errors[0]
        return {
          success: false,
          error: firstError.message,
          errorType:
            firstError.path[0] === 'email' ? 'INVALID_EMAIL' : 'WEAK_PASSWORD',
        }
      }

      const { error, data } = await supabase.auth.signUp({
        email: validation.data.email,
        password: validation.data.password,
        options: {
          emailRedirectTo: getSecureRedirectUrl(),
        },
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      // Check if email confirmation is required
      const needsConfirmation =
        !data.session && data.user && !data.user.email_confirmed_at

      if (needsConfirmation) {
        return {
          success: true,
          needsConfirmation: true,
          data: {
            message:
              'Please check your email and click the confirmation link to complete your registration.',
          },
        }
      }

      return { success: true }
    } catch (error) {
      console.error('Email sign up error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Sends a password reset email
   * @param email The user's email
   * @returns Success or error response
   */
  const resetPassword = async (
    email: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${getSecureRedirectUrl().replace('/callback', '/update-password')}`,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  /**
   * Updates the user's password
   * @param newPassword The new password
   * @returns Success or error response
   */
  const updatePassword = async (
    newPassword: string,
  ): Promise<EnhancedAuthResponse> => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (error) {
        const { message, type } = getAuthErrorMessage(error)
        return { success: false, error: message, errorType: type }
      }

      return { success: true }
    } catch (error) {
      console.error('Update password error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return { success: false, error: message, errorType: type }
    }
  }

  // Provide the auth context value
  const value: AuthContextType = {
    // Authentication state
    user,
    profile,
    userRole,
    loading,
    isAuthenticated: !!user,

    // Core authentication methods
    signInWithEmail,
    signUpWithEmail,
    signInWithSocial,
    signOut,

    // Password management
    resetPassword,
    updatePassword,

    // Profile management
    updateProfile,
    refreshProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
