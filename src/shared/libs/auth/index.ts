/**
 * @fileoverview Unified Authentication System
 * @version 2.0.0
 * @since 2024
 *
 * Single source of truth for all authentication functionality
 */

// Main authentication context and provider
export { AuthContext, AuthProvider } from './context'

// Validation and error handling
export {
  getAuthErrorMessage,
  signInSchema,
  signUpSchema,
  passwordResetSchema,
  updatePasswordSchema,
  emailSchema,
  passwordSchema,
  isValidEmail,
  isValidPassword,
} from './validation'

// Re-export types for convenience
export type {
  AuthContextType,
  UserProfile,
  EnhancedAuthResponse,
  AuthErrorType,
  UserRole,
} from '@/shared/types/auth'
