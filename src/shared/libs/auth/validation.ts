import type { AuthErrorType } from '@/shared/types/auth'
import { z } from 'zod'

/**
 * Email validation schema
 */
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(254, 'Email is too long')

/**
 * Password validation schema with security requirements
 */
export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password is too long')
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  )

/**
 * Sign up form validation schema
 */
export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
})

/**
 * Sign in form validation schema
 */
export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
})

/**
 * Password reset schema
 */
export const passwordResetSchema = z.object({
  email: emailSchema,
})

/**
 * Update password schema
 */
export const updatePasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine(
    (data: { password: string; confirmPassword: string }) =>
      data.password === data.confirmPassword,
    {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    },
  )

/**
 * Utility function to validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const result = emailSchema.safeParse(email)
  return result.success
}

/**
 * Utility function to validate password strength
 */
export const isValidPassword = (password: string): boolean => {
  const result = passwordSchema.safeParse(password)
  return result.success
}

/**
 * Unified authentication error handling
 * Replaces multiple error mapping functions throughout the codebase
 */
export function getAuthErrorMessage(error: { message?: string } | unknown): {
  message: string
  type: AuthErrorType
} {
  const errorMessage =
    (error as { message?: string })?.message?.toLowerCase() || ''

  if (
    errorMessage.includes('email already registered') ||
    errorMessage.includes('user already registered')
  ) {
    return {
      message:
        'An account with this email already exists. Please sign in instead.',
      type: 'EMAIL_ALREADY_REGISTERED',
    }
  }

  if (
    errorMessage.includes('invalid login credentials') ||
    errorMessage.includes('invalid email or password')
  ) {
    return {
      message:
        'Invalid email or password. Please check your credentials and try again.',
      type: 'INVALID_CREDENTIALS',
    }
  }

  if (errorMessage.includes('email not confirmed')) {
    return {
      message:
        'Please check your email and click the confirmation link before signing in.',
      type: 'EMAIL_NOT_CONFIRMED',
    }
  }

  if (
    errorMessage.includes('too many requests') ||
    errorMessage.includes('rate limit')
  ) {
    return {
      message: 'Too many attempts. Please wait a moment before trying again.',
      type: 'RATE_LIMITED',
    }
  }

  return {
    message:
      (error as { message?: string })?.message ||
      'An unexpected error occurred. Please try again.',
    type: 'UNKNOWN_ERROR',
  }
}
