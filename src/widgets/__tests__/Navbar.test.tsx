/// <reference types="vitest" />

import { renderWithProviders } from '@/tests/test-utils'
import { screen } from '@testing-library/react'
import { describe, expect, it } from 'vitest'
import { Navbar } from '../Navbar'

const baseItems = [
  { href: '/', title: 'Home' },
  { href: '/profile', title: 'Profile' },
  { href: '/exercises', title: 'Exercises' },
  { href: '/rankings', title: 'Rankings' },
]

const renderNavbar = (role?: 'admin' | 'grandmaster' | 'athlete' | null) =>
  renderWithProviders(<Navbar items={baseItems} role={role} />, {
    authMock: {
      user: role
        ? {
            id: 'test-id',
            app_metadata: {},
            user_metadata: {},
            aud: 'authenticated',
            created_at: '2023-01-01T00:00:00.000Z',
            role,
          }
        : null,
    },
  })

describe('Navbar RBAC links', () => {
  it('shows Admin and Submissions for admin', () => {
    renderNavbar('admin')
    expect(screen.getByText('Admin')).toBeInTheDocument()
    expect(screen.getByText('Submissions')).toBeInTheDocument()
  })

  it('shows Submissions for grandmaster, not Admin', () => {
    renderNavbar('grandmaster')
    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
    expect(screen.getByText('Submissions')).toBeInTheDocument()
  })

  it('shows neither Admin nor Submissions for athlete', () => {
    renderNavbar('athlete')
    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
    expect(screen.queryByText('Submissions')).not.toBeInTheDocument()
  })

  it('shows neither Admin nor Submissions if no role', () => {
    renderNavbar()
    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
    expect(screen.queryByText('Submissions')).not.toBeInTheDocument()
  })
})
