'use client'

import {
  BicepsFlexed,
  HomeIcon,
  type LucideIcon,
  TrophyIcon,
  UploadIcon,
  UserCircle2Icon,
} from 'lucide-react'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

import { AuthDialog } from '@/features/auth/components'
import { useAuth } from '@/features/auth/hooks/use-auth'
import { cn } from '@/libs/utils'
import { MobileBottomNavItem } from './MobileBottomNavItem'

interface NavItem {
  href: string
  icon: LucideIcon
  label: string
  authRequired?: boolean
  profileLink?: boolean
}

const NAV_ITEMS: NavItem[] = [
  { href: '/', icon: HomeIcon, label: 'Home' },
  { href: '/exercises', icon: BicepsFlexed, label: 'Exercises' },
  { href: '/rankings', icon: TrophyIcon, label: 'Rankings' },
  { href: '/submit', icon: UploadIcon, label: 'Submit', authRequired: true },
  {
    href: '/profile',
    icon: UserCircle2Icon,
    label: 'Profile',
    profileLink: true,
  },
]

export function MobileBottomNav() {
  const pathname = usePathname()
  const { user, profile, loading } = useAuth()
  const [authDialogOpen, setAuthDialogOpen] = useState(false)

  if (loading) {
    return (
      <div className="fixed bottom-0 left-0 z-30 w-full h-16 bg-card border-t border-border md:hidden animate-pulse" />
    )
  }

  const handleProfileClick = () => {
    if (!user) {
      setAuthDialogOpen(true)
    }
    // Navigation will be handled by Link component if user exists
  }

  const getHref = (item: NavItem): string => {
    if (item.profileLink) {
      // Use profile.displayName if available, otherwise fallback to /profile
      // The /profile page should handle displaying the correct user or prompting for creation
      return user && profile?.username
        ? `/profile/${profile.username}`
        : user // if user exists but no displayName, link to generic /profile
          ? '/profile'
          : '/login' // if no user, link to login
    }
    return item.href
  }

  return (
    <>
      <nav
        className={cn(
          'fixed bottom-0 left-0 z-30 w-full border-t border-border bg-card md:hidden',
          'transition-transform duration-300 ease-in-out',
        )}
      >
        <div className="container mx-auto px-0 sm:px-0 lg:px-0">
          <div className="flex h-16 items-center justify-around">
            {NAV_ITEMS.map((item) => {
              const isActive = item.profileLink
                ? pathname.startsWith('/profile') ||
                  (pathname === '/login' && !user)
                : pathname === item.href
              const href = getHref(item)

              if (item.authRequired && !user && !item.profileLink) {
                // Don't render auth-required items if not logged in, unless it's the profile link handled separately
                return null
              }

              return (
                <MobileBottomNavItem
                  key={item.label}
                  href={href}
                  icon={item.icon}
                  label={item.label}
                  isActive={isActive}
                  onClick={
                    item.profileLink && !user ? handleProfileClick : undefined
                  }
                />
              )
            })}
          </div>
        </div>
      </nav>
      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        initialMode="signin"
      />
    </>
  )
}
