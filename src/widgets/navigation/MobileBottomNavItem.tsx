'use client'

import { cn } from '@/libs/utils'
import type { LucideIcon } from 'lucide-react'
import Link from 'next/link'

export interface MobileBottomNavItemProps {
  href: string
  icon: LucideIcon
  label: string
  isActive: boolean
  onClick?: () => void
}

export function MobileBottomNavItem({
  href,
  icon: Icon,
  label,
  isActive,
  onClick,
}: MobileBottomNavItemProps) {
  const commonClasses = 'flex flex-col items-center justify-center h-14 w-1/5'
  const iconWrapperBaseClasses =
    'flex items-center justify-center w-10 h-10 rounded-full transition-colors duration-400 ease-in-out'
  const iconColor = isActive
    ? 'text-primary-foreground'
    : 'text-muted-foreground group-hover:text-foreground'

  const content = (
    <>
      <div
        className={cn(
          iconWrapperBaseClasses,
          isActive ? 'bg-primary' : 'group-hover:bg-muted/50',
        )}
      >
        <Icon
          className={cn('h-6 w-6', iconColor)}
          strokeWidth={isActive ? 2.5 : 2}
        />
      </div>
      {/* Text label can be added here if design changes */}
      {/* <span className={cn('text-xs mt-1', isActive ? 'text-primary' : 'text-muted-foreground')}>{label}</span> */}
    </>
  )

  if (onClick) {
    return (
      <button
        type="button"
        onClick={onClick}
        className={cn(commonClasses, 'group')}
        aria-label={label}
      >
        {content}
      </button>
    )
  }

  return (
    <Link href={href} className={cn(commonClasses, 'group')} aria-label={label}>
      {content}
    </Link>
  )
}
