import { Button } from '@/components/ui/button'
import { useAuth } from '@/features/auth/hooks/use-auth'
import Link from 'next/link'
import type { NavItemWithIcon } from '../Navbar'

interface MobileMenuProps {
  navigationItems: NavItemWithIcon[]
  onAuthClick: (mode: 'signin' | 'signup') => void
  onClose: () => void
}

export function MobileMenu({
  navigationItems,
  onAuthClick,
  onClose,
}: MobileMenuProps) {
  const { user, signOut, isAuthenticated } = useAuth()
  return (
    <div className="md:hidden">
      <div className="space-y-1 px-2 pt-2 pb-3">
        {navigationItems.map((item) => {
          const Icon = item.icon
          // Special handling for profile link when not signed in
          if (item.href === '/profile' && !user) {
            return (
              <button
                key={item.href}
                type="button"
                className="block w-full rounded-md px-3 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100 text-right"
                onClick={() => {
                  onAuthClick('signin')
                  onClose()
                }}
              >
                {item.title}
                {Icon && <Icon className="ml-3 h-5 w-5 inline-block" />}
              </button>
            )
          }

          // Regular links for all other items or profile when signed in
          return (
            <Link
              key={item.href}
              href={item.href}
              className="flex flex-row-reverse items-center justify-end rounded-md px-3 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
              onClick={onClose}
            >
              {item.title}
              {Icon && <Icon className="ml-3 h-5 w-5" />}
            </Link>
          )
        })}

        {user ? (
          <div className="mt-4 flex flex-col space-y-2 border-t border-gray-200 pt-4 dark:border-gray-700">
            <Link href="/account" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-end">
                My Account
              </Button>
            </Link>
            <Link href="/submit" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-end">
                Submit Performance
              </Button>
            </Link>
            <Link href="/account/submissions" onClick={onClose}>
              <Button variant="ghost" className="w-full justify-end">
                My Submissions
              </Button>
            </Link>
            <Button
              variant="destructive"
              className="w-full justify-end"
              onClick={async () => {
                await signOut()
                onClose()
              }}
            >
              Log Out
            </Button>
          </div>
        ) : (
          <div className="mt-4 flex flex-col space-y-2 border-t border-gray-200 pt-4 dark:border-gray-700">
            <Button
              variant="default"
              className="w-full justify-end"
              onClick={() => {
                onAuthClick('signin')
                onClose()
              }}
            >
              Sign In
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
