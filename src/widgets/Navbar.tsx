'use client'

import {
  BicepsFlexed,
  HomeIcon,
  type LucideIcon,
  Menu,
  SettingsIcon,
  TrophyIcon,
  UploadIcon,
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import type * as React from 'react'
import { useEffect, useRef, useState } from 'react'

import { Button } from '@/components/ui/button'
import { AuthDialog, UserMenu } from '@/features/auth/components'
import { useAuth } from '@/features/auth/hooks/use-auth'
import { ThemeToggle } from '@/features/theme/ThemeToggle'
import { cn } from '@/libs/utils'
import { MobileMenu } from '@/widgets/navigation'
import {
  type NavItem as ConstantNavItem,
  DEFAULT_NAVIGATION_ITEMS,
} from '@/widgets/navigation/constants'

const iconLookup: { [key: string]: LucideIcon } = {
  HomeIcon,
  BicepsFlexed,
  TrophyIcon,
  UploadIcon,
  SettingsIcon,
}

export type NavItemWithIcon = Omit<ConstantNavItem, 'iconName'> & {
  icon: LucideIcon
}

export interface NavbarProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'role'> {
  items?: ConstantNavItem[]
  userRole?: 'admin' | 'grandmaster' | 'athlete' | null
}

export function Navbar({
  className,
  items = DEFAULT_NAVIGATION_ITEMS,
  userRole,
  ...props
}: NavbarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const [authDialogMode, setAuthDialogMode] = useState<'signin' | 'signup'>(
    'signin',
  )
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const navRef = useRef<HTMLElement>(null)

  const { user, loading } = useAuth()

  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleScroll = () => {
      const currentScrollY = window.scrollY
      const navbarHeight = navRef.current?.offsetHeight || 0

      if (isOpen) {
        setIsVisible(true)
        setLastScrollY(currentScrollY)
        return
      }

      if (currentScrollY <= 0) {
        setIsVisible(true)
      } else if (
        currentScrollY > lastScrollY &&
        currentScrollY > navbarHeight
      ) {
        setIsVisible(false)
      } else if (currentScrollY < lastScrollY) {
        setIsVisible(true)
      }
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [lastScrollY, isOpen])

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthDialogMode(mode)
    setAuthDialogOpen(true)
  }

  const displayItems: NavItemWithIcon[] = items.map((item) => ({
    ...item,
    icon: iconLookup[item.iconName] || Menu,
  }))

  return (
    <>
      <nav
        ref={navRef}
        className={cn(
          'fixed top-0 left-0 z-40 w-full border-b border-border bg-card',
          'transition-transform duration-500 ease-in-out',
          !isVisible && '-translate-y-full',
          className,
        )}
        {...props}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 md:h-24 items-center justify-between">
            {/* Logo and brand */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/logo.webp"
                  alt="Armwrestling Power Arena Logo"
                  width={100}
                  height={100}
                  className="h-15 w-auto"
                />
                <span className="text-base sm:text-2xl font-bold text-primary">
                  Armwrestling Power Arena
                </span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-center space-x-4">
                {displayItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="flex items-center rounded-md px-3 py-2 text-md font-medium transition-colors hover:bg-background"
                    >
                      <Icon className="mr-2 h-5 w-5" />
                      {item.title}
                    </Link>
                  )
                })}
              </div>
            </div>

            {/* Right side items */}
            <div className="flex items-center space-x-4">
              {loading ? (
                <div className="h-12 w-12 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700" />
              ) : user ? (
                <UserMenu />
              ) : (
                <div className="hidden items-center md:flex">
                  <Button
                    variant="default"
                    size="default"
                    onClick={() => handleAuthClick('signin')}
                  >
                    Sign In
                  </Button>
                </div>
              )}

              <ThemeToggle />

              {/* Mobile menu button - Controls the slide-out MobileMenu */}
              <div className="hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMenu}
                  aria-label="Toggle menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        {isOpen && (
          <MobileMenu
            navigationItems={displayItems}
            onAuthClick={handleAuthClick}
            onClose={() => setIsOpen(false)}
          />
        )}
      </nav>

      {/* Auth Dialog */}
      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        initialMode={authDialogMode}
      />
    </>
  )
}
