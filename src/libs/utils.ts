import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Merges Tailwind classes, but ensures that more specific pt-/pb- overrides
 * are preserved over generic p-* or py-*.
 * Example: cn('pt-2 pb-4 p-3 px-5') => 'pt-2 pb-4 px-5'
 */
export function cn(...inputs: ClassValue[]) {
  // 1. Combine all inputs into a single string using clsx
  const classString = clsx(inputs)
  const initialParts = classString.split(' ')

  // 2. Check for specific pt-/pb- in the combined string
  const hasPt = initialParts.some((c) => /^pt-/.test(c))
  const hasPb = initialParts.some((c) => /^pb-/.test(c))

  // 3. Filter the initial parts based on pt-/pb- presence
  const filteredParts = initialParts.filter((c) => {
    if (/^p-(?![xy])/.test(c)) {
      // Check for generic p-*
      if (hasPt || hasPb) return false // Remove if pt or pb exists
    }
    if (/^py-/.test(c)) {
      // Check for py-*
      if (hasPt || hasPb) return false // Remove if pt or pb exists
    }
    return true // Keep others
  })

  // 4. Pass the filtered parts array to twMerge
  // twMerge can accept an array of strings
  return twMerge(filteredParts)
}
