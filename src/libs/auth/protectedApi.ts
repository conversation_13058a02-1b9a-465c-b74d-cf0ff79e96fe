import {
  hasAllPermissions,
  hasAnyPermission,
  hasPermission,
} from '@/libs/permissions'
import type { Permission, UserRole } from '@/libs/permissions/types'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { cookies } from 'next/headers'
import { type NextRequest, NextResponse } from 'next/server'

// Use unified UserProfile from shared types
import type { UserProfile } from '@/shared/types/auth'

/**
 * Get the current authenticated user with their role for API routes
 */
export async function getApiUser(
  request: NextRequest,
): Promise<UserProfile | null> {
  // Create a supabase client for the API route with proper cookie handling
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // Get the current user's session
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return null
  }

  // Get the user's profile with role
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('auth_user_id', user.id)
    .single<UserProfile>()

  return profile || null
}

/**
 * Create a protected API route handler that checks permissions before executing handler function
 */
type ApiHandler = (
  req: NextRequest,
  user: UserProfile,
  params?: { [key: string]: string | string[] },
) => Promise<NextResponse> | NextResponse

/**
 * Options for creating a protected API route handler
 */
interface ProtectedApiOptions {
  // Single permission required to access the route
  permission?: Permission
  // Multiple permissions with check mode
  permissions?: Permission[]
  // How to check multiple permissions
  permissionMode?: 'all' | 'any'
  // Specific role required to access the route
  role?: UserRole
  // Multiple roles (any match grants access)
  roles?: UserRole[]
}

/**
 * Create a protected API route handler with permission checking
 */
export function createProtectedApiRoute(
  handler: ApiHandler,
  options: ProtectedApiOptions = {},
) {
  const {
    permission,
    permissions,
    permissionMode = 'all',
    role,
    roles,
  } = options

  return async function protectedApiHandler(
    request: NextRequest,
    { params }: { params?: { [key: string]: string | string[] } } = {},
  ): Promise<NextResponse> {
    // Get the user
    const user = await getApiUser(request)

    // Unauthorized if no user
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Perform authorization checks
    let authorized = false

    // Check permission if specified
    if (permission) {
      authorized = hasPermission(user, permission)
    }

    // Check multiple permissions if specified
    if (permissions && permissions.length > 0) {
      authorized =
        permissionMode === 'all'
          ? hasAllPermissions(user, permissions)
          : hasAnyPermission(user, permissions)
    }

    // Check role if specified
    if (role && !authorized) {
      authorized = user.role === role
    }

    // Check multiple roles if specified
    if (roles && roles.length > 0 && !authorized) {
      authorized = roles.includes(user.role)
    }

    // If no permission or role was checked, deny access
    if (!permission && !permissions?.length && !role && !roles?.length) {
      authorized = false
    }

    // Return 403 if unauthorized
    if (!authorized) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 },
      )
    }

    // User is authorized, execute the handler
    return handler(request, user, params)
  }
}

/**
 * Create an API route that requires admin role
 */
export function createAdminApiRoute(handler: ApiHandler) {
  return createProtectedApiRoute(handler, { role: 'admin' })
}

/**
 * Create an API route that requires grandmaster or admin role
 */
export function createStaffApiRoute(handler: ApiHandler) {
  return createProtectedApiRoute(handler, { roles: ['grandmaster', 'admin'] })
}
