import {
  hasAllPermissions,
  hasAnyPermission,
  hasPermission,
} from '@/libs/permissions'
import type { Permission, UserRole } from '@/libs/permissions/types'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

// Use unified UserProfile from shared types
import type { UserProfile } from '@/shared/types/auth'

/**
 * Get the current authenticated user with their role and profile information
 */
export async function getCurrentUser(): Promise<UserProfile | null> {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // Get the current user's session
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return null
  }

  // Get the user's profile with role
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('auth_user_id', user.id)
    .single<UserProfile>()

  return profile || null
}

/**
 * Check if the current user has specific permission, redirect if not
 * @param permission Permission to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requirePermission(
  permission: Permission,
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasPermission(user, permission)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has any of the specified permissions, redirect if not
 * @param permissions List of permissions to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requireAnyPermission(
  permissions: Permission[],
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasAnyPermission(user, permissions)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has all specified permissions, redirect if not
 * @param permissions List of permissions to check
 * @param redirectUrl URL to redirect to if permission denied
 */
export async function requireAllPermissions(
  permissions: Permission[],
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !hasAllPermissions(user, permissions)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has a specific role, redirect if not
 * @param role Role to check
 * @param redirectUrl URL to redirect to if role check fails
 */
export async function requireRole(
  role: UserRole,
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || user.role !== role) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if the current user has one of the specified roles, redirect if not
 * @param roles Roles to check
 * @param redirectUrl URL to redirect to if role check fails
 */
export async function requireAnyRole(
  roles: UserRole[],
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  const user = await getCurrentUser()

  if (!user || !roles.includes(user.role)) {
    redirect(redirectUrl)
  }

  return user
}

/**
 * Check if current user is an administrator, redirect if not
 * @param redirectUrl URL to redirect to if check fails
 */
export async function requireAdmin(
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  return requireRole('admin', redirectUrl)
}

/**
 * Check if current user is a grandmaster or admin, redirect if not
 * @param redirectUrl URL to redirect to if check fails
 */
export async function requireStaffRole(
  redirectUrl = '/auth/sign-in',
): Promise<UserProfile> {
  return requireAnyRole(['grandmaster', 'admin'], redirectUrl)
}
