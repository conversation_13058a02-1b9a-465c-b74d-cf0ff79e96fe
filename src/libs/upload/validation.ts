/**
 * Secure File Upload Validation
 * Implements comprehensive security measures for file uploads
 * CVSS 5.4 Insecure File Upload Fix
 */

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png', 
  'image/webp',
  'image/gif'
] as const;

export const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
export const MAX_DIMENSION = 2048; // 2048px

/**
 * Client-side file validation (first line of defense)
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: 'File size must be less than 2MB' };
  }

  // Check MIME type
  if (!ALLOWED_IMAGE_TYPES.includes(file.type as any)) {
    return { valid: false, error: 'Only JPEG, PNG, WebP, and GIF files are allowed' };
  }

  // Check file extension matches MIME type
  const extension = file.name.toLowerCase().split('.').pop();
  const expectedExtensions = {
    'image/jpeg': ['jpg', 'jpeg'],
    'image/png': ['png'],
    'image/webp': ['webp'],
    'image/gif': ['gif']
  };

  const mimeType = file.type as keyof typeof expectedExtensions;
  if (!extension || !expectedExtensions[mimeType]?.includes(extension)) {
    return { valid: false, error: 'File extension does not match file type' };
  }

  return { valid: true };
}

/**
 * Server-side magic byte validation (critical security check)
 * Validates actual file content regardless of MIME type or extension
 */
export async function validateImageBuffer(buffer: ArrayBuffer, originalName: string): Promise<{ valid: boolean; error?: string }> {
  const uint8Array = new Uint8Array(buffer);
  
  if (uint8Array.length < 4) {
    return { valid: false, error: 'File is too small to be a valid image' };
  }

  // JPEG magic bytes: FF D8 FF
  if (uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF) {
    const isJpegExtension = /\.(jpg|jpeg)$/i.test(originalName);
    if (!isJpegExtension) {
      return { valid: false, error: 'File appears to be JPEG but has wrong extension' };
    }
    return { valid: true };
  }

  // PNG magic bytes: 89 50 4E 47 0D 0A 1A 0A
  if (uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && 
      uint8Array[2] === 0x4E && uint8Array[3] === 0x47) {
    const isPngExtension = /\.png$/i.test(originalName);
    if (!isPngExtension) {
      return { valid: false, error: 'File appears to be PNG but has wrong extension' };
    }
    return { valid: true };
  }

  // WebP magic bytes: 52 49 46 46 (RIFF) + WebP signature
  if (uint8Array[0] === 0x52 && uint8Array[1] === 0x49 && 
      uint8Array[2] === 0x46 && uint8Array[3] === 0x46 && uint8Array.length >= 12) {
    // Check for WebP signature at bytes 8-11: 57 45 42 50 (WEBP)
    if (uint8Array[8] === 0x57 && uint8Array[9] === 0x45 && 
        uint8Array[10] === 0x42 && uint8Array[11] === 0x50) {
      const isWebpExtension = /\.webp$/i.test(originalName);
      if (!isWebpExtension) {
        return { valid: false, error: 'File appears to be WebP but has wrong extension' };
      }
      return { valid: true };
    }
  }

  // GIF magic bytes: 47 49 46 38 (GIF8)
  if (uint8Array[0] === 0x47 && uint8Array[1] === 0x49 && 
      uint8Array[2] === 0x46 && uint8Array[3] === 0x38) {
    const isGifExtension = /\.gif$/i.test(originalName);
    if (!isGifExtension) {
      return { valid: false, error: 'File appears to be GIF but has wrong extension' };
    }
    return { valid: true };
  }

  return { valid: false, error: 'File is not a valid image format' };
}

/**
 * Generate secure filename to prevent path traversal and conflicts
 */
export function generateSecureFilename(userId: string, originalName: string): string {
  const extension = originalName.toLowerCase().split('.').pop() || 'jpg';
  const timestamp = Date.now();
  const randomId = crypto.randomUUID();
  return `${userId}-${timestamp}-${randomId}.${extension}`;
}

/**
 * Sanitize filename to prevent security issues
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/\.+/g, '.') // Replace multiple dots with single dot
    .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
    .substring(0, 100); // Limit length
}
