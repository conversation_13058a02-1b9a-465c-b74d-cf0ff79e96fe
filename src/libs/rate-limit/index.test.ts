import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { authRateLimit, createRateLimit, submissionRateLimit } from './index'

// Mock the headers function
vi.mock('next/headers', () => ({
  headers: vi.fn()
}))

const mockHeaders = vi.mocked(await import('next/headers')).headers

describe('Rate Limiting', () => {
  beforeEach(async () => {
    // Clear any existing rate limit data
    vi.clearAllMocks()

    // Clear the rate limit store between tests
    const { __rateLimitStore } = await import('./index')
    __rateLimitStore.clear()

    // Mock headers with default IP
    mockHeaders.mockResolvedValue(new Headers({
      'x-forwarded-for': '127.0.0.1'
    }))
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createRateLimit', () => {
    it('should allow requests within limit', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000, // 1 minute
        maxRequests: 5
      })

      const result = await rateLimit()

      expect(result.success).toBe(true)
      expect(result.remaining).toBe(4)
      expect(result.resetTime).toBeGreaterThan(Date.now())
    })

    it('should block requests when limit exceeded', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000, // 1 minute
        maxRequests: 2
      })

      // First two requests should succeed
      const result1 = await rateLimit()
      expect(result1.success).toBe(true)
      expect(result1.remaining).toBe(1)

      const result2 = await rateLimit()
      expect(result2.success).toBe(true)
      expect(result2.remaining).toBe(0)

      // Third request should be blocked
      const result3 = await rateLimit()
      expect(result3.success).toBe(false)
      expect(result3.error).toContain('Rate limit exceeded')
      expect(result3.remaining).toBe(0)
    })

    it('should reset after window expires', async () => {
      const rateLimit = createRateLimit({
        windowMs: 100, // 100ms
        maxRequests: 1
      })

      // First request should succeed
      const result1 = await rateLimit()
      expect(result1.success).toBe(true)

      // Second request should be blocked
      const result2 = await rateLimit()
      expect(result2.success).toBe(false)

      // Wait for window to expire
      await new Promise(resolve => setTimeout(resolve, 150))

      // Third request should succeed after reset
      const result3 = await rateLimit()
      expect(result3.success).toBe(true)
    })

    it('should use custom key generator', async () => {
      mockHeaders.mockResolvedValue(new Headers({
        'x-user-id': 'user123',
        'x-forwarded-for': '127.0.0.1'
      }))

      const rateLimit = createRateLimit({
        windowMs: 60000,
        maxRequests: 2,
        keyGenerator: (headers) => headers.get('x-user-id') || 'anonymous'
      })

      const result1 = await rateLimit()
      expect(result1.success).toBe(true)
      expect(result1.remaining).toBe(1)

      const result2 = await rateLimit()
      expect(result2.success).toBe(true)
      expect(result2.remaining).toBe(0)

      // Third request should be blocked for this user
      const result3 = await rateLimit()
      expect(result3.success).toBe(false)
    })

    it('should handle different IP addresses separately', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000,
        maxRequests: 1
      })

      // First IP
      mockHeaders.mockResolvedValue(new Headers({
        'x-forwarded-for': '***********'
      }))
      const result1 = await rateLimit()
      expect(result1.success).toBe(true)

      // Second request from same IP should be blocked
      const result2 = await rateLimit()
      expect(result2.success).toBe(false)

      // Different IP should be allowed
      mockHeaders.mockResolvedValue(new Headers({
        'x-forwarded-for': '***********'
      }))
      const result3 = await rateLimit()
      expect(result3.success).toBe(true)
    })

    it('should extract IP from various headers', async () => {
      const rateLimit = createRateLimit({
        windowMs: 60000,
        maxRequests: 1
      })

      // Test x-real-ip header
      mockHeaders.mockResolvedValue(new Headers({
        'x-real-ip': '********'
      }))
      const result1 = await rateLimit()
      expect(result1.success).toBe(true)

      // Test cf-connecting-ip header (Cloudflare)
      mockHeaders.mockResolvedValue(new Headers({
        'cf-connecting-ip': '********'
      }))
      const result2 = await rateLimit()
      expect(result2.success).toBe(true)

      // Test x-client-ip header
      mockHeaders.mockResolvedValue(new Headers({
        'x-client-ip': '********'
      }))
      const result3 = await rateLimit()
      expect(result3.success).toBe(true)
    })
  })

  describe('Pre-configured rate limiters', () => {
    it('should have auth rate limiter with correct config', async () => {
      // Auth rate limiter should allow 5 requests per 15 minutes
      const results = []
      for (let i = 0; i < 6; i++) {
        results.push(await authRateLimit())
      }

      // First 5 should succeed
      for (let i = 0; i < 5; i++) {
        expect(results[i].success).toBe(true)
      }

      // 6th should fail
      expect(results[5].success).toBe(false)
      expect(results[5].error).toContain('Rate limit exceeded')
    })

    it('should have submission rate limiter with user-based keys', async () => {
      mockHeaders.mockResolvedValue(new Headers({
        'x-user-id': 'user123',
        'x-forwarded-for': '127.0.0.1'
      }))

      const result = await submissionRateLimit()
      expect(result.success).toBe(true)
      expect(result.remaining).toBe(9) // 10 max - 1 used = 9 remaining
    })
  })
})
