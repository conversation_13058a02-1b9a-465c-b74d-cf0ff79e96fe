import { headers } from 'next/headers'

/**
 * Rate limiting configuration interface
 */
interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests allowed in the window
  keyGenerator?: (headers: Headers) => string // Custom key generation function
  skipSuccessfulRequests?: boolean // Don't count successful requests
  skipFailedRequests?: boolean // Don't count failed requests
}

/**
 * Rate limit entry structure
 */
interface RateLimitEntry {
  count: number
  resetTime: number
  windowStart: number
}

/**
 * Rate limit result
 */
interface RateLimitResult {
  success: boolean
  error?: string
  remaining?: number
  resetTime?: number
}

/**
 * In-memory rate limit store
 * In production, consider using Redis or similar for distributed systems
 */
const rateLimitStore = new Map<string, RateLimitEntry>()

/**
 * Export rate limit store for testing purposes
 * @internal
 */
export const __rateLimitStore = rateLimitStore

/**
 * Clean up expired entries from the rate limit store
 */
function cleanupExpiredEntries(): void {
  const now = Date.now()
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}

/**
 * Get client IP address from headers
 */
function getClientIP(headers: Headers): string {
  // Check various headers for client IP
  const forwarded = headers.get('x-forwarded-for')
  const realIP = headers.get('x-real-ip')
  const cfConnectingIP = headers.get('cf-connecting-ip') // Cloudflare
  const xClientIP = headers.get('x-client-ip')

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim()
  }

  if (realIP) {
    return realIP
  }

  if (cfConnectingIP) {
    return cfConnectingIP
  }

  if (xClientIP) {
    return xClientIP
  }

  return 'unknown'
}

/**
 * Create a rate limiter function with the given configuration
 */
export function createRateLimit(config: RateLimitConfig) {
  return async (): Promise<RateLimitResult> => {
    const now = Date.now()
    const headersList = await headers()

    // Generate key for this request
    const key = config.keyGenerator
      ? config.keyGenerator(headersList)
      : getClientIP(headersList)

    // Clean up expired entries periodically
    if (Math.random() < 0.1) { // 10% chance to cleanup on each request
      cleanupExpiredEntries()
    }

    const windowStart = now - config.windowMs
    const resetTime = now + config.windowMs

    // Get or create entry for this key
    let entry = rateLimitStore.get(key)

    if (!entry || entry.resetTime < now) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime,
        windowStart: now
      }
      rateLimitStore.set(key, entry)

      return {
        success: true,
        remaining: config.maxRequests - 1,
        resetTime
      }
    }

    // Check if limit exceeded
    if (entry.count >= config.maxRequests) {
      const timeUntilReset = Math.ceil((entry.resetTime - now) / 1000)
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${timeUntilReset} seconds.`,
        remaining: 0,
        resetTime: entry.resetTime
      }
    }

    // Increment counter
    entry.count++
    rateLimitStore.set(key, entry)

    return {
      success: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime
    }
  }
}

/**
 * Pre-configured rate limiters for different use cases
 */

// Authentication actions (sign in, sign up, password reset)
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 attempts per 15 minutes
})

// Submission actions (video submissions)
export const submissionRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 10, // 10 submissions per hour
  keyGenerator: (headers) => {
    // Use user ID if available, otherwise fall back to IP
    const userId = headers.get('x-user-id')
    return userId || getClientIP(headers)
  }
})

// Profile update actions
export const profileRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // 10 updates per 5 minutes
  keyGenerator: (headers) => {
    const userId = headers.get('x-user-id')
    return userId || getClientIP(headers)
  }
})

// API endpoints
export const apiRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute
})

// Strict rate limiting for sensitive operations
export const strictRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 3, // 3 attempts per hour
})

/**
 * Utility function to apply rate limiting to server actions
 * Usage: const rateLimitResult = await applyRateLimit(authRateLimit)
 */
export async function applyRateLimit(rateLimiter: () => Promise<RateLimitResult>): Promise<RateLimitResult> {
  return await rateLimiter()
}

/**
 * Rate limiting middleware for API routes
 * Usage: const result = await rateLimitMiddleware(apiRateLimit, request)
 */
export async function rateLimitMiddleware(
  rateLimiter: () => Promise<RateLimitResult>
): Promise<RateLimitResult> {
  return await rateLimiter()
}
