import type { RankingUser } from '@/components/rankings/RankingsTable'
import { createClient } from '@/libs/supabase/server'

type RankingsOptions = {
  limit?: number
  gender?: 'male' | 'female'
  weightCategory?: 'under_95kg' | 'over_95kg'
  country?: string
}

// Define the type for the data returned by the SQL RPC
type ProfileDataFromRpc = {
  id: string
  name: string
  profile_image: string | null
  country: string | null
  gender: 'male' | 'female' | null
  weight_category: 'under_95kg' | 'over_95kg' | null
  role: string | null
  total_points: number | null
}

/**
 * Fetches rankings data from the database
 */
export async function fetchRankings({
  limit = 50,
  gender,
  weightCategory,
  country,
}: RankingsOptions = {}): Promise<RankingUser[]> {
  try {
    const supabase = createClient()

    // Build the query
    const { data: users, error } = await supabase.rpc('get_ranked_profiles', {
      _limit: limit,
      _gender: gender,
      _weight_category: weightCategory,
      _country: country,
    })

    if (error) {
      console.error('Error fetching rankings:', error)
      return []
    }

    // Fetch medal counts for each user
    const userIds = users?.map((user: ProfileDataFromRpc) => user.id) || []
    const { data: medals, error: medalsError } = await supabase
      .from('medals')
      .select('user_id, medal_type')
      .in('user_id', userIds)

    if (medalsError) {
      console.error('Error fetching medals:', medalsError)
      return []
    }

    // Group medals by user and type
    const medalCounts: Record<
      string,
      {
        bronze: number
        silver: number
        gold: number
        platinum: number
        diamond: number
      }
    > = {}

    userIds.forEach((userId: string) => {
      medalCounts[userId] = {
        bronze: 0,
        silver: 0,
        gold: 0,
        platinum: 0,
        diamond: 0,
      }
    })

    type MedalType = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

    medals?.forEach((medal) => {
      if (
        medal.medal_type !== 'none' &&
        medalCounts[medal.user_id] &&
        (medal.medal_type === 'bronze' ||
          medal.medal_type === 'silver' ||
          medal.medal_type === 'gold' ||
          medal.medal_type === 'platinum' ||
          medal.medal_type === 'diamond')
      ) {
        medalCounts[medal.user_id][medal.medal_type as MedalType] += 1
      }
    })

    // Transform data into the ranking format
    return (users || []).map((user: ProfileDataFromRpc) => ({
      id: user.id,
      name: user.name || 'Anonymous',
      profileImage: user.profile_image,
      country: user.country || 'Unknown',
      role: user.role,
      gender: user.gender,
      weightCategory: user.weight_category,
      totalPoints: user.total_points || 0,
      bronzeMedals: medalCounts[user.id]?.bronze || 0,
      silverMedals: medalCounts[user.id]?.silver || 0,
      goldMedals: medalCounts[user.id]?.gold || 0,
      platinumMedals: medalCounts[user.id]?.platinum || 0,
      diamondMedals: medalCounts[user.id]?.diamond || 0,
    }))
  } catch (error) {
    console.error('Failed to fetch rankings:', error)
    return []
  }
}

/**
 * Fetches rankings data for male athletes
 */
export async function fetchMaleRankings(
  options: Omit<RankingsOptions, 'gender'> = {},
) {
  return fetchRankings({ ...options, gender: 'male' })
}

/**
 * Fetches rankings data for female athletes
 */
export async function fetchFemaleRankings(
  options: Omit<RankingsOptions, 'gender'> = {},
) {
  return fetchRankings({ ...options, gender: 'female' })
}

/**
 * Fetches rankings data for a specific weight category
 */
export async function fetchWeightCategoryRankings(
  weightCategory: 'under_95kg' | 'over_95kg',
  options: Omit<RankingsOptions, 'weightCategory'> = {},
) {
  return fetchRankings({ ...options, weightCategory })
}

/**
 * Fetches rankings data for a specific country
 */
export async function fetchCountryRankings(
  country: string,
  options: Omit<RankingsOptions, 'country'> = {},
) {
  return fetchRankings({ ...options, country })
}
