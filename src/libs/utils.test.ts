import { describe, expect, it } from 'vitest'
import { cn } from './utils'

describe('cn utility function', () => {
  it('should merge basic class strings', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2')
  })

  it('should handle conditional classes correctly', () => {
    expect(cn('class1', { class2: true, class3: false })).toBe('class1 class2')
    expect(cn({ 'class1 class2': true, class3: false })).toBe('class1 class2')
  })

  it('should handle arrays of classes', () => {
    expect(cn(['class1', 'class2'], 'class3')).toBe('class1 class2 class3')
    expect(cn(['class1', { class2: true, class3: false }], 'class4')).toBe(
      'class1 class2 class4',
    )
  })

  it('should handle mixed arguments (strings, objects, arrays)', () => {
    expect(
      cn('class1', ['class2', { class3: true }], { class4: false }, 'class5'),
    ).toBe('class1 class2 class3 class5')
  })

  it('should remove falsy values', () => {
    expect(cn('class1', null, undefined, false, '', 0, 'class2')).toBe(
      'class1 class2',
    )
  })

  // Tests specifically for tailwind-merge behavior
  it('should override conflicting Tailwind classes (last one wins)', () => {
    expect(cn('p-4', 'p-2')).toBe('p-2') // Padding override
    expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500') // Color override
    expect(cn('bg-black', 'bg-white')).toBe('bg-white') // Background override
    expect(cn('m-1 m-2 m-3')).toBe('m-3') // Margin override
  })

  it('should merge non-conflicting Tailwind classes', () => {
    expect(cn('p-4', 'text-red-500')).toBe('p-4 text-red-500')
    expect(cn('flex', 'items-center', 'justify-center')).toBe(
      'flex items-center justify-center',
    )
  })

  it('should handle complex Tailwind overrides correctly', () => {
    // Example: padding overrides - expect custom logic
    expect(cn('px-4 py-2', 'p-3')).toBe('p-3') // p-* overrides px-* and py-*
    expect(cn('p-3', 'px-4')).toBe('p-3 px-4') // px-* overrides the x-axis part of p-*
    expect(cn('p-3', 'py-2')).toBe('p-3 py-2') // py-* overrides the y-axis part of p-*
    expect(cn('pt-2 pb-4 p-3 px-5')).toBe('pt-2 pb-4 px-5') // More specific pt/pb preserved over p-*
  })

  it('should handle conditional Tailwind class merging', () => {
    const hasPadding = false
    const isRed = true
    expect(cn('m-4', hasPadding ? 'p-4' : 'p-2', isRed && 'text-red-500')).toBe(
      'm-4 p-2 text-red-500',
    )
    expect(cn('p-4', { 'p-2': true })).toBe('p-2') // Override via object
  })
})
