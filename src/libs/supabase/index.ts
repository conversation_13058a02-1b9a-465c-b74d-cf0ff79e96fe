// Import from client file
import { createClient as createBrowserClient, supabase } from './client'

// Import from server file
import {
  createAdminClient,
  createClient as createServerClient,
  getSupabaseRouteHandlerClient,
} from './server'

// Import auth helpers
import * as authHelpers from './auth'

// Re-export with renamed functions to avoid conflicts
export {
  // Client-side exports
  supabase,
  createBrowserClient,
  // Server-side exports
  createServerClient,
  getSupabaseRouteHandlerClient,
  createAdminClient,
}

// Re-export all auth helpers
export const auth = authHelpers

// Export default client for easier imports
export default supabase
