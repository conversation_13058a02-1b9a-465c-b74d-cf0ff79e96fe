import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import * as credentials from './credentials';
import {
    createAdminClient,
    createClient as createReadOnlyServerClient,
    getSupabaseRouteHandlerClient,
} from './server';

// --- Mocks ---
// Mock the module to spy on the function calls
vi.mock('@supabase/ssr')

vi.mock('./credentials', () => ({
  getSupabaseCredentials: vi.fn().mockImplementation(() => ({
    supabaseUrl: 'http://mock-url.com',
    supabaseAnonKey: 'mock-anon-key',
  })),
  getSupabaseServiceCredentials: vi.fn().mockImplementation(() => ({
    supabaseUrl: 'http://mock-url.com',
    supabaseServiceRoleKey: 'mock-service-key',
  })),
}))

// Import and mock after the vi.mock calls
import { createServerClient } from '@supabase/ssr';
const mockCreateServerClient = vi.mocked(createServerClient)
const mockGetSupabaseCredentials = vi.mocked(credentials.getSupabaseCredentials)
const mockGetSupabaseServiceCredentials = vi.mocked(
  credentials.getSupabaseServiceCredentials,
)

// Mock next/headers cookies - very basic mock
const mockCookieStore = {
  getAll: vi.fn(() => [{ name: 'test-cookie', value: 'test-value' }]),
  set: vi.fn(),
  // Add other methods if needed by the implementation
}
vi.mock('next/headers', () => ({
  cookies: () => mockCookieStore,
}))
// ---

describe('Supabase Server Client Utilities (src/libs/supabase/server.ts)', () => {
  const originalEnv = { ...process.env }

  beforeEach(() => {
    // Ensure NODE_ENV is set to 'test' while preserving other env vars
    process.env = { ...originalEnv, NODE_ENV: 'test' }

    // Reset all mocks
    vi.clearAllMocks()

    // Reset mock implementations with default values
    mockCreateServerClient
      .mockReset()
      .mockReturnValue({ mock: 'client', auth: { getUser: vi.fn() } } as any)
    mockGetSupabaseCredentials.mockReset().mockReturnValue({
      supabaseUrl: 'http://mock-url.com',
      supabaseAnonKey: 'mock-anon-key',
    })
    mockGetSupabaseServiceCredentials.mockReset().mockReturnValue({
      supabaseUrl: 'http://mock-url.com',
      supabaseServiceRoleKey: 'mock-service-key',
    })
  })

  afterEach(() => {
    process.env = originalEnv
  })

  // --- Tests for createClient (Read-Only Server Client) ---
  describe('createClient (Read-Only)', () => {
    it('should create a client with credentials and dummy cookie methods', () => {
      // SECURITY FIX: Use mock values instead of real environment variables
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-test-url.com',
        supabaseAnonKey: 'mock-test-anon-key',
      })

      const client = createReadOnlyServerClient()

      // Verify the client was created successfully
      expect(client).toBeDefined()
      expect(client).toHaveProperty('auth')
      expect(client.auth).toHaveProperty('getUser')
    })

    it('should create a client with credentials in test environment', () => {
      // SECURITY FIX: Use mock values instead of real environment variables
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-test-url-2.com',
        supabaseAnonKey: 'mock-test-anon-key-2',
      })

      const client = createReadOnlyServerClient()

      // Verify the client was created successfully
      expect(client).toBeDefined()
      expect(client).toHaveProperty('auth')
      expect(client.auth).toHaveProperty('getUser')
    })
  })

  // --- Tests for getSupabaseRouteHandlerClient (Server Client with Cookies) ---
  describe('getSupabaseRouteHandlerClient', () => {
    // Create a Map to easily satisfy ReadonlyRequestCookies properties
    const baseCookieMap = new Map<string, { name: string; value: string }>()
    baseCookieMap.set('session', { name: 'session', value: 'token' })

    const mockCookieStore = {
      ...Object.fromEntries(baseCookieMap.entries()), // Spread Map entries
      getAll: vi.fn().mockReturnValue(Array.from(baseCookieMap.values())),
      set: vi.fn(),
      get: (key: string) => baseCookieMap.get(key),
      has: (key: string) => baseCookieMap.has(key),
      get size() {
        return baseCookieMap.size
      },
      [Symbol.iterator]: () => baseCookieMap.entries(),
    } as unknown as ReadonlyRequestCookies

    it('should call createServerClient with local credentials and real cookie methods', () => {
      process.env.NEXT_PUBLIC_SUPABASE_ENV = 'local'
      // SECURITY FIX: Use mock values instead of real environment variables
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-local-url.com',
        supabaseAnonKey: 'mock-local-anon-key',
      })
      getSupabaseRouteHandlerClient(mockCookieStore as any)

      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-local-url.com',
        'mock-local-anon-key',
        expect.objectContaining({
          cookies: {
            getAll: expect.any(Function),
            setAll: expect.any(Function),
          },
        }),
      )
      // Check if the actual cookie methods were passed and call the mock store
      const { cookies: passedCookieHandlers } = (mockCreateServerClient as any)
        .mock.calls[0][2]
      passedCookieHandlers.getAll()
      expect(mockCookieStore.getAll).toHaveBeenCalledTimes(1)
      const cookiesToSet = [{ name: 'new', value: 'cookie', options: {} }]
      passedCookieHandlers.setAll(cookiesToSet)
      expect(mockCookieStore.set).toHaveBeenCalledTimes(1)
      // Check for name and value, don't strictly require options if it was empty
      expect(mockCookieStore.set).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'new', value: 'cookie' }),
      )
    })

    it('should call createServerClient with production credentials when env is not local', () => {
      process.env.NEXT_PUBLIC_SUPABASE_ENV = 'production'
      // SECURITY FIX: Use mock values instead of real environment variables
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-prod-url.com',
        supabaseAnonKey: 'mock-prod-anon-key',
      })
      getSupabaseRouteHandlerClient(mockCookieStore as any)

      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-prod-url.com',
        'mock-prod-anon-key',
        expect.any(Object), // Cookie methods tested above
      )
    })

    it('should get credentials and create a server client with cookie store', () => {
      // Mock credentials for this test case (ensure it uses the base mock values)
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-url.com', // Unconditionally use mock URL
        supabaseAnonKey: 'mock-anon-key', // Unconditionally use mock key
      })
      getSupabaseRouteHandlerClient(mockCookieStore)
      expect(mockGetSupabaseCredentials).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-url.com',
        'mock-anon-key',
        expect.objectContaining({
          cookies: expect.objectContaining({
            getAll: expect.any(Function),
            setAll: expect.any(Function),
          }),
        }),
      )
    })

    it('should use the provided cookie store for cookie handling', () => {
      getSupabaseRouteHandlerClient(mockCookieStore)
      const cookieHandlers = (mockCreateServerClient as any).mock.calls[0][2]
        ?.cookies

      // Test getAll
      expect(cookieHandlers?.getAll()).toEqual([
        { name: 'session', value: 'token' },
      ])
      expect(mockCookieStore.getAll).toHaveBeenCalledTimes(1)

      // Test setAll
      const cookiesToSet = [
        { name: 'new', value: 'cookie', options: { path: '/' } },
      ]
      cookieHandlers?.setAll(cookiesToSet)
      expect(mockCookieStore.set).toHaveBeenCalledTimes(1)
      expect(mockCookieStore.set).toHaveBeenCalledWith({
        name: 'new',
        value: 'cookie',
        path: '/',
      })
    })

    it('should handle setAll errors gracefully (e.g., called from Server Component context)', () => {
      // Simulate error during set
      const faultyBaseCookieMap = new Map<
        string,
        { name: string; value: string }
      >()
      const faultyCookieStore = {
        ...Object.fromEntries(faultyBaseCookieMap.entries()),
        getAll: vi.fn(),
        set: vi.fn().mockImplementation(() => {
          throw new Error('Cannot set cookie')
        }),
        get: (key: string) => faultyBaseCookieMap.get(key),
        has: (key: string) => faultyBaseCookieMap.has(key),
        get size() {
          return faultyBaseCookieMap.size
        },
        [Symbol.iterator]: () => faultyBaseCookieMap.entries(),
      } as unknown as ReadonlyRequestCookies

      getSupabaseRouteHandlerClient(faultyCookieStore)
      const cookieHandlers = (mockCreateServerClient as any).mock.calls[0][2]
        ?.cookies

      const cookiesToSet = [{ name: 'new', value: 'cookie' }]
      // Expect setAll not to throw when the internal set call fails
      expect(() => cookieHandlers?.setAll(cookiesToSet)).not.toThrow()
      expect(faultyCookieStore.set).toHaveBeenCalledTimes(1)
    })

    it('should return the client instance', () => {
      mockGetSupabaseCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-url.com',
        supabaseAnonKey: 'mock-anon-key',
      })
      const client = getSupabaseRouteHandlerClient(mockCookieStore)
      expect(client).toStrictEqual({ mock: 'client', auth: { getUser: expect.any(Function) } })
    })
  })

  // --- Tests for createAdminClient ---
  describe('createAdminClient', () => {
    it('should call createServerClient with local URL and Service Role Key', () => {
      process.env.NEXT_PUBLIC_SUPABASE_ENV = 'local'
      // SECURITY FIX: Use mock values instead of real service keys
      mockGetSupabaseServiceCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-local-url.com',
        supabaseServiceRoleKey: 'mock-local-service-key',
      })
      createAdminClient()

      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-local-url.com',
        'mock-local-service-key',
        expect.objectContaining({
          cookies: {
            getAll: expect.any(Function),
            setAll: expect.any(Function),
          },
        }),
      )
      // Check dummy cookie methods for admin client
      const { cookies } = (mockCreateServerClient as any).mock.calls[0][2]
      expect(cookies.getAll()).toEqual([])
      expect(() => cookies.setAll([])).not.toThrow()
    })

    it('should call createServerClient with production URL and Service Role Key when env is not local', () => {
      process.env.NEXT_PUBLIC_SUPABASE_ENV = 'production'
      // SECURITY FIX: Use mock values instead of real service keys
      mockGetSupabaseServiceCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-prod-url.com',
        supabaseServiceRoleKey: 'mock-prod-service-key',
      })
      createAdminClient()

      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-prod-url.com',
        'mock-prod-service-key',
        expect.any(Object), // Cookie methods tested above
      )
    })

    it('should get service credentials and create a server client', () => {
      mockGetSupabaseServiceCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-url.com',
        supabaseServiceRoleKey: 'mock-service-key',
      })
      createAdminClient()
      expect(mockGetSupabaseServiceCredentials).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledTimes(1)
      expect(mockCreateServerClient).toHaveBeenCalledWith(
        'http://mock-url.com',
        'mock-service-key', // Crucially uses the service key
        expect.objectContaining({
          cookies: expect.objectContaining({
            getAll: expect.any(Function),
            setAll: expect.any(Function),
          }),
        }),
      )
    })

    it('should provide no-op cookie handlers for admin client', () => {
      createAdminClient()
      const cookieHandlers = (mockCreateServerClient as any).mock.calls[0][2]
        ?.cookies
      expect(cookieHandlers?.getAll()).toEqual([])
      expect(
        cookieHandlers?.setAll([{ name: 'test', value: 'val' }]),
      ).toBeUndefined()
    })

    it('should return the client instance', () => {
      mockGetSupabaseServiceCredentials.mockReturnValue({
        supabaseUrl: 'http://mock-url.com',
        supabaseServiceRoleKey: 'mock-service-key',
      })
      const client = createAdminClient()
      expect(client).toStrictEqual({ mock: 'client', auth: { getUser: expect.any(Function) } })
    })
  })
})
