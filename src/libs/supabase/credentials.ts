/**
 * Shared utility for Supabase credentials
 * Centralizes the logic for determining which Supabase credentials to use based on environment
 */

/**
 * Gets the Supabase URL and anon key based on the environment.
 * Reads environment variables and throws descriptive errors if missing.
 * @returns Object containing supabaseUrl and supabaseAnonKey
 */
export const getSupabaseCredentials = () => {
  // In test mode, always read fresh env vars (no cache)
  const envMode = process.env.NEXT_PUBLIC_SUPABASE_ENV
  const isLocal = envMode === 'local'
  const supabaseUrl = isLocal
    ? process.env.NEXT_PUBLIC_SUPABASE_URL
    : process.env.NEXT_PUBLIC_SUPABASE_URL_PROD
  const supabaseAnonKey = isLocal
    ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD

  if (!supabaseUrl) {
    throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_URL')
  }
  if (!supabaseAnonKey) {
    throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY')
  }

  return { supabaseUrl, supabaseAnonKey }
}

/**
 * Gets the Supabase URL and service role key based on the environment.
 * Reads environment variables and throws descriptive errors if missing.
 * @returns Object containing supabaseUrl and supabaseServiceRoleKey
 */
export const getSupabaseServiceCredentials = () => {
  const envMode = process.env.NEXT_PUBLIC_SUPABASE_ENV
  const isLocal = envMode === 'local'
  const supabaseUrl = isLocal
    ? process.env.NEXT_PUBLIC_SUPABASE_URL
    : process.env.NEXT_PUBLIC_SUPABASE_URL_PROD
  const supabaseServiceRoleKey = isLocal
    ? process.env.SUPABASE_SERVICE_ROLE_KEY
    : process.env.SUPABASE_SERVICE_ROLE_KEY_PROD

  if (!supabaseUrl) {
    throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_URL')
  }
  if (!supabaseServiceRoleKey) {
    throw new Error('Missing env.SUPABASE_SERVICE_ROLE_KEY')
  }

  return { supabaseUrl, supabaseServiceRoleKey }
}
