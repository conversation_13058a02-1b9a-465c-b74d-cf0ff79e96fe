/**
 * This file contains examples of how to use the Supabase client in different contexts.
 * These are not meant to be used directly, but rather as a reference for how to use
 * the Supabase client in your application.
 */

// Client Component Example
'use client'

import supabase, { auth } from '@/libs/supabase'
import type { User } from '@supabase/supabase-js'
import { useState } from 'react'

export function ClientComponentExample() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Example of using the Supabase client directly in a client component
  async function fetchExercises() {
    const { data, error } = await supabase
      .from('exercises')
      .select('*')
      .limit(10)

    if (error) {
      console.error('Error fetching exercises:', error)
      return []
    }

    return data
  }

  // Example of using auth helpers
  async function handleSignIn(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const { data, error } = await auth.signIn(email, password)

      if (error) {
        throw error
      }

      setUser(data.user)
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred during sign in'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <h2>Client Component Example</h2>

      {user ? (
        <div>
          <p>Logged in as: {user.email}</p>
          <button onClick={() => auth.signOut()}>Sign Out</button>

          <h3>Fetch Data Example</h3>
          <button
            onClick={async () => {
              const exercises = await fetchExercises()
              console.log('Exercises:', exercises)
            }}
          >
            Fetch Exercises
          </button>
        </div>
      ) : (
        <form onSubmit={handleSignIn}>
          <div>
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div>
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          {error && <p style={{ color: 'red' }}>{error}</p>}

          <button type="submit" disabled={loading}>
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
      )}
    </div>
  )
}

// Server Component Example (in a separate file)
// Note: This would typically be in a separate file since you can't mix 'use client' and server components
// Example usage in a server component:
/*
import { createServerClient } from '@/libs/supabase';

export default async function ServerComponentExample() {
  // Create a server client
  const supabase = createServerClient();

  // Fetch data from Supabase
  const { data: exercises } = await supabase
    .from('exercises')
    .select('*')
    .limit(10);

  return (
    <div>
      <h2>Server Component Example</h2>
      <ul>
        {exercises?.map((exercise) => (
          <li key={exercise.id}>{exercise.title}</li>
        ))}
      </ul>
    </div>
  );
}
*/

// Route Handler Example (in a separate file)
// Example usage in a route handler (API route):
/*
import { cookies } from 'next/headers';
import { getSupabaseRouteHandlerClient } from '@/libs/supabase';

export async function POST(request: Request) {
  const cookieStore = cookies();
  const supabase = getSupabaseRouteHandlerClient(cookieStore);

  const { searchParams } = new URL(request.url);
  const email = searchParams.get('email');

  // Example: Check if a user exists
  const { data, error } = await supabase
    .from('users')
    .select('id')
    .eq('email', email)
    .single();

  if (error) {
    return Response.json({ error: error.message }, { status: 400 });
  }

  return Response.json({ exists: !!data });
}
*/

// Admin Operations Example (in a separate file)
// Example usage for admin operations (server-side only):
/*
import { createAdminClient } from '@/libs/supabase';

export async function adminCreateNewUser(
  email: string,
  password: string,
  role: 'athlete' | 'grandmaster' | 'admin'
) {
  const supabase = createAdminClient();

  // Create a new user with admin privileges
  const { data, error } = await supabase.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
    user_metadata: { role },
  });

  if (error) {
    console.error('Error creating user:', error);
    return { success: false, error };
  }

  // Add the user to the users table with their role
  if (data.user) {
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: data.user.id,
        email: data.user.email,
        role,
        created_at: new Date().toISOString(),
      });

    if (profileError) {
      console.error('Error creating user profile:', profileError);
      return { success: false, error: profileError };
    }
  }

  return { success: true, user: data.user };
}
*/
