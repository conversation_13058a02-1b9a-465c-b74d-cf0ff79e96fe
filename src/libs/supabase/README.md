# Supabase Client for Armwrestling Power Arena

This directory contains utilities for interacting with Supa<PERSON> in the Armwrestling Power Arena application. It provides a consistent way to access Supabase services from both client and server components, with support for local development and production environments.

## Setup

### Prerequisites

1. Docker Desktop installed
2. Supabase CLI installed
3. Local Supabase instance running

### Environment Variables

The following environment variables are required in your `.env.local` file:

```
# Local Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Environment mode (local or production)
NEXT_PUBLIC_SUPABASE_ENV=local

# Production Supabase configuration (to be filled when moving to production)
# NEXT_PUBLIC_SUPABASE_URL_PROD=your-production-url
# NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD=your-production-anon-key
# SUPABASE_SERVICE_ROLE_KEY_PROD=your-production-service-role-key
```

## Local Supabase Management

### Starting Local Supabase

```bash
supabase start
```

This command starts all the necessary services for local Supabase development. The first time you run this, it may take a few minutes to download and set up the Docker containers.

### Viewing Local Supabase Status

```bash
supabase status
```

This command shows the status of your local Supabase instance, including URLs and keys.

### Stopping Local Supabase

```bash
supabase stop
```

This command stops the local Supabase services.

### Resetting Local Supabase

If you need to reset your local Supabase instance (clear all data):

```bash
supabase db reset
```

### Accessing Supabase Studio

The Supabase Studio is available at http://127.0.0.1:54323. You can use it to:

- View and manage your database
- Create and manage authentication users
- Configure storage buckets
- View logs and more

## Usage

### Client-Side Usage

Import the default Supabase client for use in client components:

```typescript
import supabase from '@/libs/supabase';

// Example: Query data
const { data, error } = await supabase.from('exercises').select('*').limit(10);
```

### Server-Side Usage

For server components, use the server client:

```typescript
import { createServerClient } from '@/libs/supabase';

// In a server component
const supabase = createServerClient();
const { data, error } = await supabase.from('exercises').select('*').limit(10);
```

### Route Handler Usage

For API routes and route handlers, use the route handler client with cookie support:

```typescript
import { cookies } from 'next/headers';
import { getSupabaseRouteHandlerClient } from '@/libs/supabase';

export async function POST(request: Request) {
  const cookieStore = cookies();
  const supabase = getSupabaseRouteHandlerClient(cookieStore);

  // Use supabase client...
}
```

### Authentication Helpers

The `auth` object provides helper functions for common authentication operations:

```typescript
import { auth } from '@/libs/supabase';

// Sign up
const { data, error } = await auth.signUp('<EMAIL>', 'password123');

// Sign in
const { data, error } = await auth.signIn('<EMAIL>', 'password123');

// Sign out
await auth.signOut();

// Get current user
const { user, error } = await auth.getUser();
```

### Admin Operations

For admin operations (server-side only):

```typescript
import { createAdminClient } from '@/libs/supabase';

// In a server-side function
const adminClient = createAdminClient();

// Example: Create a user with admin privileges
const { data, error } = await adminClient.auth.admin.createUser({
  email: '<EMAIL>',
  password: 'securepassword',
  email_confirm: true,
  user_metadata: { role: 'admin' },
});
```

## Examples

See the `examples.tsx` file for more detailed examples of how to use the Supabase client in different contexts.

## Switching Between Local and Production

The environment is controlled by the `NEXT_PUBLIC_SUPABASE_ENV` environment variable. Set it to:

- `local` for local development
- `production` for production environment

The client utilities will automatically use the appropriate URL and keys based on this setting.

## Testing the Connection

You can verify that your Supabase client is correctly configured and can connect to your local Supabase instance by running the test script:

```bash
npm run test:supabase
```

This script will:

1. Check that your environment variables are properly loaded
2. Attempt to connect to your local Supabase instance
3. Verify that the authentication system is working

If the connection is successful, you'll see output similar to:

```
Testing connection to Supabase...
URL: http://127.0.0.1:54321
✅ Successfully connected to Supabase!
✅ Authentication system is working!
Session: None
```

If there are any issues, the script will provide troubleshooting tips.

## Troubleshooting

### Common Issues

1. **Connection refused errors**: Make sure your local Supabase instance is running. Check with `supabase status`.

2. **Authentication issues**: Verify that you're using the correct anon key and URL.

3. **Database schema issues**: If you've made changes to the schema, you may need to reset your local database with `supabase db reset`.

4. **Docker issues**: If Docker containers aren't starting properly, try restarting Docker Desktop.

### Logs

To view logs from your local Supabase instance:

```bash
supabase logs
```

You can filter logs by service:

```bash
supabase logs auth
supabase logs db
supabase logs api
```
