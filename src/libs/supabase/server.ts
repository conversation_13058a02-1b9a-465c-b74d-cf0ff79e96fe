import { createServerClient } from '@supabase/ssr'
import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'
import {
    getSupabaseCredentials,
    getSupabaseServiceCredentials,
} from './credentials'

/**
 * Cookie handlers for different client types
 */
const createNoCookieHandlers = () => ({
  getAll() {
    return []
  },
  setAll() {},
})

const createRouteHandlerCookieHandlers = (cookieStore: ReadonlyRequestCookies) => ({
  getAll() {
    return cookieStore.getAll()
  },
  setAll(cookiesToSet: Array<{
    name: string
    value: string
    options?: Record<string, unknown>
  }>) {
    try {
      for (const { name, value, options } of cookiesToSet) {
        cookieStore.set({ name, value, ...options })
      }
    } catch {
      // Silently handle errors (e.g., when called from Server Component context)
    }
  },
})

/**
 * Unified client factory function
 */
const createSupabaseClient = (
  url: string,
  key: string,
  cookieHandlers: ReturnType<typeof createNoCookieHandlers | typeof createRouteHandlerCookieHandlers>
) => {
  return createServerClient(url, key, {
    cookies: cookieHandlers,
  })
}

/**
 * Creates a Supabase client for use in server components.
 * This client doesn't have cookie handling capabilities.
 */
export const createClient = () => {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createSupabaseClient(supabaseUrl, supabaseAnonKey, createNoCookieHandlers())
}

/**
 * Creates a Supabase client for use in route handlers with cookie support.
 *
 * Usage in a route handler:
 * ```
 * import { cookies } from 'next/headers';
 * import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server';
 *
 * export async function POST(request: Request) {
 *   const cookieStore = cookies();
 *   const supabase = getSupabaseRouteHandlerClient(cookieStore);
 *   // Use supabase client...
 * }
 * ```
 */
export const getSupabaseRouteHandlerClient = (
  cookieStore: ReadonlyRequestCookies,
) => {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  return createSupabaseClient(
    supabaseUrl,
    supabaseAnonKey,
    createRouteHandlerCookieHandlers(cookieStore)
  )
}

/**
 * Creates a Supabase admin client with the service role key.
 * This client has full access to the database without RLS restrictions.
 * IMPORTANT: Only use this on the server side and never expose it to the client.
 */
export const createAdminClient = () => {
  const { supabaseUrl, supabaseServiceRoleKey } = getSupabaseServiceCredentials()
  return createSupabaseClient(supabaseUrl, supabaseServiceRoleKey, createNoCookieHandlers())
}
