import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server'
import { cookies } from 'next/headers'
import { isPathAllowed } from './acl'
import { getPageAccess } from './pageRoles'

/**
 * Throws if the current user does not have one of the required roles.
 * Usage: await assertRole(["admin"], cookies());
 */
export async function assertRole(
  allowed: Array<'admin' | 'grandmaster' | 'athlete'>,
) {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()
  if (!user) {
    throw new Error('Permission denied: not authenticated')
  }
  const { data } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  if (!data || !allowed.includes(data.role)) {
    throw new Error('Permission denied: insufficient role')
  }
  return data.role as 'admin' | 'grandmaster' | 'athlete'
}

/**
 * Throws if the current user is not allowed to access the given path.
 * Usage: await assertPathAllowed("/admin")
 */
export async function assertPathAllowed(pathname: string) {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()
  if (!user) {
    throw new Error('Permission denied: not authenticated')
  }
  const { data } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  if (!data || !isPathAllowed(data.role, pathname)) {
    throw new Error('Permission denied: path not allowed for role')
  }
  return data.role as 'admin' | 'grandmaster' | 'athlete'
}

/**
 * Returns the page access levels for the current user and page.
 * Usage: await getCurrentPageAccess("/profile")
 */
export async function getCurrentPageAccess(page: string) {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()
  if (!user) {
    return { edit: 0, remove: 0 }
  }
  const { data } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  if (!data) {
    return { edit: 0, remove: 0 }
  }
  return getPageAccess(data.role, page)
}
