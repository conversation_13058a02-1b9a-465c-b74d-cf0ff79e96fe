/**
 * Role-Based Access Control (RBAC) Types for Armwrestling Power Arena
 */

/**
 * Available user roles in the system
 */
export type UserRole = 'athlete' | 'grandmaster' | 'admin'

/**
 * Permission categories for organizing permissions
 */
export type PermissionCategory =
  | 'exercises'
  | 'submissions'
  | 'users'
  | 'medals'
  | 'settings'

/**
 * Individual permissions that can be granted to roles
 */
export type Permission =
  // Exercise permissions
  | 'exercises:view'
  | 'exercises:create'
  | 'exercises:edit'
  | 'exercises:delete'

  // Submission permissions
  | 'submissions:view-own'
  | 'submissions:view-all'
  | 'submissions:create'
  | 'submissions:edit-own'
  | 'submissions:edit-all'
  | 'submissions:evaluate'
  | 'submissions:delete'

  // User permissions
  | 'users:view-profiles'
  | 'users:edit-own-profile'
  | 'users:edit-any-profile'
  | 'users:manage-roles'

  // Medal permissions
  | 'medals:view'
  | 'medals:award'
  | 'medals:edit'
  | 'medals:delete'

  // Settings permissions
  | 'settings:view'
  | 'settings:edit'

/**
 * Maps permission to a readable description
 */
export const PERMISSION_DESCRIPTIONS: Record<Permission, string> = {
  // Exercise permissions
  'exercises:view': 'View exercises and tutorials',
  'exercises:create': 'Create new exercises',
  'exercises:edit': 'Edit existing exercises',
  'exercises:delete': 'Delete exercises',

  // Submission permissions
  'submissions:view-own': 'View your own submissions',
  'submissions:view-all': 'View all user submissions',
  'submissions:create': 'Submit exercise performances',
  'submissions:edit-own': 'Edit your own pending submissions',
  'submissions:edit-all': 'Edit any submission',
  'submissions:evaluate': 'Evaluate and award points for submissions',
  'submissions:delete': 'Delete submissions',

  // User permissions
  'users:view-profiles': 'View user profiles',
  'users:edit-own-profile': 'Edit your own profile',
  'users:edit-any-profile': 'Edit any user profile',
  'users:manage-roles': 'Change user roles',

  // Medal permissions
  'medals:view': 'View medals',
  'medals:award': 'Award medals to users',
  'medals:edit': 'Edit awarded medals',
  'medals:delete': 'Delete medals',

  // Settings permissions
  'settings:view': 'View system settings',
  'settings:edit': 'Edit system settings',
}

/**
 * Maps each permission to its category
 */
export const PERMISSION_CATEGORIES: Record<Permission, PermissionCategory> = {
  // Exercise permissions
  'exercises:view': 'exercises',
  'exercises:create': 'exercises',
  'exercises:edit': 'exercises',
  'exercises:delete': 'exercises',

  // Submission permissions
  'submissions:view-own': 'submissions',
  'submissions:view-all': 'submissions',
  'submissions:create': 'submissions',
  'submissions:edit-own': 'submissions',
  'submissions:edit-all': 'submissions',
  'submissions:evaluate': 'submissions',
  'submissions:delete': 'submissions',

  // User permissions
  'users:view-profiles': 'users',
  'users:edit-own-profile': 'users',
  'users:edit-any-profile': 'users',
  'users:manage-roles': 'users',

  // Medal permissions
  'medals:view': 'medals',
  'medals:award': 'medals',
  'medals:edit': 'medals',
  'medals:delete': 'medals',

  // Settings permissions
  'settings:view': 'settings',
  'settings:edit': 'settings',
}
