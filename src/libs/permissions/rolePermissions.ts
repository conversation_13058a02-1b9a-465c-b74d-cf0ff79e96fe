import type { Permission, UserRole } from './types'

/**
 * Permission sets for each role in the system
 * Permission grants are cumulative - higher roles inherit permissions from lower roles
 */

/**
 * Base permissions for athletes (regular users)
 */
export const ATHLETE_PERMISSIONS: Permission[] = [
  'exercises:view',
  'submissions:view-own',
  'submissions:create',
  'submissions:edit-own',
  'users:view-profiles',
  'users:edit-own-profile',
  'medals:view',
]

/**
 * Grandma<PERSON> permissions include all athlete permissions
 * plus additional evaluation capabilities
 */
export const GRANDMASTER_PERMISSIONS: Permission[] = [
  ...ATHLETE_PERMISSIONS,
  'submissions:view-all',
  'submissions:evaluate',
  'medals:award',
]

/**
 * Admin permissions include all permissions
 */
export const ADMIN_PERMISSIONS: Permission[] = [
  ...GRANDMASTER_PERMISSIONS,
  'exercises:create',
  'exercises:edit',
  'exercises:delete',
  'submissions:edit-all',
  'submissions:delete',
  'users:edit-any-profile',
  'users:manage-roles',
  'medals:edit',
  'medals:delete',
  'settings:view',
  'settings:edit',
]

/**
 * Maps user roles to their permission sets
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  athlete: ATHLETE_PERMISSIONS,
  grandmaster: GRANDMASTER_PERMISSIONS,
  admin: ADMIN_PERMISSIONS,
}

/**
 * Gets all permissions available to a specific user role
 */
export function getPermissionsForRole(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || []
}

/**
 * Checks if a specific role has a specific permission
 */
export function roleHasPermission(
  role: UserRole,
  permission: Permission,
): boolean {
  return ROLE_PERMISSIONS[role]?.includes(permission) || false
}

/**
 * Checks if a specific role has any of the specified permissions
 */
export function roleHasAnyPermission(
  role: UserRole,
  permissions: Permission[],
): boolean {
  return permissions.some((permission) => roleHasPermission(role, permission))
}

/**
 * Checks if a specific role has all of the specified permissions
 */
export function roleHasAllPermissions(
  role: UserRole,
  permissions: Permission[],
): boolean {
  return permissions.every((permission) => roleHasPermission(role, permission))
}
