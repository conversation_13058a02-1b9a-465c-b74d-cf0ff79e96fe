import {
  roleHasAllPermissions,
  roleHasAnyPermission,
  roleHasPermission,
} from './rolePermissions'
import type { Permission, UserRole } from './types'

/**
 * User profile with role information
 */
export interface UserWithRole {
  id: string
  role: UserRole
  [key: string]: any
}

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  user: UserWithRole | null | undefined,
  permission: Permission,
): boolean {
  if (!user) return false
  return roleHasPermission(user.role, permission)
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  user: UserWithRole | null | undefined,
  permissions: Permission[],
): boolean {
  if (!user) return false
  return roleHasAnyPermission(user.role, permissions)
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(
  user: UserWithRole | null | undefined,
  permissions: Permission[],
): boolean {
  if (!user) return false
  return roleHasAllPermissions(user.role, permissions)
}

/**
 * Checks if the provided user is the resource owner or has the required permission
 * This is useful for checking if a user can modify their own resources or has admin rights
 */
export function isOwnerOrHasPermission(
  user: UserWithRole | null | undefined,
  resourceOwnerId: string | null | undefined,
  permission: Permission,
): boolean {
  if (!user) return false

  // Check if user is the resource owner
  const isOwner = Boolean(resourceOwnerId && user.id === resourceOwnerId)

  // If user is the owner, they have access
  if (isOwner) return true

  // Otherwise, check if they have the required permission
  return hasPermission(user, permission)
}

/**
 * Check if user has a specific role
 */
export function hasRole(
  user: UserWithRole | null | undefined,
  role: UserRole,
): boolean {
  if (!user) return false
  return user.role === role
}

/**
 * Check if user has one of the specified roles
 */
export function hasAnyRole(
  user: UserWithRole | null | undefined,
  roles: UserRole[],
): boolean {
  if (!user) return false
  return roles.includes(user.role)
}

/**
 * Checks if user is an administrator
 */
export function isAdmin(user: UserWithRole | null | undefined): boolean {
  return hasRole(user, 'admin')
}

/**
 * Checks if user is a grandmaster (or higher)
 */
export function isGrandmasterOrAdmin(
  user: UserWithRole | null | undefined,
): boolean {
  if (!user) return false
  return user.role === 'grandmaster' || user.role === 'admin'
}
