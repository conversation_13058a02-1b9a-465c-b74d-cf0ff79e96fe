import { describe, expect, it } from 'vitest'
import { isPathAllowed } from '../acl'

describe('isPathAllowed', () => {
  it('allows admin all admin routes', () => {
    expect(isPathAllowed('admin', '/admin')).toBe(true)
    expect(isPathAllowed('admin', '/admin/settings')).toBe(true)
    expect(isPathAllowed('admin', '/submissions')).toBe(true)
    expect(isPathAllowed('admin', '/profile')).toBe(true)
    expect(isPathAllowed('admin', '/')).toBe(true)
  })

  it('blocks athlete from /admin and /submissions', () => {
    expect(isPathAllowed('athlete', '/admin')).toBe(false)
    expect(isPathAllowed('athlete', '/submissions')).toBe(false)
    expect(isPathAllowed('athlete', '/profile')).toBe(true)
    expect(isPathAllowed('athlete', '/exercises')).toBe(true)
    expect(isPathAllowed('athlete', '/')).toBe(true)
  })

  it('allows grandmaster to /submissions but not /admin', () => {
    expect(isPathAllowed('grandmaster', '/submissions')).toBe(true)
    expect(isPathAllowed('grandmaster', '/admin')).toBe(false)
    expect(isPathAllowed('grandmaster', '/profile')).toBe(true)
    expect(isPathAllowed('grandmaster', '/')).toBe(true)
  })

  it('handles exact root path', () => {
    expect(isPathAllowed('admin', '/')).toBe(true)
    expect(isPathAllowed('athlete', '/')).toBe(true)
    expect(isPathAllowed('grandmaster', '/')).toBe(true)
  })

  it('blocks unknown paths', () => {
    expect(isPathAllowed('athlete', '/notfound')).toBe(false)
    expect(isPathAllowed('grandmaster', '/secret')).toBe(false)
    expect(isPathAllowed('admin', '/supersecret')).toBe(false)
  })

  it('matches only allowed prefixes', () => {
    expect(isPathAllowed('athlete', '/exercises/123')).toBe(true)
    expect(isPathAllowed('grandmaster', '/rankings/2024')).toBe(true)
    expect(isPathAllowed('admin', '/account/settings')).toBe(true)
  })
})
