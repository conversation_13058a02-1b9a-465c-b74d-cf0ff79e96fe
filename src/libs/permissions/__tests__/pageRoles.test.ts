import { describe, expect, it } from 'vitest'
import { getPageAccess } from '../pageRoles'

describe('getPageAccess', () => {
  it('returns correct levels for admin', () => {
    expect(getPageAccess('admin', '/profile')).toEqual({ edit: 1, remove: 1 })
    expect(getPageAccess('admin', '/submissions')).toEqual({
      edit: 1,
      remove: 1,
    })
    expect(getPageAccess('admin', '/exercises')).toEqual({
      edit: 1,
      remove: 1,
    })
  })

  it('returns correct levels for grandmaster', () => {
    expect(getPageAccess('grandmaster', '/profile')).toEqual({
      edit: 1,
      remove: 0,
    })
    expect(getPageAccess('grandmaster', '/submissions')).toEqual({
      edit: 1,
      remove: 1,
    })
    expect(getPageAccess('grandmaster', '/exercises')).toEqual({
      edit: 1,
      remove: 0,
    })
  })

  it('returns correct levels for athlete', () => {
    expect(getPageAccess('athlete', '/profile')).toEqual({
      edit: 1,
      remove: 0,
    })
    expect(getPageAccess('athlete', '/submissions')).toEqual({
      edit: 0,
      remove: 0,
    })
    expect(getPageAccess('athlete', '/exercises')).toEqual({
      edit: 0,
      remove: 0,
    })
  })

  it('returns {0,0} for missing page or role', () => {
    expect(getPageAccess('admin', '/notfound')).toEqual({ edit: 0, remove: 0 })
    expect(getPageAccess('grandmaster', '/unknown')).toEqual({
      edit: 0,
      remove: 0,
    })
    expect(getPageAccess('athlete', '/foo')).toEqual({ edit: 0, remove: 0 })
  })
})
