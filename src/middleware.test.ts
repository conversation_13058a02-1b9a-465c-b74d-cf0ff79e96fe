import { createServerClient } from '@supabase/ssr'
import type { NextRequest } from 'next/server'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { middleware } from './middleware'

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(),
}))

function createMockRequest(path: string): NextRequest {
  const url = `http://localhost:3000${path}`
  return {
    nextUrl: {
      pathname: path,
      origin: 'http://localhost:3000',
      clone: () => ({ pathname: path, origin: 'http://localhost:3000' }),
    },
    url: url,
    cookies: { getAll: () => [] },
  } as unknown as NextRequest
}

describe('middleware route protection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('redirects anonymous user from /account to /login', async () => {
    ;(createServerClient as any).mockReturnValue({
      auth: { getUser: async () => ({ data: { user: null } }) },
    })
    const req = createMockRequest('/account')
    const res = await middleware(req as any)
    expect(res.headers.get('location')).toBe('http://localhost:3000/login')
  })

  it('allows anonymous user to access /profile/abc', async () => {
    ;(createServerClient as any).mockReturnValue({
      auth: { getUser: async () => ({ data: { user: null } }) },
    })
    const req = createMockRequest('/profile/abc')
    const res = await middleware(req as any)
    expect(res.headers.get('location')).toBeNull()
  })

  it('allows authenticated user to access /account', async () => {
    ;(createServerClient as any).mockReturnValue({
      auth: { getUser: async () => ({ data: { user: { id: 'u1' } } }) },
    })
    const req = createMockRequest('/account')
    const res = await middleware(req as any)
    expect(res.headers.get('location')).toBeNull()
  })
})
