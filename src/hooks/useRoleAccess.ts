import { useAuth } from '@/features/auth/hooks/use-auth'
import { getPageAccess } from '@/libs/permissions/pageRoles'
import { usePathname } from 'next/navigation'

/**
 * Hook for fine-grained in-page access control.
 * Returns { editLevel, removeLevel } for the current user and page.
 * Optionally override page and role.
 */
export function useRoleAccess({
  page,
  role,
}: { page?: string; role?: string } = {}) {
  const { profile } = useAuth()
  const pathname = usePathname()

  // Determine role: explicit > profile > fallback
  const userRole =
    role ||
    (profile && typeof profile.role === 'string' ? profile.role : undefined)

  // Determine page: explicit > current path
  const currentPage = page || pathname

  if (!userRole || !currentPage) {
    return { editLevel: 0, removeLevel: 0 }
  }

  const allowedRoles = ['admin', 'grandmaster', 'athlete'] as const
  type Role = (typeof allowedRoles)[number]
  const narrowedRole = allowedRoles.includes(userRole as Role)
    ? (userRole as Role)
    : undefined

  const access =
    narrowedRole && currentPage
      ? getPageAccess(narrowedRole, currentPage)
      : { edit: 0, remove: 0 }

  return {
    editLevel: access.edit ?? 0,
    removeLevel: access.remove ?? 0,
  }
}

export default useRoleAccess
