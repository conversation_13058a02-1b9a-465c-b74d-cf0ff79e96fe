'use client'

import { type RefObject, useEffect, useRef, useState } from 'react'

export type UseIntersectionObserverOptions = {
  threshold?: number | number[]
  root?: Element | null
  rootMargin?: string
  /** If true, the observer will unobserve the target after the first intersection. */
  triggerOnce?: boolean
  /** If true, the intersecting state will remain true once the element has intersected, even if it scrolls out of view.
   *  Works with `triggerOnce` to unobserve after this first "freezing" intersection. */
  freezeOnceVisible?: boolean
}

/**
 * Custom hook to observe an element's intersection with the viewport or a specified root element.
 * @param options Configuration options for the IntersectionObserver.
 * @returns A tuple containing: a RefObject to attach to the target element, a boolean indicating if the element is intersecting, and the latest IntersectionObserverEntry.
 */
export function useIntersectionObserver<T extends Element>(
  options?: UseIntersectionObserverOptions,
): [RefObject<T | null>, boolean, IntersectionObserverEntry | undefined] {
  const {
    threshold = 0.1,
    root = null,
    rootMargin = '0px',
    triggerOnce = false,
    freezeOnceVisible = false,
  } = options || {}

  const targetRef = useRef<T>(null)
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry>()

  useEffect(() => {
    const currentElement = targetRef.current

    if (!currentElement || typeof IntersectionObserver === 'undefined') {
      return
    }

    const observer = new IntersectionObserver(
      ([currentEntry]) => {
        const currentlyVisible = currentEntry.isIntersecting

        if (freezeOnceVisible && currentlyVisible) {
          setIsIntersecting(true) // Freeze state to true
          setEntry(currentEntry)
          if (triggerOnce) {
            observer.unobserve(currentElement)
            observer.disconnect()
          }
        } else if (!freezeOnceVisible) {
          setIsIntersecting(currentlyVisible)
          setEntry(currentEntry)
          if (currentlyVisible && triggerOnce) {
            observer.unobserve(currentElement)
            observer.disconnect()
          }
        }
        // If !currentlyVisible and freezeOnceVisible is true, we do nothing, state remains true.
      },
      { threshold, root, rootMargin },
    )

    observer.observe(currentElement)

    return () => {
      observer.unobserve(currentElement)
      observer.disconnect()
    }
  }, [threshold, root, rootMargin, triggerOnce, freezeOnceVisible]) // targetRef itself is stable, not needed in deps.
  // The effect will re-run if the options change.
  // Caller should memoize options if they are complex objects/arrays that might change reference frequently.

  return [targetRef, isIntersecting, entry]
}

// Example Usage (for documentation purposes, not part of the hook file):
// const MyComponent = () => {
//   const [targetRef, isVisible, entry] = useIntersectionObserver<HTMLDivElement>({
//     threshold: 0.5,
//     triggerOnce: true,
//   });
//
//   useEffect(() => {
//     if (isVisible) {
//       console.log('Element is visible!', entry);
//     }
//   }, [isVisible, entry]);
//
//   return (
//     <div ref={targetRef} style={{ height: '100px', backgroundColor: 'lightgray' }}>
//       {isVisible ? 'Visible' : 'Not Visible'}
//     </div>
//   );
// };
