/// <reference types="vitest" />

import { renderHook } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useRoleAccess } from '../useRoleAccess'

// Mock the useAuth hook
vi.mock('@/features/auth/hooks/use-auth', () => ({
  useAuth: vi.fn(),
}))

// Module-level mock for next/navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
}))

import { useAuth } from '@/features/auth/hooks/use-auth'
import type { AuthContextType, UserRole } from '@/shared/types/auth'

describe('useRoleAccess', () => {
  const mockAuthContext = (role: string | undefined): AuthContextType => ({
    user: null,
    profile: role
      ? {
          id: 'test-id',
          email: '<EMAIL>',
          username: 'testuser',
          role: role as User<PERSON>ole,
          total_points: 0,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
        }
      : null,
    userRole: role as UserRole,
    loading: false,
    isAuthenticated: !!role,
    signInWithEmail: vi.fn(),
    signUpWithEmail: vi.fn(),
    signInWithSocial: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    updateProfile: vi.fn(),
    refreshProfile: vi.fn(),
  })

  beforeEach(() => {
    vi.restoreAllMocks()
    vi.mocked(usePathname).mockClear()
  })

  it('returns correct levels for admin on /profile', () => {
    vi.mocked(useAuth).mockReturnValue(mockAuthContext('admin'))
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 1 })
  })

  it('returns correct levels for grandmaster on /exercises', () => {
    vi.mocked(useAuth).mockReturnValue(mockAuthContext('grandmaster'))
    vi.mocked(usePathname).mockReturnValue('/exercises')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 0 })
  })

  it('returns correct levels for athlete on /submissions', () => {
    vi.mocked(useAuth).mockReturnValue(mockAuthContext('athlete'))
    vi.mocked(usePathname).mockReturnValue('/submissions')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 0, removeLevel: 0 })
  })

  it('returns {0,0} for missing role', () => {
    vi.mocked(useAuth).mockReturnValue(mockAuthContext(undefined))
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() => useRoleAccess())
    expect(result.current).toEqual({ editLevel: 0, removeLevel: 0 })
  })

  it('returns correct levels when page and role are passed explicitly', () => {
    vi.mocked(useAuth).mockReturnValue(mockAuthContext('athlete'))
    vi.mocked(usePathname).mockReturnValue('/profile')
    const { result } = renderHook(() =>
      useRoleAccess({ page: '/exercises', role: 'grandmaster' }),
    )
    expect(result.current).toEqual({ editLevel: 1, removeLevel: 0 })
  })
})
