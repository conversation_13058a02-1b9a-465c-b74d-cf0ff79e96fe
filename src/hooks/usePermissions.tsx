/**
 * usePermissions.tsx
 *
 * This hook provides permission checking functionality based on the user's role.
 * It uses the auth context to get the user's role and then checks permissions.
 */

import { useAuth } from '@/features/auth/hooks/use-auth'
import {
  hasAllPermissions,
  hasAnyPermission,
  hasAnyRole,
  hasPermission,
  hasRole,
  isAdmin,
  isGrandmasterOrAdmin,
  isOwnerOrHasPermission,
} from '@/libs/permissions/permissions'
import type { Permission } from '@/libs/permissions/types'

/**
 * Hook for checking user permissions
 * @returns Object with permission checking functions
 */
export function usePermissions() {
  const { profile } = useAuth()

  return {
    /**
     * Check if the user has a specific permission
     */
    can: (permission: Permission): boolean => {
      return hasPermission(profile, permission)
    },

    /**
     * Check if the user has any of the specified permissions
     */
    canAny: (permissions: Permission[]): boolean => {
      return hasAnyPermission(profile, permissions)
    },

    /**
     * Check if the user has all of the specified permissions
     */
    canAll: (permissions: Permission[]): boolean => {
      return hasAllPermissions(profile, permissions)
    },

    /**
     * Check if the user is the owner of a resource or has the specified permission
     */
    isOwnerOr: (
      resourceOwnerId: string | null | undefined,
      permission: Permission,
    ): boolean => {
      return isOwnerOrHasPermission(profile, resourceOwnerId, permission)
    },

    /**
     * Check if the user has a specific role
     */
    hasRole: (role: string): boolean => {
      return hasRole(profile, role)
    },

    /**
     * Check if the user has any of the specified roles
     */
    hasAnyRole: (roles: string[]): boolean => {
      return hasAnyRole(profile, roles)
    },

    /**
     * Check if the user is an admin
     */
    isAdmin: (): boolean => {
      return isAdmin(profile)
    },

    /**
     * Check if the user is a grandmaster or admin
     */
    isGrandmasterOrAdmin: (): boolean => {
      return isGrandmasterOrAdmin(profile)
    },
  }
}
