import { ThemeProvider } from '@/components/providers/theme-provider'
import { AuthContext, type AuthContextType } from '@/shared/libs/auth'
import { render } from '@testing-library/react'
import type React from 'react'

/**
 * TestAuthProvider: Provides a mock AuthContext for tests.
 */
export const TestAuthProvider = ({
  children,
  mock,
}: {
  children: React.ReactNode
  mock?: Partial<AuthContextType>
}) => (
  <AuthContext.Provider
    value={{
      user: null,
      profile: null,
      userRole: null,
      loading: false,
      isAuthenticated: false,
      signInWithEmail: async () => ({ success: false, error: null }),
      signUpWithEmail: async () => ({ success: false, error: null }),
      signInWithSocial: async () => ({ success: false, error: null }),
      signOut: async () => {},
      resetPassword: async () => ({ success: false, error: null }),
      updatePassword: async () => ({ success: false, error: null }),
      updateProfile: async () => ({ success: false, error: null }),
      refreshProfile: async () => {},
      ...(mock ?? {}),
    }}
  >
    {children}
  </AuthContext.Provider>
)

type RenderWithProvidersOptions = {
  authMock?: Partial<AuthContextType>
  theme?: string
}

/**
 * renderWithProviders: Wraps children with ThemeProvider and TestAuthProvider.
 * Pass options to override auth context or theme.
 */
export function renderWithProviders(
  ui: React.ReactElement,
  { authMock, theme = 'dark' }: RenderWithProvidersOptions = {},
) {
  return render(
    <ThemeProvider attribute="class" defaultTheme={theme} enableSystem={false}>
      <TestAuthProvider mock={authMock}>{ui}</TestAuthProvider>
    </ThemeProvider>,
  )
}
