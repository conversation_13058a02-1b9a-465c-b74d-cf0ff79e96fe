'use client'

import { ExerciseCard } from '@/components/features/exercises/ExerciseCard'
import {
  type UseIntersectionObserverOptions,
  useIntersectionObserver,
} from '@/hooks/useIntersectionObserver'
import type { Exercise } from '@/types/exercises'
import { useCallback, useEffect, useRef, useState } from 'react'

interface ObservedExerciseCardProps {
  exercise: Exercise
  onIntersectionChange: (
    id: string,
    entry: IntersectionObserverEntry | undefined,
    isIntersecting: boolean,
  ) => void
  isActive: boolean
  observerOptions: UseIntersectionObserverOptions
}

const ObservedExerciseCard = ({
  exercise,
  onIntersectionChange,
  isActive,
  observerOptions,
}: ObservedExerciseCardProps) => {
  const [targetRef, isIntersecting, entry] =
    useIntersectionObserver<HTMLDivElement>(observerOptions)

  useEffect(() => {
    // Report intersection change along with the entry
    onIntersectionChange(exercise.id, entry, isIntersecting)
  }, [isIntersecting, entry, onIntersectionChange, exercise.id])

  return (
    <div ref={targetRef} className="h-full">
      <ExerciseCard exercise={exercise} isActive={isActive} />
    </div>
  )
}

type FeaturedExercisesListProps = {
  exercises: Exercise[]
}

export function FeaturedExercisesList({
  exercises,
}: FeaturedExercisesListProps) {
  const [activeCardId, setActiveCardId] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const intersectingCardsRef = useRef<Map<string, IntersectionObserverEntry>>(
    new Map(),
  )

  useEffect(() => {
    const checkMobile = () => {
      const touchSupported =
        'ontouchstart' in window || navigator.maxTouchPoints > 0
      setIsMobile(touchSupported)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const updateActiveCard = useCallback(() => {
    if (!isMobile) {
      setActiveCardId(null) // No scroll-based active card on desktop
      return
    }

    let topmostIntersectingCardId: string | null = null
    let minTop = Number.POSITIVE_INFINITY

    intersectingCardsRef.current.forEach((entry, id) => {
      if (entry.boundingClientRect.top < minTop) {
        minTop = entry.boundingClientRect.top
        topmostIntersectingCardId = id
      }
    })
    setActiveCardId(topmostIntersectingCardId)
  }, [isMobile])

  const handleIntersectionChange = useCallback(
    (
      id: string,
      entry: IntersectionObserverEntry | undefined,
      isCurrentlyIntersecting: boolean,
    ) => {
      if (entry) {
        if (isCurrentlyIntersecting) {
          intersectingCardsRef.current.set(id, entry)
        } else {
          intersectingCardsRef.current.delete(id)
        }
        updateActiveCard()
      }
    },
    [updateActiveCard],
  )

  const observerOptions: UseIntersectionObserverOptions = {
    threshold: 0.5, // Card is considered for activation when 50% visible
  }

  if (exercises.length === 0) {
    return (
      <div className="col-span-full text-center py-10">
        <p className="text-muted-foreground">
          No featured exercises available at the moment. Check back soon!
        </p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {exercises.map((exercise) => (
        <ObservedExerciseCard
          key={exercise.id}
          exercise={exercise}
          onIntersectionChange={handleIntersectionChange}
          isActive={activeCardId === exercise.id} // isActive now driven by topmost logic
          observerOptions={observerOptions}
        />
      ))}
    </div>
  )
}
