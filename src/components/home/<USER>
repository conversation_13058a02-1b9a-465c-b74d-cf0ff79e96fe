import { Button } from '@/components/Button'
import { ExerciseCard } from '@/components/features/exercises/ExerciseCard'
import { createClient } from '@/libs/supabase/server'
import type { Exercise } from '@/types/exercises'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { FeaturedExercisesList } from './FeaturedExercisesList'

async function getFeaturedExercises(): Promise<Exercise[]> {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from('exercises')
      .select('id, title, description, video_tutorial_url')
      .limit(3)

    if (error) {
      console.error('Error fetching featured exercises:', error)
      return []
    }

    if (!data) {
      return []
    }

    return data.map((exercise) => {
      return {
        id: exercise.id,
        title: exercise.title,
        description: exercise.description,
        video_tutorial_url: exercise.video_tutorial_url,
        image_url: '/images/logo.webp',
      }
    })
  } catch (error) {
    console.error('Failed to fetch featured exercises:', error)
    return []
  }
}

export default async function ExercisesSection() {
  const exercises = await getFeaturedExercises()

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-foreground">
          Featured Exercises
        </h2>
        <FeaturedExercisesList exercises={exercises} />
        <div className="mt-8 text-center">
          <Button variant="secondary" size="lg" asChild>
            <Link href="/exercises">Explore All Exercises</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
