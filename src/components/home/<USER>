import { Button } from '@/components/Button'
import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import RankingsTable, {
  type RankingUser,
} from '@/components/rankings/RankingsTable'
import Link from 'next/link'

interface RankingPreviewCardProps {
  title: string
  rankings: RankingUser[]
  gender: 'male' | 'female'
}

export function RankingPreviewCard({
  title,
  rankings,
  gender,
}: RankingPreviewCardProps) {
  return (
    <Card className="overflow-hidden shadow-lg">
      <CardHeader className="bg-gray-50 dark:bg-gray-800">
        <CardTitle className="text-center">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <RankingsTable users={rankings} isCondensed={true} />
      </CardContent>
      <CardFooter className="flex justify-center p-4">
        <Link href={`/rankings?gender=${gender}`}>
          <Button variant="secondary">
            All {gender.charAt(0).toUpperCase() + gender.slice(1)}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}
