import { Button } from '@/components/ui/button'
import Link from 'next/link'

export const HeroSection = () => {
  return (
    <div className="relative min-h-[70vh] flex items-center justify-center bg-gradient-to-br from-gradient-start via-gradient-mid to-gradient-end dark:from-gradient-start dark:via-gradient-mid dark:to-gradient-end dark:text-white">
      <div className="w-full py-16 relative z-10 text-center text-white px-4 md:px-8">
        <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold mb-6 tracking-wider">
          ARM
          {/* <br className="md:hidden" /> */}
          WRESTLING POWER ARENA
        </h1>
        <h2 className="text-2xl md:text-3xl font-semibold mb-4">
          Showcase your armwrestling strength
        </h2>
        <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
          Join the ultimate platform for armwrestlers to track progress, compete
          globally, and earn recognition through our medal system. Submit your
          exercises, climb the rankings, and become a champion.
        </p>
        <Link href="/exercises">
          <Button size="lg" className="text-xl py-6 px-10 rounded-md">
            ENTER THE ARENA
          </Button>
        </Link>
      </div>
    </div>
  )
}
