import { Button } from '@/components/Button'
import { fetchFemaleRankings, fetchMaleRankings } from '@/libs/rankings'
import Link from 'next/link'
import { RankingPreviewCard } from './RankingPreviewCard'

export default async function RankingsSection() {
  // Fetch top 10 male and female athletes
  const [maleRankings, femaleRankings] = await Promise.all([
    fetchMaleRankings({ limit: 10 }),
    fetchFemaleRankings({ limit: 10 }),
  ])

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="w-full">
        <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">
          Global Rankings
        </h2>

        <div className="grid md:grid-cols-2 gap-8 max-w-7xl mx-auto px-4">
          <RankingPreviewCard
            title="Top Male Athletes"
            rankings={maleRankings}
            gender="male"
          />
          <RankingPreviewCard
            title="Top Female Athletes"
            rankings={femaleRankings}
            gender="female"
          />
        </div>

        <div className="flex justify-center mt-8">
          <Link href="/rankings">
            <Button variant="secondary" size="lg">
              View Full Rankings
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
