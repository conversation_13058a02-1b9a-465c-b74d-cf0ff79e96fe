import { Button } from '@/components/Button'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/Card'
import type { MedalType } from '@/components/medals/MedalIcon'
import { formatDistance } from 'date-fns'
import Link from 'next/link'
import React from 'react'

export interface Submission {
  id: string
  exercise_title: string
  video_url: string
  weight_lifted: number
  status: 'pending' | 'approved' | 'rejected'
  submitted_at: string
  evaluated_at?: string | null
  points_awarded?: number | null
  medal_awarded?: MedalType | 'none' | null
  private_comments?: string | null
}

interface SubmissionHistoryProps {
  submissions: Submission[]
  isCurrentUser?: boolean
  title?: string
  emptyMessage?: string
}

const SubmissionHistory = ({
  submissions,
  isCurrentUser = false,
  title = 'Submission History',
  emptyMessage = 'No submissions yet.',
}: SubmissionHistoryProps) => {
  // Helper function to get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  // Helper function to get medal color
  const getMedalColor = (type?: MedalType | 'none' | null) => {
    if (!type || type === 'none') return ''

    switch (type) {
      case 'bronze':
        return 'bg-gradient-to-r from-amber-700 to-yellow-600'
      case 'silver':
        return 'bg-gradient-to-r from-gray-400 to-gray-300'
      case 'gold':
        return 'bg-gradient-to-r from-yellow-500 to-yellow-300'
      case 'platinum':
        return 'bg-gradient-to-r from-gray-300 to-white'
      case 'diamond':
        return 'bg-gradient-to-r from-blue-400 to-purple-500'
      default:
        return 'bg-gray-200'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{title}</CardTitle>
        {isCurrentUser && (
          <Button asChild variant="outline" size="sm">
            <Link href="/exercises">New Submission</Link>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {submissions.length > 0 ? (
          <div className="space-y-4">
            {submissions.map((submission) => (
              <div
                key={submission.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex flex-wrap justify-between items-start gap-2 mb-2">
                  <h4 className="font-medium">{submission.exercise_title}</h4>
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${getStatusBadgeClass(submission.status)}`}
                    >
                      {submission.status.charAt(0).toUpperCase() +
                        submission.status.slice(1)}
                    </span>
                    {submission.medal_awarded &&
                      submission.medal_awarded !== 'none' && (
                        <span
                          className={`text-xs px-2 py-1 rounded-full ${getMedalColor(submission.medal_awarded)} text-white`}
                        >
                          {submission.medal_awarded}
                        </span>
                      )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
                  <div>
                    <span className="text-gray-500">Weight: </span>
                    <span className="font-medium">
                      {submission.weight_lifted}kg
                    </span>
                  </div>
                  {submission.points_awarded !== null &&
                    submission.points_awarded !== undefined && (
                      <div>
                        <span className="text-gray-500">Points: </span>
                        <span className="font-medium">
                          +{submission.points_awarded}
                        </span>
                      </div>
                    )}
                  <div>
                    <span className="text-gray-500">Submitted: </span>
                    <span>
                      {formatDistance(
                        new Date(submission.submitted_at),
                        new Date(),
                        { addSuffix: true },
                      )}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <Button asChild size="sm" variant="outline">
                    <a
                      href={submission.video_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Watch Video
                    </a>
                  </Button>

                  {submission.private_comments && (
                    <div className="text-xs italic text-gray-500">
                      "{submission.private_comments}"
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>{emptyMessage}</p>
            {isCurrentUser && (
              <Button asChild className="mt-4">
                <Link href="/exercises">Submit Your First Exercise</Link>
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default SubmissionHistory
