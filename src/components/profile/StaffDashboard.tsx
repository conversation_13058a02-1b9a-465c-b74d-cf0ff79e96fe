import { Button } from '@/components/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/Card'
import Link from 'next/link'
import React from 'react'

interface StaffDashboardProps {
  role: string
  className?: string
}

const StaffDashboard = ({ role, className = '' }: StaffDashboardProps) => {
  if (role !== 'admin' && role !== 'grandmaster') {
    return null
  }

  return (
    <Card className={`bg-blue-50 dark:bg-blue-900/20 ${className}`}>
      <CardHeader>
        <CardTitle>
          {role === 'admin' ? 'Admin' : 'Grandmaster'} Dashboard
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {role === 'admin' && (
            <>
              <Button asChild variant="default" size="lg">
                <Link href="/admin">Admin Dashboard</Link>
              </Button>

              <Button asChild variant="secondary" size="lg">
                <Link href="/admin/users">Manage Users</Link>
              </Button>

              <Button asChild variant="secondary" size="lg">
                <Link href="/admin/exercises">Manage Exercises</Link>
              </Button>
            </>
          )}

          <Button asChild variant="default" size="lg">
            <Link href="/submissions?status=pending">Review Submissions</Link>
          </Button>

          <Button asChild variant="secondary" size="lg">
            <Link href="/statistics">View Statistics</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default StaffDashboard
