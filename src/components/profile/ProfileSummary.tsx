import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/Card'
import React from 'react'
import MedalIcon, { MedalType } from '../medals/MedalIcon'

export interface UserProfile {
  id: string
  name: string
  profile_image?: string
  country?: string
  gender?: string
  weight_category?: string
  role: string
  total_points: number
  created_at: string
  titles?: string[]
  social_links?: Record<string, string>
}

interface MedalCounts {
  bronze: number
  silver: number
  gold: number
  platinum: number
  diamond: number
}

interface ProfileSummaryProps {
  user: UserProfile
  medalCounts: MedalCounts
}

const ProfileSummary = ({ user, medalCounts }: ProfileSummaryProps) => {
  // Format date for display
  const joinedDate = new Date(user.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
  })

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* User Info Card */}
      <div className="md:w-1/3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {user.name}
              {user.role !== 'athlete' && (
                <span className="text-xs px-2 py-1 bg-blue-500 text-white rounded-full">
                  {user.role === 'grandmaster' ? 'Grandmaster' : 'Admin'}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.profile_image ? (
              <div className="w-32 h-32 mx-auto rounded-full overflow-hidden">
                <img
                  src={user.profile_image}
                  alt={user.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto flex items-center justify-center text-4xl uppercase">
                {user.name.charAt(0)}
              </div>
            )}

            <div className="space-y-2 pt-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Country:
                </span>
                <span>{user.country || 'Not specified'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Gender:
                </span>
                <span>{user.gender || 'Not specified'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Weight Category:
                </span>
                <span>{user.weight_category || 'Not specified'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Total Points:
                </span>
                <span className="font-bold">{user.total_points || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Member Since:
                </span>
                <span>{joinedDate}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistics and Details */}
      <div className="md:w-2/3">
        <Card>
          <CardHeader>
            <CardTitle>Profile Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Medals Summary */}
            <div>
              <h3 className="text-lg font-medium mb-3">Medals Summary</h3>
              <div className="grid grid-cols-5 gap-4">
                {(
                  ['diamond', 'platinum', 'gold', 'silver', 'bronze'] as const
                ).map((type) => (
                  <MedalIcon
                    key={type}
                    type={type}
                    count={medalCounts[type]}
                    showLabel
                  />
                ))}
              </div>
            </div>

            {/* Titles */}
            {user.titles && user.titles.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Titles</h3>
                <div className="flex flex-wrap gap-2">
                  {user.titles.map((title, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900 rounded-full text-sm"
                    >
                      {title}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Social Links */}
            {user.social_links && Object.keys(user.social_links).length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Social Links</h3>
                <div className="space-y-2">
                  {Object.entries(user.social_links).map(([platform, link]) => (
                    <div key={platform} className="flex items-center gap-2">
                      <span className="capitalize">{platform}:</span>
                      <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {link}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ProfileSummary
