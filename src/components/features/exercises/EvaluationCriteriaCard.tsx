'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/libs/utils'
import { ClipboardList, Mars, Venus } from 'lucide-react'
import Image from 'next/image'
import { type FC, useMemo, useState } from 'react'

// Type for the JSON-based criteria format
type MedalThresholds = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
type WeightCategory = 'women' | 'men_95' | 'men_95+'
type JsonCriteria = Partial<
  Record<MedalThresholds, Partial<Record<WeightCategory, string>>>
>

type EvaluationCriteriaCardProps = {
  criteria: string | JsonCriteria // Can be a JSON string or a pre-parsed object
  className?: string
}

const medalOrder: MedalThresholds[] = [
  'bronze',
  'silver',
  'gold',
  'platinum',
  'diamond',
]

const medalDisplayInfo = [
  {
    name: 'bronze',
    tooltip: 'Bronze',
    highlightClass: 'bg-amber-200 dark:bg-amber-900/70',
  },
  {
    name: 'silver',
    tooltip: 'Silver',
    highlightClass: 'bg-slate-300 dark:bg-slate-900/70',
  },
  {
    name: 'gold',
    tooltip: 'Gold',
    highlightClass: 'bg-yellow-200 dark:bg-yellow-900/70',
  },
  {
    name: 'platinum',
    tooltip: 'Platinum',
    highlightClass: 'bg-slate-300 dark:bg-slate-900/70',
  },
  {
    name: 'diamond',
    tooltip: 'Diamond',
    highlightClass: 'bg-sky-200 dark:bg-sky-900/70',
  },
]

export const EvaluationCriteriaCard: FC<EvaluationCriteriaCardProps> = ({
  criteria,
  className,
}) => {
  const [hoveredMedalIndex, setHoveredMedalIndex] = useState<number | null>(
    null,
  )

  const parsedJsonCriteria: JsonCriteria = useMemo(() => {
    if (typeof criteria === 'string') {
      try {
        const parsed = JSON.parse(criteria)
        if (typeof parsed === 'object' && parsed !== null) {
          return parsed
        }
      } catch (error) {
        console.error('Failed to parse criteria JSON:', error)
        return {}
      }
    }
    return criteria
  }, [criteria])

  const hasCriteria = Object.keys(parsedJsonCriteria).length > 0

  return (
    <TooltipProvider delayDuration={0}>
      <Card
        className={`overflow-hidden transition-shadow duration-300 gap-2 py-2 hover:shadow-xl ${className}`}
      >
        <CardHeader className="px-4 py-3">
          <div className="flex flex-row items-center gap-2">
            <ClipboardList className="h-6 w-6" />
            <CardTitle className="text-xl">Evaluation Criteria</CardTitle>
          </div>
          <p className="text-base text-muted-foreground">
            The kilograms you need to lift to earn a medal in each category.
          </p>
        </CardHeader>
        <CardContent className="px-4 pb-4 pt-2">
          {hasCriteria ? (
            <div className="font-sans">
              {/* Header */}
              <div className="flex items-center px-3 py-2">
                <div className="w-[30%] text-xs font-bold uppercase text-muted-foreground tracking-wider">
                  Category
                </div>
                <div className="flex flex-1 justify-around">
                  {medalDisplayInfo.map((medal, index) => (
                    <div
                      key={medal.name}
                      className="flex flex-1 justify-center"
                      onMouseEnter={() => setHoveredMedalIndex(index)}
                      onMouseLeave={() => setHoveredMedalIndex(null)}
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Image
                            src={`/images/medals/${medal.name}.png`}
                            alt={medal.tooltip}
                            width={40}
                            height={40}
                            className={cn(
                              'h-10 w-10 object-contain transition-transform duration-200 cursor-pointer',
                              hoveredMedalIndex === index && 'scale-150',
                            )}
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{medal.tooltip}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center rounded-lg p-3 bg-pink-50 dark:bg-pink-900/20 text-pink-800 dark:text-pink-200">
                  <div className="w-[30%] flex items-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Venus className="h-5 w-5 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Women</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex flex-1 justify-around text-center">
                    {medalOrder.map((medal, index) => (
                      <div
                        key={medal}
                        className={cn(
                          'flex-1 font-semibold py-1 rounded-md transition-colors duration-200',
                          hoveredMedalIndex === index &&
                            medalDisplayInfo[index].highlightClass,
                        )}
                      >
                        {parsedJsonCriteria[medal]?.women || '-'}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex items-center rounded-lg p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200">
                  <div className="w-[30%] flex items-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-2 cursor-pointer">
                          <Mars className="h-5 w-5" />
                          <span className="text-sm font-medium whitespace-nowrap">
                            &lt; 95kg
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Men under 95kg</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex flex-1 justify-around text-center">
                    {medalOrder.map((medal, index) => (
                      <div
                        key={medal}
                        className={cn(
                          'flex-1 font-semibold py-1 rounded-md transition-colors duration-200',
                          hoveredMedalIndex === index &&
                            medalDisplayInfo[index].highlightClass,
                        )}
                      >
                        {parsedJsonCriteria[medal]?.men_95 || '-'}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex items-center rounded-lg p-3 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-800 dark:text-indigo-200">
                  <div className="w-[30%] flex items-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-2 cursor-pointer">
                          <Mars className="h-5 w-5" />
                          <span className="text-sm font-medium whitespace-nowrap">
                            &gt; 95kg
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Men over 95kg</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex flex-1 justify-around text-center">
                    {medalOrder.map((medal, index) => (
                      <div
                        key={medal}
                        className={cn(
                          'flex-1 font-semibold py-1 rounded-md transition-colors duration-200',
                          hoveredMedalIndex === index &&
                            medalDisplayInfo[index].highlightClass,
                        )}
                      >
                        {parsedJsonCriteria[medal]?.['men_95+'] || '-'}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-600 dark:text-gray-400">
              No evaluation criteria available.
            </p>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  )
}
