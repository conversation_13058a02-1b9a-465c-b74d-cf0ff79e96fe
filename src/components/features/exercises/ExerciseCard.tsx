import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '@/components/ui/card' // Assuming ShadCN Card is in ui
import { cn } from '@/libs/utils'
import type { Exercise } from '@/types/exercises'
import Image from 'next/image' // Using next/image for optimization
import Link from 'next/link'

type ExerciseCardProps = {
  exercise: Exercise
  className?: string
  isActive?: boolean // Added isActive prop
}

export function ExerciseCard({
  exercise,
  className,
  isActive,
}: ExerciseCardProps) {
  // On mobile, isActive will drive the styles. On desktop, group-hover will.
  // We can use data-attributes to control mobile-specific active states if needed,
  // or rely on Tailwind's responsive prefixes if isActive is only true on mobile.
  // For now, we assume isActive is only true on mobile due to parent logic.

  const cardActiveStyles = isActive
    ? 'border-primary scale-105 shadow-xl dark:shadow-2xl dark:shadow-primary/20'
    : ''
  const imageActiveStyles = isActive ? 'opacity-70 blur-[1px]' : 'blur-sm' // Mobile active image: less blur, defined opacity
  const titleActiveStyles = isActive ? 'text-primary' : ''

  return (
    <Link
      href={`/exercises/${exercise.id}`}
      className={cn(
        'block rounded-lg overflow-hidden h-full group focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background',
        className,
      )}
      aria-label={`View details for ${exercise.title}`}
    >
      <Card
        className={cn(
          'h-full flex flex-col border border-border',
          // Desktop hover effects (will not apply if isActive is true and overrides them, or use md: prefix)
          'transition-all duration-300 ease-in-out',
          'group-hover:border-primary group-focus-within:border-primary md:group-hover:scale-105 md:group-hover:shadow-xl md:dark:group-hover:shadow-2xl md:dark:group-hover:shadow-primary/20 md:focus-within:scale-105 md:focus-within:shadow-xl md:dark:focus-within:shadow-2xl md:dark:focus-within:shadow-primary/20',
          cardActiveStyles, // Mobile active styles based on isActive prop
        )}
      >
        <div className="relative h-56 w-full overflow-hidden bg-muted/30 dark:bg-muted/50">
          {exercise.image_url ? (
            <Image
              src={exercise.image_url}
              alt={`Preview for ${exercise.title}`}
              fill
              className={cn(
                'transition duration-300 object-contain p-4',
                // Desktop hover effects for image
                'group-hover:opacity-70 group-hover:blur-[1px]',
                imageActiveStyles, // Mobile active styles for image
              )}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700 to-gray-900">
              <span className="text-white text-lg font-medium">
                Exercise Preview
              </span>
            </div>
          )}
        </div>
        <CardHeader className="pb-3 pt-4">
          <CardTitle
            className={cn(
              'text-xl font-semibold transition-colors',
              'group-hover:text-primary',
              'flex items-center justify-center',
              titleActiveStyles,
            )}
          >
            {exercise.title}
          </CardTitle>
          {/* <CardDescription className="text-sm line-clamp-3 h-[3.75rem]">
            {exercise.description}
          </CardDescription> */}
        </CardHeader>
        {/* <CardFooter className="mt-auto pt-3 pb-4">
          <span className="text-sm font-medium text-foreground/80 group-hover:text-secondary group-hover:underline focus:text-secondary focus:underline transition-colors">
            View Details
          </span>
        </CardFooter> */}
      </Card>
    </Link>
  )
}
