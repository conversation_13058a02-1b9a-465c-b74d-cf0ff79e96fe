import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { ReactNode } from 'react'

type ExerciseDetailCardProps = {
  title: string
  content: ReactNode
  icon?: ReactNode
  className?: string
}

/**
 * ExerciseDetailCard is a reusable component to display a card with a title and content section.
 * It is used on the exercise detail page to show information like description, equipment, and criteria.
 */
export const ExerciseDetailCard = ({
  title,
  content,
  icon,
  className,
}: ExerciseDetailCardProps) => {
  return (
    <Card
      className={`overflow-hidden transition-shadow duration-300  gap-3 py-3 hover:shadow-xl ${className}`}
    >
      <CardHeader className="px-4 pt-2 pb-1">
        <div className="flex flex-row items-center gap-2">
          {icon}
          <CardTitle className="text-xl">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-0">{content}</CardContent>
    </Card>
  )
}
