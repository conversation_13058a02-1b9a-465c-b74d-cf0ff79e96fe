import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { GoToSubmitButton } from '@/features/submissions/GoToSubmitButton'
import type { ReactNode } from 'react'

type SubmitPerformanceCardProps = {
  exerciseId: string
  exerciseTitle: string
  icon?: ReactNode
}

/**
 * SubmitPerformanceCard is a dedicated component for the exercise submission call-to-action.
 * It displays a card prompting the user to submit their performance for a given exercise.
 */
export const SubmitPerformanceCard = ({
  exerciseId,
  exerciseTitle,
  icon,
}: SubmitPerformanceCardProps) => {
  return (
    <Card className="overflow-hidden transition-shadow gap-2 duration-300 hover:shadow-xl bg-card border-primary/20">
      <CardHeader className="px-4 pt-2 pb-1">
        <div>
          <div className="flex flex-row items-center gap-2">
            {icon}
            <CardTitle className="text-xl font-bold text-card-foreground">
              Submit Your Performance
            </CardTitle>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-4">
        <GoToSubmitButton
          exerciseId={exerciseId}
          exerciseTitle={exerciseTitle}
          className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold"
        />
      </CardContent>
    </Card>
  )
}
