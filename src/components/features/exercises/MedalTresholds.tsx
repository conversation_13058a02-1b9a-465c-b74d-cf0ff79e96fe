import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import Image from 'next/image'

type MedalType = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

type MedalThresholdsProps = {
  thresholds?: Record<string, Record<string, number>>
}

export const MedalThresholds = ({ thresholds }: MedalThresholdsProps) => {
  // Default thresholds if none provided
  const defaultThresholds = {
    men: {
      bronze: 0,
      silver: 0,
      gold: 0,
      platinum: 0,
      diamond: 0,
    },
    women: {
      bronze: 0,
      silver: 0,
      gold: 0,
      platinum: 0,
      diamond: 0,
    },
  }

  // Use provided thresholds or defaults
  const medalData = thresholds || defaultThresholds

  const medalTypes: Array<{
    name: MedalType
    image: string
    title: string
    colorClass?: string
  }> = [
    {
      name: 'bronze',
      image: '/images/medals/bronze.png',
      title: 'Bronze',
      colorClass: 'text-[hsl(var(--medal-bronze))]',
    },
    {
      name: 'silver',
      image: '/images/medals/silver.png',
      title: 'Silver',
      colorClass: 'text-[hsl(var(--medal-silver))]',
    },
    {
      name: 'gold',
      image: '/images/medals/gold.png',
      title: 'Gold',
      colorClass: 'text-[hsl(var(--medal-gold))]',
    },
    {
      name: 'platinum',
      image: '/images/medals/platinum.png',
      title: 'Platinum',
      colorClass: 'text-[hsl(var(--medal-platinum))]',
    },
    {
      name: 'diamond',
      image: '/images/medals/diamond.png',
      title: 'Diamond',
      colorClass: 'text-[hsl(var(--medal-diamond))]',
    },
  ]

  const getMedalValue = (category: string, medalName: string): number => {
    const categoryData = medalData[category as keyof typeof medalData]
    if (categoryData && typeof categoryData === 'object') {
      return (categoryData as Record<string, number>)[medalName] || 0
    }
    return 0
  }

  return (
    <TooltipProvider delayDuration={200}>
      <div className="w-full rounded-xl bg-card p-6 shadow-xl border border-border overflow-hidden">
        <h2 className="mb-8 text-center text-2xl font-bold text-card-foreground font-heading">
          Medal Thresholds
        </h2>

        {/* Enhanced Medals Gallery with Tooltips */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-8 sm:gap-x-6 sm:gap-y-10 items-start justify-center">
          {medalTypes.map((medal) => {
            const menValue =
              getMedalValue('men', medal.name) ||
              getMedalValue('male', medal.name)
            const womenValue =
              getMedalValue('women', medal.name) ||
              getMedalValue('female', medal.name)
            const tooltipContent = `${medal.title}: Men - ${menValue}, Women - ${womenValue}`

            return (
              <Tooltip key={`medal-${medal.name}`}>
                <TooltipTrigger asChild>
                  <div className="flex flex-col items-center text-center cursor-default group">
                    <div className="relative w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 transition-transform group-hover:scale-110 duration-200 ease-in-out mb-3">
                      <Image
                        src={medal.image}
                        alt={`${medal.title} medal`}
                        fill
                        className="object-contain"
                        priority={medal.name === 'gold'}
                      />
                    </div>
                    <h4
                      className={`text-lg sm:text-xl font-semibold mb-1 ${medal.colorClass || 'text-card-foreground'}`}
                    >
                      {medal.title}
                    </h4>
                    <p className="text-xs sm:text-sm text-muted-foreground">
                      M: {menValue} / W: {womenValue}
                    </p>
                  </div>
                </TooltipTrigger>
                {/* <TooltipContent className="bg-popover text-popover-foreground border-border shadow-md">
                  <p>{tooltipContent}</p>
                </TooltipContent> */}
              </Tooltip>
            )
          })}
        </div>
      </div>
    </TooltipProvider>
  )
}
