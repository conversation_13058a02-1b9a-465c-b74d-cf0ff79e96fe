'use client'

import { <PERSON><PERSON> } from '@/components/Button'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from '@/components/Card'
import { AlertTriangle } from 'lucide-react'
import * as React from 'react'

interface ErrorBoundaryProps {
  children: React.ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div
          className="flex flex-col items-center justify-center min-h-[300px] p-6 bg-background rounded-lg shadow-md border border-destructive/50"
          role="alert"
        >
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-destructive mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-primary mb-2">
              Oops! Something went wrong.
            </h2>
            <p className="text-muted-foreground mb-4">
              An error occurred while loading this content.
            </p>
            {this.state.error && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-[200px] text-sm font-mono">
                {this.state.error.message}
              </div>
            )}
          </div>
          <CardFooter>
            <Button
              onClick={() => {
                this.setState({ hasError: false, error: null })
                window.location.reload()
              }}
            >
              Try again
            </Button>
          </CardFooter>
        </div>
      )
    }

    return this.props.children
  }
}
