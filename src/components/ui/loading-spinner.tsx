import { cn } from '@/libs/utils'
import * as React from 'react'
import type { SVGProps } from 'react'

interface LoadingSpinnerProps extends SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

export const LoadingSpinner = ({
  size = 24,
  color = 'currentColor',
  className,
  ...props
}: LoadingSpinnerProps) => {
  const spinnerClasses = cn(
    'animate-spin rounded-full border-solid border-primary border-t-transparent',
    className,
  )

  return (
    <div
      className={cn('flex items-center justify-center', className)}
      {...props}
    >
      <div className={spinnerClasses} />
    </div>
  )
}

export function LoadingPage() {
  return (
    <div className="flex items-center justify-center min-h-[400px] w-full">
      <LoadingSpinner size={48} />
    </div>
  )
}
