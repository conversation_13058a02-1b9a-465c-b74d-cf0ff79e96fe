import { cn } from '@/libs/utils'
import { <PERSON><PERSON><PERSON>, <PERSON>Up, Minus } from 'lucide-react'
import { UserIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

export type RankingUser = {
  id: string
  name: string
  profileImage?: string
  country: string
  role: 'athlete' | 'grandmaster' | 'admin'
  gender: 'male' | 'female' | 'other'
  weightCategory: 'under_95kg' | 'over_95kg'
  totalPoints: number
  previousRank?: number
  bronzeMedals: number
  silverMedals: number
  goldMedals: number
  platinumMedals: number
  diamondMedals: number
}

type RankingsTableProps = {
  users: RankingUser[]
  isCondensed?: boolean
  className?: string
}

export default function RankingsTable({
  users,
  isCondensed = false,
  className,
}: RankingsTableProps) {
  return (
    <div className={cn('w-full overflow-x-auto', className)}>
      <table className="w-full">
        <thead className="bg-gray-100 dark:bg-gray-700">
          <tr>
            <th className="py-3 px-4 text-left font-medium">Rank</th>
            <th className="py-3 px-4 text-left font-medium">Athlete</th>
            {!isCondensed && (
              <th className="py-3 px-4 text-left font-medium">Country</th>
            )}
            <th className="py-3 px-4 text-left font-medium">Medals</th>
            <th className="py-3 px-4 text-right font-medium">Points</th>
          </tr>
        </thead>
        <tbody>
          {users.length === 0 ? (
            <tr>
              <td
                colSpan={isCondensed ? 3 : 5}
                className="py-4 px-4 text-center"
              >
                No rankings available at the moment.
              </td>
            </tr>
          ) : (
            users.map((user, index) => (
              <tr
                key={user.id}
                className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <td className="py-4 px-4">
                  <div className="flex items-center">
                    <span className="font-bold mr-2">{index + 1}</span>
                    {user.previousRank && (
                      <span>
                        {user.previousRank > index + 1 ? (
                          <ArrowUp className="h-4 w-4 text-green-500" />
                        ) : user.previousRank < index + 1 ? (
                          <ArrowDown className="h-4 w-4 text-red-500" />
                        ) : (
                          <Minus className="h-4 w-4 text-gray-400" />
                        )}
                      </span>
                    )}
                  </div>
                </td>
                <td className="py-4 px-4">
                  <div className="flex items-center">
                    {user.profileImage ? (
                      <div className="w-12 h-12 rounded-full mr-3 flex-shrink-0 overflow-hidden">
                        <Image
                          src={user.profileImage}
                          alt={user.name}
                          width={48}
                          height={48}
                          className="rounded-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-300 mr-3 flex-shrink-0 overflow-hidden border-2 border-primary">
                        <UserIcon className="w-full h-full text-gray-500" />
                      </div>
                    )}
                    <div>
                      <Link
                        href={`/profile/${user.id}`}
                        className="font-medium hover:text-primary"
                      >
                        {user.name}
                      </Link>
                      {!isCondensed && (
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <RoleBadge role={user.role} />
                          <span>
                            {user.weightCategory === 'under_95kg'
                              ? 'Under 95kg'
                              : 'Over 95kg'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                {!isCondensed && <td className="py-4 px-4">{user.country}</td>}
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-2">
                    {user.diamondMedals > 0 && (
                      <div className="flex items-center">
                        <span className="inline-block w-5 h-5 rounded-full bg-[#B9F2FF] mr-1" />
                        <span>{user.diamondMedals}</span>
                      </div>
                    )}
                    {user.platinumMedals > 0 && (
                      <div className="flex items-center">
                        <span className="inline-block w-5 h-5 rounded-full bg-[#E5E4E2] mr-1" />
                        <span>{user.platinumMedals}</span>
                      </div>
                    )}
                    {user.goldMedals > 0 && (
                      <div className="flex items-center">
                        <span className="inline-block w-5 h-5 rounded-full bg-[#FFD700] mr-1" />
                        <span>{user.goldMedals}</span>
                      </div>
                    )}
                    {user.silverMedals > 0 && (
                      <div className="flex items-center">
                        <span className="inline-block w-5 h-5 rounded-full bg-[#C0C0C0] mr-1" />
                        <span>{user.silverMedals}</span>
                      </div>
                    )}
                    {user.bronzeMedals > 0 && (
                      <div className="flex items-center">
                        <span className="inline-block w-5 h-5 rounded-full bg-[#CD7F32] mr-1" />
                        <span>{user.bronzeMedals}</span>
                      </div>
                    )}
                    {user.diamondMedals === 0 &&
                      user.platinumMedals === 0 &&
                      user.goldMedals === 0 &&
                      user.silverMedals === 0 &&
                      user.bronzeMedals === 0 && (
                        <span className="text-gray-400">No medals</span>
                      )}
                  </div>
                </td>
                <td className="py-4 px-4 text-right font-bold">
                  {user.totalPoints.toLocaleString()}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  )
}

function RoleBadge({ role }: { role: 'athlete' | 'grandmaster' | 'admin' }) {
  const badgeClasses = {
    athlete: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    grandmaster:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  }

  return (
    <span
      className={cn(
        'px-2 py-1 rounded-full text-xs font-medium',
        badgeClasses[role],
      )}
    >
      {role.charAt(0).toUpperCase() + role.slice(1)}
    </span>
  )
}
