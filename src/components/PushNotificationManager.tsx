'use client'

import { sendNotification, subscribeUser, unsubscribeUser } from '@/app/actions'
import { Button } from '@/components/Button'
import { Bell, BellOff, Send } from 'lucide-react'
import { useEffect, useState } from 'react'

function urlBase64ToUint8Array(base64String: string) {
  if (!base64String) return new Uint8Array()

  const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }
  return outputArray
}

export function PushNotificationManager() {
  const [isSupported, setIsSupported] = useState(false)
  const [subscription, setSubscription] = useState<PushSubscription | null>(
    null,
  )
  const [message, setMessage] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // Check if push notifications are supported
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      setIsSupported(true)
      checkSubscriptionStatus()
    }
  }, [])

  async function checkSubscriptionStatus() {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      setSubscription(subscription)
    } catch (error) {
      console.error('Error checking subscription status:', error)
    }
  }

  async function handleSubscribe() {
    try {
      setLoading(true)

      // This would use an actual VAPID key in production
      const dummyVapidKey =
        'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U'

      const registration = await navigator.serviceWorker.ready
      const newSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || dummyVapidKey,
        ),
      })

      setSubscription(newSubscription)

      // Save the subscription on the server
      await subscribeUser(JSON.parse(JSON.stringify(newSubscription)))
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  async function handleUnsubscribe() {
    try {
      setLoading(true)

      if (subscription) {
        const endpoint = subscription.endpoint
        await subscription.unsubscribe()
        setSubscription(null)

        // Remove the subscription from the server
        await unsubscribeUser(endpoint)
      }
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  async function handleSendNotification() {
    if (!message.trim()) return

    try {
      setLoading(true)
      await sendNotification(message)
      setMessage('')
    } catch (error) {
      console.error('Error sending notification:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isSupported) {
    return null
  }

  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Notifications</h3>

        {subscription ? (
          <Button
            onClick={handleUnsubscribe}
            disabled={loading}
            variant="outline"
            className="text-sm"
          >
            <BellOff className="h-4 w-4 mr-2" />
            Unsubscribe
          </Button>
        ) : (
          <Button
            onClick={handleSubscribe}
            disabled={loading}
            variant="outline"
            className="text-sm"
          >
            <Bell className="h-4 w-4 mr-2" />
            Subscribe
          </Button>
        )}
      </div>

      {subscription && (
        <div className="flex gap-2 mt-3">
          <input
            type="text"
            placeholder="Enter notification message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="flex-1 px-3 py-1.5 rounded border border-gray-300 dark:border-gray-700 bg-transparent focus:outline-none focus:ring-1 focus:border-blue-500"
          />
          <Button
            onClick={handleSendNotification}
            disabled={loading || !message.trim()}
            size="sm"
          >
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
        </div>
      )}
    </div>
  )
}
