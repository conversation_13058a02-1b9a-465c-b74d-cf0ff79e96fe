/**
 * ProtectedRoute.tsx
 *
 * This component protects routes that require authentication or specific permissions.
 * It redirects unauthenticated users to the login page and unauthorized users to an error page.
 */

'use client'

import { useAuth } from '@/features/auth/hooks/use-auth'
import { usePermissions } from '@/hooks/usePermissions'
import type { Permission } from '@/libs/permissions/types'
import { useRouter } from 'next/navigation'
import { type ReactNode, useEffect } from 'react'

interface ProtectedRouteProps {
  children: ReactNode
  requiredPermission?: Permission
  requiredPermissions?: Permission[]
  requireAllPermissions?: boolean
  fallback?: ReactNode
}

export function ProtectedRoute({
  children,
  requiredPermission,
  requiredPermissions = [],
  requireAllPermissions = false,
  fallback,
}: ProtectedRouteProps) {
  const { isAuthenticated, loading } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // Add single permission to array if provided
  if (requiredPermission) {
    requiredPermissions = [...requiredPermissions, requiredPermission]
  }

  // Check if user has required permissions
  const hasRequiredPermissions =
    requiredPermissions.length === 0
      ? true
      : requireAllPermissions
        ? permissions.canAll(requiredPermissions)
        : permissions.canAny(requiredPermissions)

  useEffect(() => {
    // Wait until auth is loaded
    if (loading) return

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    // If permissions are required but user doesn't have them,
    // redirect to unauthorized page if no fallback is provided
    if (
      requiredPermissions.length > 0 &&
      !hasRequiredPermissions &&
      !fallback
    ) {
      router.push('/unauthorized')
    }
  }, [
    isAuthenticated,
    loading,
    hasRequiredPermissions,
    router,
    requiredPermissions.length,
  ])

  // Show nothing while loading
  if (loading) {
    return null
  }

  // Show fallback if user doesn't have required permissions
  if (requiredPermissions.length > 0 && !hasRequiredPermissions && fallback) {
    return <>{fallback}</>
  }

  // If authenticated and has permissions (or no permissions required), show children
  return isAuthenticated ? <>{children}</> : null
}
