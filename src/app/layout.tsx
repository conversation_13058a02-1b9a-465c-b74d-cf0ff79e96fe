import { Toaster } from '@/components/ui/sonner'
import '@/styles/globals.css'
import { DumbbellIcon, HomeIcon, UploadIcon } from 'lucide-react'
import type { Metadata } from 'next'
import { <PERSON> } from 'next/font/google'

import { Footer } from '@/components/Footer'
import { PWAInstallPrompt } from '@/components/PWAInstallPrompt'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { AuthDialogHandler } from '@/features/auth/components/AuthDialogHandler'
import { createClient } from '@/libs/supabase/server'
import { AuthProvider } from '@/shared/libs/auth'
import { Navbar } from '@/widgets/Navbar'
import { MobileBottomNav } from '@/widgets/navigation'
import {
  DEFAULT_NAVIGATION_ITEMS,
  type NavItem,
} from '@/widgets/navigation/constants'

const oswald = Oswald({
  subsets: ['latin'],
  variable: '--font-heading',
  weight: ['400', '500', '600', '700'],
})

export const metadata: Metadata = {
  title: 'Armwrestling Power Arena',
  description: 'Track and improve your armwrestling performance',
  manifest: '/manifest.json',
  themeColor: '#000000',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black-translucent',
    title: 'Armwrestling Power Arena',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  applicationName: 'Armwrestling Power Arena',
  icons: {
    icon: ['/favicon-16x16.png', '/favicon-32x32.png'],
    apple: '/apple-touch-icon.png',
    shortcut: '/favicon.ico',
  },
  other: {
    'mobile-web-app-capable': 'yes',
  },
}

// const navItems: NavItem[] = [
//   { href: '/', title: 'Home', icon: HomeIcon },
//   { href: '/exercises', title: 'Exercises', icon: DumbbellIcon },
//   { href: '/submissions', title: 'Submissions', icon: UploadIcon },
// ]

export const TestLayout = ({ children }: { children: React.ReactNode }) => (
  <html lang="en">
    <body>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false}>
        <AuthProvider>
          <Navbar items={DEFAULT_NAVIGATION_ITEMS} userRole={null} />
          <main>{children}</main>
          <Footer />
          <PWAInstallPrompt />
          <AuthDialogHandler />
          <Toaster />
          <MobileBottomNav />
        </AuthProvider>
      </ThemeProvider>
    </body>
  </html>
)

export default process.env.NODE_ENV === 'test'
  ? TestLayout
  : async function RootLayout({
      children,
    }: Readonly<{
      children: React.ReactNode
    }>) {
      // Server-side: fetch user role for Navbar
      let role: 'admin' | 'grandmaster' | 'athlete' | null = null
      try {
        const supabase = createClient()
        const {
          data: { user },
        } = await supabase.auth.getUser()
        if (user) {
          const { data } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single()
          if (data && typeof data.role === 'string') {
            role = data.role as 'admin' | 'grandmaster' | 'athlete'
          }
        }
      } catch {
        // ignore, treat as null
      }

      return (
        <html lang="en" suppressHydrationWarning={true}>
          <body
            className={`${oswald.variable} flex min-h-screen flex-col font-sans antialiased`}
          >
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem={false}
              disableTransitionOnChange
            >
              <AuthProvider>
                <Navbar items={DEFAULT_NAVIGATION_ITEMS} userRole={role} />
                <main className="w-full max-w-full flex-1 pt-16 md:pt-24 bg-gradient-to-br from-gradient-start via-gradient-mid to-gradient-end dark:from-gradient-start dark:via-gradient-mid dark:to-gradient-end dark:text-white">
                  {children}
                </main>
                <Footer />
                <PWAInstallPrompt />
                <AuthDialogHandler />
                <Toaster />
                <MobileBottomNav />
              </AuthProvider>
            </ThemeProvider>
          </body>
        </html>
      )
    }
