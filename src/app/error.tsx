'use client'

import { But<PERSON> } from '@/components/Button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import { AlertTriangle } from 'lucide-react'
import { useEffect } from 'react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="w-16 h-16 text-yellow-400" />
          </div>
          <CardTitle className="text-3xl font-bold text-center text-primary">
            Something went wrong
          </CardTitle>
          <CardDescription className="text-center text-gray-500 dark:text-gray-400 mt-2">
            We are sorry, but an unexpected error occurred. Please try again
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error.message && (
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-[200px] text-sm font-mono">
              {error.message}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={reset}>Try again</Button>
        </CardFooter>
      </Card>
    </div>
  )
}
