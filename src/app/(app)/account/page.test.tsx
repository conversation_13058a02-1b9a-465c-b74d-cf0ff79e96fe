import { render } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import AccountPage from './page'

// Mock next/headers cookies
vi.mock('next/headers', () => ({
  cookies: () => ({
    getAll: () => [],
  }),
}))

// Mock Supabase server client
const mockGetUser = vi.fn()
const mockSingle = vi.fn()
const mockSelect = vi.fn()
const mockEq = vi.fn()
const mockOrder = vi.fn()
const mockLimit = vi.fn()

vi.mock('@/libs/supabase', () => ({
  getSupabaseRouteHandlerClient: () => ({
    auth: {
      getUser: mockGetUser,
    },
    from: (table: string) => {
      if (table === 'profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: mockSingle,
            }),
          }),
        }
      }
      if (table === 'submissions') {
        return {
          select: mockSelect.mockReturnValue({
            eq: mockEq.mockReturnValue({
              order: mockOrder.mockReturnValue({
                limit: mockLimit.mockReturnValue({ data: [] }),
              }),
            }),
          }),
        }
      }
      return {
        select: () => ({
          eq: () => ({
            single: () => ({ data: null }),
          }),
        }),
      }
    },
  }),
}))

describe('AccountPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders user info and no completion prompt when profile is complete', async () => {
    mockGetUser.mockResolvedValue({
      data: { user: { id: 'u1', email: '<EMAIL>' } },
      error: null,
    })
    mockSingle.mockResolvedValue({
      data: {
        id: 'p1',
        username: 'testuser',
        full_name: 'Test User',
        country: 'USA',
        gender: 'male',
        weight_category: '80kg',
        avatar_url: null,
        titles: ['Champion'],
      },
      error: null,
    })

    const element = await AccountPage()
    const { findByText, queryByText } = render(element)
    expect(await findByText('My Account')).toBeInTheDocument()
    expect(await findByText('testuser')).toBeInTheDocument()
    expect(await findByText('<EMAIL>')).toBeInTheDocument()
    expect(await findByText('Test User')).toBeInTheDocument()
    expect(await findByText('Champion')).toBeInTheDocument()
    expect(queryByText(/complete your profile/i)).not.toBeInTheDocument()
  })

  it('shows completion prompt when required fields are missing', async () => {
    mockGetUser.mockResolvedValue({
      data: { user: { id: 'u2', email: '<EMAIL>' } },
      error: null,
    })
    mockSingle.mockResolvedValue({
      data: {
        id: 'p2',
        username: 'incomplete',
        full_name: null,
        country: null,
        gender: null,
        weight_category: null,
        avatar_url: null,
        titles: [],
      },
      error: null,
    })

    const element = await AccountPage()
    const { findByText } = render(element)
    expect(await findByText(/complete your profile/i)).toBeInTheDocument()
    expect(await findByText('Edit Account')).toBeInTheDocument()
  })

  it('renders sign-in required if no user', async () => {
    mockGetUser.mockResolvedValue({
      data: { user: null },
      error: null,
    })
    const element = await AccountPage()
    const { findByText } = render(element)
    expect(await findByText(/sign in required/i)).toBeInTheDocument()
  })

  it('renders submissions section with data', async () => {
    // Mock user and profile data
    mockGetUser.mockResolvedValue({
      data: { user: { id: 'u1', email: '<EMAIL>' } },
      error: null,
    })
    mockSingle.mockResolvedValue({
      data: {
        id: 'p1',
        username: 'testuser',
        full_name: 'Test User',
        country: 'USA',
        gender: 'male',
        weight_category: '80kg',
        avatar_url: null,
        titles: ['Champion'],
      },
      error: null,
    })

    // Mock submissions data
    mockLimit.mockReturnValue({
      data: [],
    })

    // Mock SubmissionHistoryList component
    vi.mock('@/features/submissions/SubmissionHistoryList', () => ({
      default: ({
        submissions,
        limit,
      }: {
        submissions: unknown[]
        limit?: number
      }) => (
        <div data-testid="submission-history">
          {submissions.length} submissions found
          {limit && <span>Limit: {limit}</span>}
        </div>
      ),
    }))

    const element = await AccountPage()
    const { findByText, findByTestId } = render(element)

    expect(await findByText('Recent Submissions')).toBeInTheDocument()
    expect(await findByTestId('submission-history')).toBeInTheDocument()
    expect(await findByText('0 submissions found')).toBeInTheDocument()
  })
})
