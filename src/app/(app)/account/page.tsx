import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import SubmissionHistoryList, {
  type SubmissionWithExercise,
} from '@/features/submissions/SubmissionHistoryList'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { cookies } from 'next/headers'
import Image from 'next/image'
import Link from 'next/link'

type Profile = {
  id: string
  username: string
  full_name: string | null
  country: string | null
  gender: string | null
  weight_category: string | null
  avatar_url: string | null
  titles: string[] | null
}

export default async function AccountPage() {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    // Should never happen due to middleware, but double-safety
    return (
      <div className="container mx-auto flex flex-col items-center justify-center px-4 py-16">
        <Card>
          <CardHeader>
            <CardTitle>Sign In Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Please sign in to access your account.</p>
            <Button asChild>
              <Link href="/login">Go to Login</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Fetch profile by auth user id
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('auth_user_id', user.id)
    .single<Profile>()

  // Fetch user's submissions with exercise titles
  const { data: submissions } = await supabase
    .from('submissions')
    .select(
      `
      *,
      exercises(title)
    `,
    )
    .eq('user_id', user.id)
    .order('submitted_at', { ascending: false })
    .limit(20)

  // Transform submissions data to include exercise title
  const submissionsWithExercises: SubmissionWithExercise[] = submissions
    ? submissions.map((submission) => ({
        ...submission,
        exercise_title: submission.exercises?.title || 'Unknown Exercise',
      }))
    : []

  // Profile completion logic
  const requiredFields: (keyof Profile)[] = [
    'full_name',
    'country',
    'gender',
    'weight_category',
  ]
  const isIncomplete =
    !profile || requiredFields.some((k) => !profile[k] || profile[k] === '')

  return (
    <div className="container mx-auto max-w-2xl px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold">My Account</h1>

      {isIncomplete && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Complete Your Profile</AlertTitle>
          <AlertDescription>
            Some important profile fields are missing. Please{' '}
            <Link href="/account/edit" className="font-medium underline">
              complete profile
            </Link>{' '}
            to unlock all features.
          </AlertDescription>
        </Alert>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Account Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center gap-4">
            {profile?.avatar_url ? (
              <Image
                src={profile.avatar_url}
                alt="Avatar"
                width={64}
                height={64}
                className="h-16 w-16 rounded-full border object-cover"
              />
            ) : (
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 text-2xl font-bold">
                {profile?.username?.[0]?.toUpperCase() ?? '?'}
              </div>
            )}
            <div>
              <div className="text-lg font-semibold">
                {profile?.username ?? 'Unknown'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {user.email}
              </div>
              {profile?.full_name && (
                <div className="text-sm text-gray-800 dark:text-gray-100">
                  {profile.full_name}
                </div>
              )}
              {profile?.titles && profile.titles.length > 0 && (
                <div className="mt-1 flex flex-wrap gap-1">
                  {profile.titles.map((title) => (
                    <Badge key={title}>{title}</Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className="mb-2 grid grid-cols-2 gap-2">
            <div>
              <span className="font-medium">Country:</span>{' '}
              {profile?.country ?? <span className="text-gray-400">—</span>}
            </div>
            <div>
              <span className="font-medium">Gender:</span>{' '}
              {profile?.gender ?? <span className="text-gray-400">—</span>}
            </div>
            <div>
              <span className="font-medium">Weight Category:</span>{' '}
              {profile?.weight_category ?? (
                <span className="text-gray-400">—</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mb-8 flex flex-wrap gap-3">
        <Button asChild>
          <Link href="/account/edit">Edit Account</Link>
        </Button>
        {profile?.username && (
          <Button asChild variant="secondary">
            <Link href={`/profile/${profile.username}`}>
              View Public Profile
            </Link>
          </Button>
        )}
        <Button asChild variant="outline">
          <Link href="/submit">Submit New Performance</Link>
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Recent Submissions</CardTitle>
          <Button asChild variant="outline" size="sm">
            <Link href="/account/submissions">View All Submissions</Link>
          </Button>
        </CardHeader>
        <CardContent>
          <SubmissionHistoryList
            submissions={submissionsWithExercises}
            limit={5}
          />
        </CardContent>
      </Card>
    </div>
  )
}
