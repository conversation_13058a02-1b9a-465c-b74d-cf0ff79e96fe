import { Button } from '@/components/ui/button'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import SubmissionHistoryList, {
  type SubmissionWithExercise,
} from '@/features/submissions/SubmissionHistoryList'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import type { Metadata } from 'next'
import { cookies } from 'next/headers'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'My Submissions | Arm Power Arena',
  description: 'View and manage your exercise performance submissions',
}

/**
 * Account Submissions Page
 * Displays all submissions for the authenticated user
 */
export default async function AccountSubmissionsPage() {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    // Should never happen due to middleware, but double-safety
    return (
      <div className="container mx-auto flex flex-col items-center justify-center px-4 py-16">
        <Card>
          <CardHeader>
            <CardTitle>Sign In Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Please sign in to access your submissions.</p>
            <Button asChild>
              <Link href="/login">Go to Login</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Fetch user's submissions with exercise titles
  const { data: submissions } = await supabase
    .from('submissions')
    .select(`
      *,
      exercises(title)
    `)
    .eq('user_id', user.id)
    .order('submitted_at', { ascending: false })

  // Transform submissions data to include exercise title
  const submissionsWithExercises: SubmissionWithExercise[] = submissions
    ? submissions.map((submission) => ({
        ...submission,
        exercise_title: submission.exercises?.title || 'Unknown Exercise',
      }))
    : []

  return (
    <div className="container mx-auto max-w-4xl px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">My Submissions</h1>
        <Button asChild>
          <Link href="/submit">Submit New Performance</Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <SubmissionHistoryList submissions={submissionsWithExercises} />
        </CardContent>
      </Card>
    </div>
  )
}
