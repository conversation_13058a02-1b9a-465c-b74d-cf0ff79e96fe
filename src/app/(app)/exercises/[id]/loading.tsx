import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'

/**
 * Loading state component for the exercise detail page
 */
export default function ExerciseDetailLoading() {
  return (
    <main className="container mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <Skeleton className="h-12 w-64 sm:h-14 sm:w-80" />
        <Button variant="outline" asChild>
          <Link href="/exercises">Back to Exercises</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-10">
        {/* Left column (video and submit button) */}
        <div className="space-y-8 lg:col-span-2">
          {/* Video player skeleton */}
          <Skeleton className="aspect-video w-full rounded-xl" />

          {/* Submit button skeleton */}
          <Card className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="px-6 py-5">
              <Skeleton className="mb-4 h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="mt-4 h-10 w-full" />
            </CardContent>
          </Card>
        </div>

        {/* Right column (details) */}
        <div className="space-y-8 lg:col-span-1">
          {/* Description skeleton */}
          <Card className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="px-6 py-5">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="mt-2 h-4 w-full" />
              <Skeleton className="mt-2 h-4 w-3/4" />
            </CardContent>
          </Card>

          {/* Equipment Required skeleton */}
          <Card className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="px-6 py-5">
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>

          {/* Evaluation Criteria skeleton */}
          <Card className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent className="px-6 py-5">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="mt-2 h-4 w-full" />
              <Skeleton className="mt-2 h-4 w-1/2" />
            </CardContent>
          </Card>

          {/* Medal Thresholds skeleton */}
          <Card className="overflow-hidden">
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent className="px-6 py-5">
              <div className="space-y-4">
                <div>
                  <Skeleton className="mb-2 h-5 w-32" />
                  <div className="grid grid-cols-4 gap-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                </div>
                <div>
                  <Skeleton className="mb-2 h-5 w-40" />
                  <div className="grid grid-cols-4 gap-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
