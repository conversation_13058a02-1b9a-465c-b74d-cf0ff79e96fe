import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'

/**
 * Not Found page for exercise detail
 * Displayed when an exercise with the specified ID doesn't exist
 */
export default function ExerciseNotFound() {
  return (
    <main className="container mx-auto flex min-h-[70vh] flex-col items-center justify-center px-4 py-16 text-center">
      <h1 className="mb-4 text-3xl font-bold sm:text-4xl">
        Exercise Not Found
      </h1>
      <p className="mb-8 max-w-md text-lg text-gray-600 dark:text-gray-400">
        Sorry, we couldn't find the exercise you're looking for. It may have
        been removed or the ID is incorrect.
      </p>
      <Button asChild>
        <Link href="/exercises">Browse All Exercises</Link>
      </Button>
    </main>
  )
}
