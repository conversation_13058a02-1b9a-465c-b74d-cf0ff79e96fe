import { EvaluationCriteriaCard } from '@/components/features/exercises/EvaluationCriteriaCard'
import { ExerciseDetailCard } from '@/components/features/exercises/ExerciseDetailCard'
import { MedalThresholds } from '@/components/features/exercises/MedalTresholds'
import { SubmitPerformanceCard } from '@/components/features/exercises/SubmitPerformanceCard'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server'
import { VideoPlayer } from '@/shared/ui/VideoPlayer'
import { Dumbbell, FileText, Upload } from 'lucide-react'
import { cookies } from 'next/headers'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { generateMetadata } from './metadata'

// Export the metadata generator
export { generateMetadata }

/**
 * Type definition for Exercise data from Supabase
 */
type Exercise = {
  id: string
  title: string
  description: string
  video_tutorial_url: string
  equipment_required: string[]
  evaluation_criteria: string
  medal_thresholds: Record<string, Record<string, number>>
  created_by: string
  created_at: string
  updated_at: string
}

/**
 * Dynamic page component for displaying detailed exercise information
 */
export default async function ExerciseDetailPage({
  params,
}: {
  params: { id: string }
}) {
  // Create Supabase client with cookie support
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  // Ensure params.id is available before using it
  const id = await params.id

  // Fetch exercise data from Supabase
  const { data: exercise, error } = await supabase
    .from('exercises')
    .select('*')
    .eq('id', id)
    .single<Exercise>()

  // Handle errors or missing data with notFound()
  if (error || !exercise) {
    console.error('Error fetching exercise:', error)
    notFound()
  }

  return (
    <main className="container mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
          {exercise.title}
        </h1>
        <Button variant="outline" asChild>
          <Link href="/exercises">Back to Exercises</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-10">
        {/* Left column (video and medal thresholds) */}
        <div className="space-y-8 lg:col-span-2">
          {/* Video player */}
          <VideoPlayer url={exercise.video_tutorial_url} />

          {/* Medal Thresholds */}
          <MedalThresholds thresholds={exercise.medal_thresholds} />
        </div>

        <div className="space-y-8 lg:col-span-1">
          {/* Description */}
          <ExerciseDetailCard
            title="Description"
            icon={<FileText className="h-6 w-6" />}
            content={
              <ul className="list-disc space-y-2 pl-5">
                {exercise.description
                  .split('- ')
                  .filter((item) => item.trim() !== '')
                  .map((item) => (
                    <li
                      key={item}
                      className="leading-relaxed text-gray-700 dark:text-gray-300"
                    >
                      {item.trim()}
                    </li>
                  ))}
              </ul>
            }
          />

          <EvaluationCriteriaCard criteria={exercise.evaluation_criteria} />

          <ExerciseDetailCard
            title="Equipment Required"
            icon={<Dumbbell className="h-6 w-6" />}
            content={
              exercise.equipment_required &&
              Array.isArray(exercise.equipment_required) &&
              exercise.equipment_required.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {exercise.equipment_required.map((item: string) => (
                    <Badge key={item} variant="secondary">
                      {item}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 dark:text-gray-400">
                  No specific equipment required.
                </p>
              )
            }
          />

          <SubmitPerformanceCard
            exerciseId={exercise.id}
            exerciseTitle={exercise.title}
            icon={<Upload className="h-6 w-6" />}
          />
        </div>
      </div>
    </main>
  )
}
