import { render, screen } from '@testing-library/react'
import { describe, expect, it } from 'vitest'
import SubmitPage from './page'

// Mock SubmissionForm to isolate page test
vi.mock('@/features/submissions/SubmissionForm', () => ({
  SubmissionForm: () => <div data-testid="submission-form" />,
}))

describe('/submit page', () => {
  it('renders the SubmissionForm inside a centered main', () => {
    render(<SubmitPage />)
    const main = screen.getByRole('main')
    expect(main).toHaveClass('flex')
    expect(screen.getByTestId('submission-form')).toBeInTheDocument()
  })
})
