'use server'

import { authRateLimit, strictRateLimit } from '@/libs/rate-limit'
import { getSupabaseCredentials } from '@/libs/supabase/credentials'
import {
    getAuthErrorMessage,
    passwordResetSchema,
    signInSchema,
    signUpSchema,
} from '@/shared/libs/auth/validation'
import type { AuthErrorType } from '@/shared/types/auth'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

// Define enhanced auth response types
export interface AuthActionState {
  error: string | null
  success: boolean
  errorType?: AuthErrorType
  needsConfirmation?: boolean
  data?: {
    message?: string
  }
}

// Helper function to create Supabase server client
async function createSupabaseServerClient() {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabase<PERSON><PERSON><PERSON>ey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        for (const { name, value, options } of cookiesToSet) {
          cookieStore.set(name, value, options)
        }
      },
    },
  })
}

// Using unified error handling from shared auth library

export async function signInAction(
  _prevState: AuthActionState | null,
  formData: FormData,
): Promise<AuthActionState> {
  // SECURITY: Apply rate limiting to prevent brute force attacks
  const rateLimitResult = await authRateLimit()
  if (!rateLimitResult.success) {
    return {
      error: rateLimitResult.error || 'Too many attempts. Please try again later.',
      success: false,
      errorType: 'RATE_LIMITED',
    }
  }

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Validation using Zod schema
  const validationResult = signInSchema.safeParse({ email, password })

  if (!validationResult.success) {
    const firstError = validationResult.error.issues[0]
    return {
      error: firstError.message,
      success: false,
      errorType:
        firstError.path[0] === 'email'
          ? 'INVALID_CREDENTIALS'
          : 'UNKNOWN_ERROR',
    }
  }

  const validatedData = validationResult.data

  try {
    const supabase = await createSupabaseServerClient()

    const { error } = await supabase.auth.signInWithPassword({
      email: validatedData.email,
      password: validatedData.password,
    })

    if (error) {
      console.error('Sign in error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return {
        error: message,
        success: false,
        errorType: type,
      }
    }

    // Successful sign in - no redirect, return success state
    return {
      error: null,
      success: true,
    }
  } catch (error) {
    console.error('Unexpected sign in error:', error)
    return {
      error: 'An unexpected error occurred. Please try again.',
      success: false,
      errorType: 'UNKNOWN_ERROR',
    }
  }
}

export async function signUpAction(
  _prevState: AuthActionState | null,
  formData: FormData,
): Promise<AuthActionState> {
  // SECURITY: Apply rate limiting to prevent spam registrations
  const rateLimitResult = await authRateLimit()
  if (!rateLimitResult.success) {
    return {
      error: rateLimitResult.error || 'Too many attempts. Please try again later.',
      success: false,
      errorType: 'RATE_LIMITED',
    }
  }

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Validation using Zod schema
  const validationResult = signUpSchema.safeParse({ email, password })

  if (!validationResult.success) {
    const firstError = validationResult.error.issues[0]
    return {
      error: firstError.message,
      success: false,
      errorType: 'UNKNOWN_ERROR',
    }
  }

  const validatedData = validationResult.data

  try {
    const supabase = await createSupabaseServerClient()

    const { error, data } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
      },
    })

    if (error) {
      console.error('Sign up error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return {
        error: message,
        success: false,
        errorType: type,
      }
    }

    // Check if email confirmation is needed
    if (data.user && !data.session) {
      return {
        error: null,
        success: true,
        needsConfirmation: true,
        data: {
          message:
            'Please check your email and click the confirmation link to complete your registration.',
        },
      }
    }

    // Successful sign up with immediate session (no confirmation needed)
    redirect('/profile/create')
  } catch (error) {
    console.error('Unexpected sign up error:', error)
    return {
      error: 'An unexpected error occurred. Please try again.',
      success: false,
      errorType: 'UNKNOWN_ERROR',
    }
  }
}

export async function resetPasswordAction(
  _prevState: AuthActionState | null,
  formData: FormData,
): Promise<AuthActionState> {
  // SECURITY: Apply strict rate limiting for password reset to prevent abuse
  const rateLimitResult = await strictRateLimit()
  if (!rateLimitResult.success) {
    return {
      error: rateLimitResult.error || 'Too many password reset attempts. Please try again later.',
      success: false,
      errorType: 'RATE_LIMITED',
    }
  }

  const email = formData.get('email') as string

  // Validation using Zod schema
  const validationResult = passwordResetSchema.safeParse({ email })

  if (!validationResult.success) {
    const firstError = validationResult.error.issues[0]
    return {
      error: firstError.message,
      success: false,
      errorType: 'UNKNOWN_ERROR',
    }
  }

  const validatedData = validationResult.data

  try {
    const supabase = await createSupabaseServerClient()

    const { error } = await supabase.auth.resetPasswordForEmail(
      validatedData.email,
      {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/update-password`,
      },
    )

    if (error) {
      console.error('Reset password error:', error)
      const { message, type } = getAuthErrorMessage(error)
      return {
        error: message,
        success: false,
        errorType: type,
      }
    }

    return {
      error: null,
      success: true,
      data: {
        message: 'Password reset email sent. Please check your inbox.',
      },
    }
  } catch (error) {
    console.error('Unexpected reset password error:', error)
    return {
      error: 'An unexpected error occurred. Please try again.',
      success: false,
      errorType: 'UNKNOWN_ERROR',
    }
  }
}

export async function signOutAction(): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient()
    await supabase.auth.signOut()
  } catch (error) {
    console.error('Sign out error:', error)
    // Still redirect even if sign out fails
  }
  redirect('/login')
}
