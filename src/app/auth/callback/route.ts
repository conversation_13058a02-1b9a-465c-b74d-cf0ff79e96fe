import { getSupabaseCredentials } from '@/libs/supabase/credentials'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type NextRequest, NextResponse } from 'next/server'

/**
 * Securely sanitize redirect URLs to prevent open redirects
 * @param url - The redirect URL to sanitize
 * @param baseUrl - The base URL of the request
 * @returns Safe redirect path
 */
function sanitizeRedirectUrl(url: string, baseUrl: string): string {
  try {
    const parsed = new URL(url, baseUrl)
    const requestOrigin = new URL(baseUrl).origin

    // Only allow same-origin redirects
    if (parsed.origin !== requestOrigin) {
      console.warn(`Blocked redirect to different origin: ${parsed.origin}`)
      return '/'
    }

    // Return pathname + search only (no hash, protocol, etc.)
    return parsed.pathname + parsed.search
  } catch (error) {
    console.warn(`Invalid redirect URL: ${url}`, error)
    return '/'
  }
}

/**
 * Generate CSRF token for OAuth flows
 */
function generateCSRFToken(): string {
  return crypto.randomUUID()
}

/**
 * Validate CSRF token format
 */
function validateCSRFToken(token: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return uuidRegex.test(token)
}

/**
 * Route handler for Supabase OAuth/email confirmation callback.
 * Exchanges the code for a session and redirects to the appropriate page.
 * Includes proper error handling and secure redirect validation.
 */
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')
  const errorDescription = requestUrl.searchParams.get('error_description')
  const state = requestUrl.searchParams.get('state')
  const redirectTo = requestUrl.searchParams.get('redirectTo') || '/'

  // Secure redirect URL sanitization
  const sanitizedRedirectTo = sanitizeRedirectUrl(redirectTo, request.url)

  // CSRF protection: validate state parameter if present
  if (state && !validateCSRFToken(state)) {
    console.error('Invalid CSRF token in auth callback')
    const errorUrl = new URL('/login', request.url)
    errorUrl.searchParams.set('error', 'invalid_state')
    errorUrl.searchParams.set(
      'error_description',
      'Invalid authentication state. Please try again.',
    )
    return NextResponse.redirect(errorUrl)
  }

  // Handle auth errors (e.g., expired link, invalid token)
  if (error) {
    console.error('Auth callback error:', error, errorDescription)
    const errorUrl = new URL('/login', request.url)
    errorUrl.searchParams.set('error', error)
    if (errorDescription) {
      errorUrl.searchParams.set('error_description', errorDescription)
    }
    return NextResponse.redirect(errorUrl)
  }

  if (code) {
    try {
      const cookieStore = await cookies()
      const { supabaseUrl, supabaseAnonKey } = getSupabaseCredentials()
      const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            for (const { name, value, options } of cookiesToSet) {
              cookieStore.set({ name, value, ...options })
            }
          },
        },
      })

      const { error: exchangeError } =
        await supabase.auth.exchangeCodeForSession(code)

      if (exchangeError) {
        console.error('Failed to exchange code for session:', exchangeError)
        const errorUrl = new URL('/login', request.url)
        errorUrl.searchParams.set('error', 'auth_code_exchange_failed')
        errorUrl.searchParams.set(
          'error_description',
          'Failed to complete authentication. Please try again.',
        )
        return NextResponse.redirect(errorUrl)
      }

      // Check if user has a profile (in case the trigger failed)
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single()

        if (!profile) {
          console.warn(
            'User profile not found, redirecting to profile creation',
          )
          return NextResponse.redirect(new URL('/profile/create', request.url))
        }
      }
    } catch (error) {
      console.error('Unexpected error in auth callback:', error)
      const errorUrl = new URL('/login', request.url)
      errorUrl.searchParams.set('error', 'unexpected_error')
      errorUrl.searchParams.set(
        'error_description',
        'An unexpected error occurred during authentication.',
      )
      return NextResponse.redirect(errorUrl)
    }
  }

  // Successful authentication - redirect to the intended destination
  return NextResponse.redirect(new URL(sanitizedRedirectTo, request.url))
}
