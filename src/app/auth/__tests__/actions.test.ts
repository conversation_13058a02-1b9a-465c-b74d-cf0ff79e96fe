/* eslint-disable @typescript-eslint/no-explicit-any */
import * as supabaseCredentials from '@/libs/supabase/credentials'
import * as supabaseSSR from '@supabase/ssr'
import * as nextHeaders from 'next/headers'
import * as nextNavigation from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  resetPasswordAction,
  signInAction,
  signOutAction,
  signUpAction,
} from '../actions'

vi.mock('@/libs/supabase/credentials')
vi.mock('@supabase/ssr')
vi.mock('next/headers')
vi.mock('next/navigation')

describe('React 19 auth server actions', () => {
  const redirect = vi.fn()
  const cookiesMock = vi.fn()
  const signInWithPasswordMock = vi.fn()
  const signUpMock = vi.fn()
  const resetPasswordForEmailMock = vi.fn()
  const signOutMock = vi.fn()
  const createServerClientMock = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Set up required environment variable for tests
    process.env.NEXT_PUBLIC_SITE_URL = 'https://test.com'

    // Mock Next.js navigation
    ;(nextNavigation as Record<string, unknown>).redirect = redirect

    // Mock Supabase credentials
    ;(supabaseCredentials as Record<string, unknown>).getSupabaseCredentials =
      vi.fn().mockReturnValue({
        supabaseUrl: 'https://test.supabase.co',
        supabaseAnonKey: 'test-anon-key',
      })

    // Mock Next.js cookies
    cookiesMock.mockResolvedValue({
      getAll: () => [],
      set: vi.fn(),
    })
    ;(nextHeaders as Record<string, unknown>).cookies = cookiesMock

    // Mock Supabase SSR client
    createServerClientMock.mockReturnValue({
      auth: {
        signInWithPassword: signInWithPasswordMock,
        signUp: signUpMock,
        resetPasswordForEmail: resetPasswordForEmailMock,
        signOut: signOutMock,
      },
    })
    ;(supabaseSSR as Record<string, unknown>).createServerClient =
      createServerClientMock
  })

  describe('signInAction', () => {
    it('validates email and password', async () => {
      const formData = new FormData()
      formData.append('email', 'invalid-email')
      formData.append('password', 'password')

      const result = await signInAction(null, formData)

      expect(result).toEqual({
        error: 'Please enter a valid email address',
        success: false,
        errorType: 'INVALID_CREDENTIALS',
      })
      expect(signInWithPasswordMock).not.toHaveBeenCalled()
      expect(redirect).not.toHaveBeenCalled()
    })

    it('handles successful sign in', async () => {
      signInWithPasswordMock.mockResolvedValue({ error: null })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password')

      const result = await signInAction(null, formData)

      expect(signInWithPasswordMock).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      })
      expect(redirect).not.toHaveBeenCalled()
      expect(result).toEqual({ error: null, success: true })
    })

    it('handles sign in error', async () => {
      signInWithPasswordMock.mockResolvedValue({
        error: { message: 'Invalid login credentials' },
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'wrongpassword')

      const result = await signInAction(null, formData)

      expect(result).toEqual({
        error: 'Invalid login credentials',
        success: false,
        errorType: 'INVALID_CREDENTIALS',
      })
      expect(redirect).not.toHaveBeenCalled()
    })

    it('handles unexpected errors', async () => {
      signInWithPasswordMock.mockRejectedValue(new Error('Network error'))

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password')

      const result = await signInAction(null, formData)

      expect(result).toEqual({
        error: 'An unexpected error occurred. Please try again.',
        success: false,
        errorType: 'UNKNOWN_ERROR',
      })
      expect(redirect).not.toHaveBeenCalled()
    })
  })

  describe('signUpAction', () => {
    it('validates email and password', async () => {
      const formData = new FormData()
      formData.append('email', 'invalid-email')
      formData.append('password', 'weak')

      const result = await signUpAction(null, formData)

      expect(result).toEqual({
        error: 'Please enter a valid email address',
        success: false,
        errorType: 'UNKNOWN_ERROR',
      })
      expect(signUpMock).not.toHaveBeenCalled()
      expect(redirect).not.toHaveBeenCalled()
    })

    it('handles successful sign up with email confirmation', async () => {
      signUpMock.mockResolvedValue({
        error: null,
        data: { user: { id: '123' }, session: null },
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'ValidPassword123')

      const result = await signUpAction(null, formData)

      expect(signUpMock).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'ValidPassword123',
        options: {
          emailRedirectTo: 'https://test.com/auth/callback',
        },
      })
      expect(result).toEqual({
        error: null,
        success: true,
        needsConfirmation: true,
        data: {
          message:
            'Please check your email and click the confirmation link to complete your registration.',
        },
      })
      expect(redirect).not.toHaveBeenCalled()
    })

    it('handles successful sign up with immediate session', async () => {
      signUpMock.mockResolvedValue({
        error: null,
        data: { user: { id: '123' }, session: { user: { id: '123' } } },
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'ValidPassword123')

      await signUpAction(null, formData)

      expect(redirect).toHaveBeenCalledWith('/profile/create')
    })

    it('handles sign up error', async () => {
      signUpMock.mockResolvedValue({
        error: { message: 'User already registered' },
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'ValidPassword123')

      const result = await signUpAction(null, formData)

      expect(result).toEqual({
        error: 'User already registered',
        success: false,
        errorType: 'EMAIL_ALREADY_REGISTERED',
      })
      expect(redirect).not.toHaveBeenCalled()
    })
  })

  describe('resetPasswordAction', () => {
    it('validates email', async () => {
      const formData = new FormData()
      formData.append('email', 'invalid-email')

      const result = await resetPasswordAction(null, formData)

      expect(result).toEqual({
        error: 'Please enter a valid email address',
        success: false,
        errorType: 'UNKNOWN_ERROR',
      })
      expect(resetPasswordForEmailMock).not.toHaveBeenCalled()
    })

    it('handles successful password reset', async () => {
      resetPasswordForEmailMock.mockResolvedValue({ error: null })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')

      const result = await resetPasswordAction(null, formData)

      expect(resetPasswordForEmailMock).toHaveBeenCalledWith(
        '<EMAIL>',
        {
          redirectTo: 'https://test.com/auth/update-password',
        },
      )
      expect(result).toEqual({
        error: null,
        success: true,
        data: {
          message: 'Password reset email sent. Please check your inbox.',
        },
      })
    })

    it('handles password reset error', async () => {
      resetPasswordForEmailMock.mockResolvedValue({
        error: { message: 'Email not found' },
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')

      const result = await resetPasswordAction(null, formData)

      expect(result).toEqual({
        error: 'Email not found',
        success: false,
        errorType: 'UNKNOWN_ERROR',
      })
    })
  })

  describe('signOutAction', () => {
    it('handles successful sign out', async () => {
      signOutMock.mockResolvedValue({})

      await signOutAction()

      expect(signOutMock).toHaveBeenCalled()
      expect(redirect).toHaveBeenCalledWith('/login')
    })

    it('handles sign out error gracefully', async () => {
      signOutMock.mockRejectedValue(new Error('Network error'))

      await signOutAction()

      expect(signOutMock).toHaveBeenCalled()
      expect(redirect).toHaveBeenCalledWith('/login')
    })
  })
})
