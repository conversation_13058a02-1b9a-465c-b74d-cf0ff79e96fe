'use client'

import { <PERSON><PERSON> } from '@/components/Button'
import { PWAInstallPrompt } from '@/components/PWAInstallPrompt'
import { PushNotificationManager } from '@/components/PushNotificationManager'
import { Bell, Download, Wifi, WifiOff } from 'lucide-react'
import { Metadata } from 'next'
import Link from 'next/link'
import { useEffect } from 'react'

export default function PWAFeaturesPage() {
  useEffect(() => {
    document.querySelectorAll('.client-only').forEach((el) => {
      if (el.classList) {
        el.classList.remove('hidden')
      }
    })
  }, [])

  return (
    <div className="container max-w-4xl mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-6">PWA Features</h1>

      <div className="space-y-10">
        {/* App Installation Section */}
        <section className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Download className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Install the App</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Install Armwrestling Power Arena on your device to get the full
                app-like experience. Access your training data quickly without
                opening a browser.
              </p>
              <div className="client-only hidden">
                <PWAInstallPrompt />
                <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                  If you don't see an install button, this app may already be
                  installed or your browser might not support PWA installation.
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Push Notifications Section */}
        <section className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Bell className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold mb-2">Push Notifications</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Stay updated with your training progress, competitions, and
                important announcements through push notifications.
              </p>
              <div className="client-only hidden">
                <PushNotificationManager />
              </div>
            </div>
          </div>
        </section>

        {/* Offline Functionality Section */}
        <section className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <WifiOff className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Offline Access</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Access your training plans and exercise guides even when you're
                offline. The app caches important data so you can view it
                without an internet connection.
              </p>
              <Link href="/offline">
                <Button variant="outline" className="text-sm">
                  <WifiOff className="h-4 w-4 mr-2" />
                  Test Offline Page
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* PWA Information Section */}
        <section className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">
            About Progressive Web Apps
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-3">
            Progressive Web Apps (PWAs) combine the best features of websites
            and mobile applications:
          </p>
          <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
            <li>Install directly from the web - no app store required</li>
            <li>Work offline or with poor internet connections</li>
            <li>Receive push notifications like a native app</li>
            <li>Always up-to-date - no manual updates needed</li>
            <li>Fast loading and smooth performance</li>
            <li>Use less storage space than traditional apps</li>
          </ul>
        </section>
      </div>
    </div>
  )
}
