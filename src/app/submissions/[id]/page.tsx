import { Button } from '@/components/ui/button'
import { SubmissionErrorBoundary } from '@/features/submissions/components/SubmissionErrorBoundary'
import { SubmissionDetailsCard } from '@/features/submissions/components/detail/submission-details-card'
import { SubmissionEvaluation } from '@/features/submissions/components/detail/submission-evaluation'
import { SubmissionNotFound } from '@/features/submissions/components/detail/submission-not-found'
import { SubmissionVideo } from '@/features/submissions/components/detail/submission-video'
import { assertRole } from '@/libs/permissions/rbac'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import type { SubmissionWithRelations } from '@/types/submission'
import type { Metadata } from 'next'
import { cookies } from 'next/headers'
import Link from 'next/link'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: 'Submission Review | Arm Power Arena',
  description: 'Review and evaluate an athlete submission',
}

/**
 * Submission Detail Page Content
 * Displays a single submission for review and evaluation
 */
async function SubmissionDetailContent({ params }: { params: { id: string } }) {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)

  try {
    // Verify the user has admin or grandmaster role
    await assertRole(['admin', 'grandmaster'])

    const { data: submission, error } = await supabase
      .from('submissions')
      .select(
        `
        *,
        exercises(id, title, description, medal_thresholds),
        profiles:user_id(id, username, full_name, avatar_url, gender, weight_category)
      `,
      )
      .eq('id', params.id)
      .single<SubmissionWithRelations>() // Explicitly type the returned data

    if (error || !submission) {
      console.error('Error fetching submission:', error?.message)
      return <SubmissionNotFound />
    }

    return (
      <div className="container mx-auto max-w-5xl px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-3xl font-bold">Submission Review</h1>
          <Button asChild variant="outline">
            <Link href="/submissions">Back to Submissions</Link>
          </Button>
        </div>

        <SubmissionVideo videoUrl={submission.video_url} />

        <div className="grid gap-6 md:grid-cols-2">
          <SubmissionDetailsCard submission={submission} />
          <SubmissionEvaluation submission={submission} />
        </div>
      </div>
    )
  } catch (error) {
    // If assertRole fails or any other unexpected error
    if (error instanceof Error && error.message.includes('Unauthorized')) {
      // Or a specific error code/type from assertRole
      redirect('/') // Redirect to home for unauthorized access
    } else {
      console.error('Unexpected error in SubmissionDetailPage:', error)
      // Optionally, show a generic error page or redirect
      // For now, re-throwing or redirecting to a generic error might be suitable
      redirect('/error') // Or some other generic error handling
    }
  }
}

/**
 * Submission Detail Page
 * Protected by role-based access control and wrapped with error boundary
 */
export default async function SubmissionDetailPage({
  params,
}: { params: { id: string } }) {
  return (
    <SubmissionErrorBoundary>
      <SubmissionDetailContent params={params} />
    </SubmissionErrorBoundary>
  )
}
