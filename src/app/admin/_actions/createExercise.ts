'use server'

import { assertRole } from '@/libs/permissions/rbac'

/**
 * Example server action that requires 'admin' role.
 * Throws if the user is not an admin.
 */
export async function createExerciseAction(_formData: FormData) {
  void _formData // silence ESLint unused warning

  // SECURITY: Apply strict rate limiting to prevent admin action abuse
  const { strictRateLimit } = await import('@/libs/rate-limit')
  const rateLimitResult = await strictRateLimit()
  if (!rateLimitResult.success) {
    throw new Error(rateLimitResult.error || 'Too many admin actions. Please try again later.')
  }

  // Enforce RBAC: only admins can create exercises
  await assertRole(['admin'])

  // ...actual creation logic would go here
  // For demonstration, just return a success object
  return { success: true, message: 'Exercise created (dummy action)' }
}
