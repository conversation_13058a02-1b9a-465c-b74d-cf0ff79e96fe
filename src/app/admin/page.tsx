import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/Card'

export default function AdminPage() {
  return (
    <>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-gray-500 dark:text-gray-400">
          Manage users, exercises, and submissions
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 dark:text-gray-400">
              Manage user accounts, permissions, and roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Exercise Library</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 dark:text-gray-400">
              Add, edit, or remove exercises from the library
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Submission Review</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 dark:text-gray-400">
              Review and approve user exercise submissions
            </p>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
