import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/Card'
import PageLayout from '@/components/PageLayout'
import RankingsTable from '@/components/rankings/RankingsTable'
import { LoadingPage } from '@/components/ui/loading-spinner'
import { fetchRankings } from '@/libs/rankings'
import { createClient } from '@/libs/supabase/server'
import { Suspense } from 'react'
import RankingsPageClient from './rankings-client'

// Get all unique countries from the database
async function getCountries(): Promise<string[]> {
  try {
    const supabase = createClient()
    const { data, error } = await supabase
      .from('profiles')
      .select('country')
      .not('country', 'is', null)
      .order('country')

    if (error) {
      console.error('Error fetching countries:', error)
      return []
    }

    // Extract unique countries
    const countries = [...new Set(data.map((user) => user.country))]
    return countries
  } catch (error) {
    console.error('Failed to fetch countries:', error)
    return []
  }
}

interface PageProps {
  searchParams: {
    gender?: string
    weightCategory?: string
    country?: string
  }
}

export default async function RankingsPage({ searchParams }: PageProps) {
  // Extract filter parameters from URL safely
  const gender =
    searchParams.gender === 'male' || searchParams.gender === 'female'
      ? searchParams.gender
      : undefined

  const weightCategory =
    searchParams.weightCategory === 'under_95kg' ||
    searchParams.weightCategory === 'over_95kg'
      ? searchParams.weightCategory
      : undefined

  const country =
    searchParams.country && searchParams.country !== 'all'
      ? searchParams.country
      : undefined

  // Fetch rankings based on filters
  const rankings = await fetchRankings({
    gender,
    weightCategory,
    country,
  })

  // Fetch countries for the filter
  const countries = await getCountries()

  return (
    <PageLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Global Rankings</h1>
        <p className="text-gray-500 dark:text-gray-400">
          See how you compare to other armwrestlers around the world
        </p>
      </div>

      <Card className="mb-8 w-full">
        <CardContent className="px-6 py-4">
          <div className="flex items-center gap-4 w-full">
            <h3 className="text-lg font-semibold whitespace-nowrap">Filters</h3>
            <Suspense
              fallback={<div className="flex-grow">Loading filters...</div>}
            >
              <RankingsPageClient
                countries={countries}
                initialGender={gender || 'all'}
                initialWeightCategory={weightCategory || 'all'}
                initialCountry={country || 'all'}
              />
            </Suspense>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300 w-full">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Athlete Rankings</CardTitle>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {rankings.length} athletes
          </div>
        </CardHeader>
        <CardContent className="px-6 py-4">
          <div className="w-full">
            <Suspense fallback={<LoadingPage />}>
              <RankingsTable users={rankings} />
            </Suspense>
          </div>
        </CardContent>
      </Card>
    </PageLayout>
  )
}
