import { fireEvent, render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import LoginPage from './page'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    replace: vi.fn(),
    push: vi.fn(),
  }),
}))

// Mock auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: vi.fn(),
}))

describe('LoginPage', () => {
  it('shows loading spinner when auth is loading', () => {
    // Mock loading state
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      loading: true,
      user: null,
    })

    render(<LoginPage />)
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('shows login UI when user is not authenticated', () => {
    // Mock unauthenticated state
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      loading: false,
      user: null,
    })

    render(<LoginPage />)
    expect(screen.getByText('Sign In')).toBeInTheDocument()
    expect(
      screen.getByText(
        'Please sign in to access your account and submit performances.',
      ),
    ).toBeInTheDocument()
  })

  it('redirects to home when user is authenticated', () => {
    // Mock authenticated state
    const mockReplace = vi.fn()
    vi.mocked(require('next/navigation').useRouter).mockReturnValue({
      replace: mockReplace,
      push: vi.fn(),
    })
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      loading: false,
      user: { id: 'user-123' },
    })

    render(<LoginPage />)
    expect(mockReplace).toHaveBeenCalledWith('/')
  })

  it('navigates to home with auth param when login button is clicked', () => {
    // Mock unauthenticated state
    const mockPush = vi.fn()
    vi.mocked(require('next/navigation').useRouter).mockReturnValue({
      replace: vi.fn(),
      push: mockPush,
    })
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      loading: false,
      user: null,
    })

    render(<LoginPage />)
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }))
    expect(mockPush).toHaveBeenCalledWith('/?auth=signin')
  })
})
