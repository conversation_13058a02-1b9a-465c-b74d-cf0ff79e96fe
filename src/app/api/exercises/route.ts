import { apiRateLimit } from '@/libs/rate-limit'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

/**
 * GET /api/exercises
 * Fetches all exercises for use in submission forms
 * Protected by authentication
 */
export async function GET() {
  try {
    // SECURITY: Apply rate limiting to prevent API abuse
    const rateLimitResult = await apiRateLimit()
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: rateLimitResult.error || 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const cookieStore = await cookies()
    const supabase = getSupabaseRouteHandlerClient(cookieStore)

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 },
      )
    }

    const { data: exercises, error } = await supabase
      .from('exercises')
      .select('id, title')
      .order('title')

    if (error) {
      console.error('Error fetching exercises:', error)
      return NextResponse.json(
        { error: 'Failed to fetch exercises' },
        { status: 500 },
      )
    }

    return NextResponse.json({ exercises: exercises || [] })
  } catch (error) {
    console.error('Unexpected error in exercises API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    )
  }
}
