'use client'

import { useAuth } from '@/features/auth/hooks/use-auth'
import { useRouter } from 'next/navigation'

export default function ProfilePage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  // useEffect removed: redirect now synchronous
  if (!loading && user) {
    router.replace('/account')
    return null
  }

  if (loading) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="border-primary h-12 w-12 animate-spin rounded-full border-t-2 border-b-2" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="mb-4 text-2xl font-bold">Sign In Required</h1>
        <p className="mb-6 text-gray-600 dark:text-gray-300">
          Please sign in to view your profile.
        </p>
      </div>
    )
  }

  // Fallback: should never render
  return null
}
