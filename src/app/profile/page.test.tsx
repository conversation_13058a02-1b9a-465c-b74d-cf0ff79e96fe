import { render, waitFor } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'

/* ------------------------------------------------------------------
 * GLOBAL MOCKS
 * ------------------------------------------------------------------ */

// --- router / redirect spy -------------------------------------------------
const replace = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({ replace }),
}))

// --- auth hook mock --------------------------------------------------------
/*  We expose a mutable authState object that each test can configure
 *  before rendering the component. The mocked hook always returns
 *  the current snapshot of authState.
 */
const authState: { user: null | Record<string, unknown>; loading: boolean } = {
  user: null,
  loading: false,
}

vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({ ...authState }),
}))

// Helper setters for tests
const setAuthenticated = () => {
  authState.user = { id: 'u1' }
  authState.loading = false
}

const setUnauthenticated = () => {
  authState.user = null
  authState.loading = false
}

/* ------------------------------------------------------------------
 * TESTS
 * ------------------------------------------------------------------ */

describe('ProfilePage (redirect root)', () => {
  beforeEach(() => {
    // reset spies & default state
    vi.clearAllMocks()
    setUnauthenticated()
  })

  it('redirects to /account if authenticated', async () => {
    setAuthenticated()

    const Page = (await import('./page')).default
    render(<Page />)

    await waitFor(() => expect(replace).toHaveBeenCalledWith('/account'))
  })

  it('shows sign-in prompt if not authenticated', async () => {
    // auth state already unauthenticated by default
    const Page = (await import('./page')).default
    const { getByText } = render(<Page />)

    expect(getByText(/sign in required/i)).toBeInTheDocument()
  })
})
