'use client'

import { ExerciseCard } from '@/components/features/exercises/ExerciseCard'
import {
  type UseIntersectionObserverOptions,
  useIntersectionObserver,
} from '@/hooks/useIntersectionObserver'
import type { Exercise } from '@/types/exercises'
import { useCallback, useEffect, useRef, useState } from 'react'

// Reusing ObservedExerciseCard logic, could be moved to a shared file if used in more places
interface ObservedExerciseCardProps {
  exercise: Exercise
  onIntersectionChange: (
    id: string,
    entry: IntersectionObserverEntry | undefined,
    isIntersecting: boolean,
  ) => void
  isActive: boolean
  observerOptions: UseIntersectionObserverOptions
  className?: string // Pass through className for grid item styling if needed
}

const ObservedExerciseCard = ({
  exercise,
  onIntersectionChange,
  isActive,
  observerOptions,
  className,
}: ObservedExerciseCardProps) => {
  const [targetRef, isIntersecting, entry] =
    useIntersectionObserver<HTMLDivElement>(observerOptions)

  useEffect(() => {
    onIntersectionChange(exercise.id, entry, isIntersecting)
  }, [isIntersecting, entry, onIntersectionChange, exercise.id])

  return (
    <div ref={targetRef} className={`h-full ${className || ''}`.trim()}>
      <ExerciseCard exercise={exercise} isActive={isActive} />
    </div>
  )
}

type ExerciseLibraryListProps = {
  exercises: Exercise[]
}

export function ExerciseLibraryList({ exercises }: ExerciseLibraryListProps) {
  const [activeCardId, setActiveCardId] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const intersectingCardsRef = useRef<Map<string, IntersectionObserverEntry>>(
    new Map(),
  )

  useEffect(() => {
    const checkMobile = () => {
      const touchSupported =
        'ontouchstart' in window || navigator.maxTouchPoints > 0
      setIsMobile(touchSupported)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const updateActiveCard = useCallback(() => {
    if (!isMobile) {
      setActiveCardId(null)
      return
    }
    let topmostIntersectingCardId: string | null = null
    let minTop = Number.POSITIVE_INFINITY
    intersectingCardsRef.current.forEach((entry, id) => {
      if (entry.boundingClientRect.top < minTop) {
        minTop = entry.boundingClientRect.top
        topmostIntersectingCardId = id
      }
    })
    setActiveCardId(topmostIntersectingCardId)
  }, [isMobile])

  const handleIntersectionChange = useCallback(
    (
      id: string,
      entry: IntersectionObserverEntry | undefined,
      isCurrentlyIntersecting: boolean,
    ) => {
      if (entry) {
        if (isCurrentlyIntersecting) {
          intersectingCardsRef.current.set(id, entry)
        } else {
          intersectingCardsRef.current.delete(id)
        }
        updateActiveCard()
      }
    },
    [updateActiveCard],
  )

  const observerOptions: UseIntersectionObserverOptions = {
    threshold: 0.5, // Reverted to match homepage, rootMargin removed
  }

  if (exercises.length === 0) {
    return (
      <div className="text-center py-20">
        <p className="text-xl text-muted-foreground">
          No exercises found. Please check back later or add new ones!
        </p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
      {exercises.map((exercise) => (
        <ObservedExerciseCard
          key={exercise.id}
          exercise={exercise}
          onIntersectionChange={handleIntersectionChange}
          isActive={activeCardId === exercise.id}
          observerOptions={observerOptions}
          // className="" // Add any specific item class if needed, grid handles layout here
        />
      ))}
    </div>
  )
}
