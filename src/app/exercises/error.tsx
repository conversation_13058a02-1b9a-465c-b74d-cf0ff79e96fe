'use client'

import { But<PERSON> } from '@/components/Button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/Card'
import PageLayout from '@/components/PageLayout'
import { Frown } from 'lucide-react'
import { useEffect } from 'react'

export default function ExercisesError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Exercises section error:', error)
  }, [error])

  return (
    <PageLayout>
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader>
            <div className="flex flex-col items-center justify-center mb-4">
              <Frown className="w-16 h-16 text-yellow-500 mb-2" />
              <CardTitle className="text-2xl font-semibold text-center text-primary">
                Oops! Exercise Error
              </CardTitle>
            </div>
            <CardDescription className="text-center">
              An error occurred while loading the exercise library.
            </CardDescription>
          </CardHeader>
          <CardContent className="px-6 py-4">
            {error.message && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-auto max-h-[200px] text-sm font-mono">
                {error.message}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={reset} className="w-full max-w-xs">
              Try again
            </Button>
          </CardFooter>
        </Card>
      </div>
    </PageLayout>
  )
}
