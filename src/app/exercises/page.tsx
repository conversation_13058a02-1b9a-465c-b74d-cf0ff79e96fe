import PageLayout from '@/components/PageLayout'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import type { Exercise } from '@/types/exercises'
import { cookies } from 'next/headers'
import { ExerciseLibraryList } from './ExerciseLibraryList'

export default async function ExercisesPage() {
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const { data, error } = await supabase.from('exercises').select('*')

  if (error) {
    console.error('Error fetching exercises:', error)
  }

  const exercisesData = data || []

  const exerciseList: Exercise[] = exercisesData.map((dbExercise) => {
    return {
      id: dbExercise.id,
      title: dbExercise.title,
      description: dbExercise.description,
      video_tutorial_url: dbExercise.video_tutorial_url,
      equipment_required: dbExercise.equipment_required,
      created_by: dbExercise.created_by,
      created_at: dbExercise.created_at,
      updated_at: dbExercise.updated_at,
      image_url: '/images/logo.webp',
    }
  })

  return (
    <PageLayout>
      <div className="container mx-auto py-8 md:py-12">
        <div className="flex justify-between items-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-card-foreground">
            Exercise Library
          </h1>
        </div>

        <ExerciseLibraryList exercises={exerciseList} />
      </div>
    </PageLayout>
  )
}
