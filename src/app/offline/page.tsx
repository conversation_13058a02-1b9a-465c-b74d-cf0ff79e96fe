import { But<PERSON> } from '@/components/Button'
import { RefreshCcw } from 'lucide-react'

export const metadata = {
  title: 'Offline - Armwrestling Power Arena',
  description: 'You are currently offline',
}

export default function OfflinePage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 py-12 text-center">
      <div className="mb-6">
        <RefreshCcw className="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500" />
      </div>
      <h1 className="text-3xl font-bold mb-4">You're offline</h1>
      <p className="text-lg mb-8 max-w-md mx-auto text-gray-600 dark:text-gray-400">
        There seems to be a problem with your connection. Please check your
        internet and try again.
      </p>
      <Button
        onClick={() => window.location.reload()}
        className="flex items-center gap-2"
      >
        <RefreshCcw className="w-4 h-4" />
        Try again
      </Button>
    </div>
  )
}
