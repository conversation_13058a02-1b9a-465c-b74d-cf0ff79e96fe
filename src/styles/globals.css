@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-rgb: 10, 10, 10;
  }
}

html {
  scrollbar-width: none;
}

body {
  background: rgb(var(--background-rgb));
}

/* Custom focus styles */
.custom-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Utility classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card {
  background: var(--card);
  border-radius: var(--radius);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-gradient-start: var(--gradient-start);
  --color-gradient-mid: var(--gradient-mid);
  --color-gradient-end: var(--gradient-end);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply text-foreground;
    background: linear-gradient(
        135deg,
        var(--gradient-start) 0%,
        var(--gradient-mid) 50%,
        var(--gradient-end) 100%
      )
      no-repeat center center fixed;
    background-size: cover;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  img {
    -webkit-user-drag: none;
    user-select: none;
  }
}

@layer base {
  :root {
    /* Custom theme variables based on theme-system.md */
    --background: hsl(0, 0%, 97%); /* #F8F8F8 - Light Mode Background */
    --foreground: hsl(0, 0%, 26%); /* #424242 - Light Mode Text (Body) */
    --card: hsl(0, 0%, 100%); /* #FFFFFF - Light Mode Panels */
    --card-foreground: hsl(0, 0%, 26%); /* #424242 - Text on Light Panels */
    --popover: hsl(0, 0%, 100%); /* #FFFFFF - Light Mode Popover BG */
    --popover-foreground: hsl(0, 0%, 26%); /* #424242 - Text on Light Popover */
    --primary: hsl(16, 100%, 50%); /* #ff4500 - Burnt Orange (from landing.css) */
    --primary-foreground: hsl(0, 0%, 100%); /* #FFFFFF - Text on Primary */
    --primary-hover: hsl(16, 100%, 40%); /* #cc3700 - Burnt Orange Hover (from landing.css) */
    --secondary: hsl(173, 57%, 39%); /* #2A9D8F - Secondary Action Teal */
    --secondary-foreground: hsl(0, 0%, 100%); /* #FFFFFF - Text on Secondary Teal */
    --muted: hsl(0, 0%, 94%); /* #F0F0F0 - Light Muted */
    --muted-foreground: hsl(0, 0%, 46%); /* #757575 - Light Muted Foreground */
    --accent: hsl(176, 46%, 47%); /* #FFBA08 - Accent Highlight Yellow */
    --accent-foreground: hsl(0, 0%, 0%); /* #000000 - Text on Accent Yellow */
    --destructive: hsl(0, 84%, 60%); /* Standard Red - Kept from existing */
    --destructive-foreground: hsl(0, 0%, 98%); /* Standard Light - Kept from existing */
    --border: hsl(0, 0%, 88%); /* #E0E0E0 - Light Border */
    --input: hsl(0, 0%, 88%); /* #E0E0E0 - Light Input */
    --ring: hsl(16, 100%, 50%); /* #ff4500 - Ring matches new Primary Burnt Orange */
    --radius: 0.625rem;

    --gradient-start: hsl(275, 65%, 73%);
    --gradient-mid: hsl(225, 55%, 66%);
    --gradient-end: hsl(167, 94%, 46%);

    /* Original OKLCH Chart variables - Kept as not in docs */
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* Original OKLCH Sidebar variables - Kept as not in docs
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0); */

    /* Medal Colors from theme-system.md */
    --medal-gold: hsl(50, 100%, 50%); /* #FFD700 */
    --medal-silver: hsl(0, 0%, 75%); /* #C0C0C0 */
    --medal-bronze: hsl(30, 60%, 50%); /* #CD7F32 */
    --medal-platinum: hsl(40, 7%, 90%); /* #E5E4E2 - New */
    --medal-diamond: hsl(195, 100%, 86%); /* #B9F2FF - New */
  }

  .dark {
    /* Custom theme variables for dark mode based on theme-system.md */
    --background: hsl(223, 30%, 12%); /* #121212 - Dark Mode Background */
    --foreground: hsl(0, 0%, 88%); /* #E0E0E0 - Dark Mode Text (Body) */
    --card: hsl(214.99 31% 17%);
    --card-foreground: hsl(0, 0%, 88%); /* #E0E0E0 - Text on Dark Panels */
    --popover: hsl(0, 3%, 29%); /* #4d4646 - Dark Mode Popover BG */
    --popover-foreground: hsl(0, 0%, 88%); /* #E0E0E0 - Text on Dark Popover */
    --primary: hsl(16, 85%, 39%); /* #ff4500 - Burnt Orange (from landing.css) */
    --primary-foreground: hsl(0, 0%, 100%); /* #FFFFFF - Text on Primary */
    --primary-hover: hsl(16, 69%, 39%); /* #cc3700 - Burnt Orange Hover (from landing.css) */
    --secondary: hsl(173, 57%, 39%); /* #2A9D8F - Secondary Action Teal */
    --secondary-foreground: hsl(0, 0%, 100%); /* #FFFFFF - Text on Secondary Teal */
    --muted: hsl(0, 0%, 17%); /* #2C2C2C - Dark Muted */
    --muted-foreground: hsl(0, 0%, 63%); /* #A0A0A0 - Dark Muted Foreground */
    --accent: hsl(176, 46%, 47%); /* #FFBA08 - Accent Highlight Yellow */
    --accent-foreground: hsl(0, 0%, 0%); /* #000000 - Text on Accent Yellow */
    --destructive: hsl(0, 65%, 30%); /* Standard Dark Red - Kept from existing */
    --destructive-foreground: hsl(0, 0%, 98%); /* Standard Light - Kept from existing */
    --border: hsl(0, 0%, 15%); /* #262626 - Dark Border */
    --input: hsl(0, 0%, 15%); /* #262626 - Dark Input */
    --ring: hsl(16, 88%, 29%); /* #ff4500 - Ring matches new Primary Burnt Orange */

    --gradient-start: hsl(274.93, 82%, 38%);
    --gradient-mid: hsl(225.35, 84%, 49%);
    --gradient-end: hsl(173, 58%, 34%);

    /* Original OKLCH Chart variables for dark mode - Kept as not in docs */
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    /* Original OKLCH Sidebar variables for dark mode - Kept as not in docs */
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);

    /* Medal Colors (re-declared for dark, though likely same as light) */
    --medal-gold: hsl(50, 100%, 50%); /* #FFD700 */
    --medal-silver: hsl(0, 0%, 75%); /* #C0C0C0 */
    --medal-bronze: hsl(30, 60%, 50%); /* #CD7F32 */
    --medal-platinum: hsl(40, 7%, 90%); /* #E5E4E2 - New */
    --medal-diamond: hsl(195, 100%, 86%); /* #B9F2FF - New */
  }

  * {
    @apply border-border outline-ring/50;
  }
}
