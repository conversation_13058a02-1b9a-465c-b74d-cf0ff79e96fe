// Placeholder for Supabase generated types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON>son }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          gender: string | null
          weight_category: string | null
          // Add other profile fields as needed
        }
        Insert: {
          id: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          // Add other profile fields as needed
        }
        Update: {
          id?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          // Add other profile fields as needed
        }
      }
      exercises: {
        Row: {
          id: string
          title: string | null
          description: string | null
          medal_thresholds: Json | null // Assuming medal_thresholds is a JSONB type
          // Add other exercise fields as needed
        }
        Insert: {
          id: string
          title?: string | null
          description?: string | null
          medal_thresholds?: Json | null
          // Add other exercise fields as needed
        }
        Update: {
          id?: string
          title?: string | null
          description?: string | null
          medal_thresholds?: Json | null
          // Add other exercise fields as needed
        }
      }
      submissions: {
        Row: {
          id: string
          user_id: string
          exercise_id: string
          video_url: string
          weight_lifted: string // Assuming this is numeric but stored as text, adjust if it's a numeric type
          status: string // e.g., 'pending', 'approved', 'rejected'
          submitted_at: string // timestamp
          evaluated_at: string | null // timestamp
          evaluated_by: string | null // user id of evaluator
          private_comments: string | null
          public_comments: string | null
          medal_awarded: string | null // e.g., 'gold', 'silver', 'bronze', 'none'
          points_awarded: number
          notes: string | null
          // Add other submission fields as needed
        }
        Insert: {
          id: string
          user_id: string
          exercise_id: string
          video_url: string
          weight_lifted: string
          status?: string
          submitted_at?: string
          evaluated_at?: string | null
          evaluated_by?: string | null
          private_comments?: string | null
          public_comments?: string | null
          medal_awarded?: string | null
          points_awarded?: number
          notes?: string | null
          // Add other submission fields as needed
        }
        Update: {
          id?: string
          user_id?: string
          exercise_id?: string
          video_url?: string
          weight_lifted?: string
          status?: string
          submitted_at?: string
          evaluated_at?: string | null
          evaluated_by?: string | null
          private_comments?: string | null
          public_comments?: string | null
          medal_awarded?: string | null
          points_awarded?: number
          notes?: string | null
          // Add other submission fields as needed
        }
      }
      // Add other tables as needed
    }
    Views: {
      // Define views if any
    } & Record<string, unknown>
    Functions: {
      // Define functions if any
    } & Record<string, unknown>
    Enums: {
      // Define enums if any
    } & Record<string, unknown>
    CompositeTypes: {
      // Define composite types if any
    } & Record<string, unknown>
  }
}
