import type { Database } from './database.types'

// Assuming Profile and Exercise types are defined elsewhere or directly from Database types
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Exercise = Database['public']['Tables']['exercises']['Row']

export type Submission = Database['public']['Tables']['submissions']['Row']

export interface SubmissionWithRelations extends Submission {
  exercises: Exercise | null // Assuming exercises can be null
  profiles: Profile | null // Assuming profiles can be null
}
