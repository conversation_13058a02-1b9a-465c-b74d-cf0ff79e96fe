// Common type definitions for the Armwrestling Power Arena application

/**
 * User profile type
 */
export interface User {
  id: string
  name: string
  email: string
  profilePicture?: string
}

/**
 * Armwrestling session type
 */
export interface ArmwrestlingSession {
  id: string
  userId: string
  date: Date
  duration: number // in minutes
  exercises: Exercise[]
  notes?: string
}

/**
 * Exercise type
 */
export interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight?: number // in kg
  duration?: number // in seconds
}
