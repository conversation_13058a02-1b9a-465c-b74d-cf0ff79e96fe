'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import type { <PERSON><PERSON> } from '@/types/database.types'
import { useForm } from '@tanstack/react-form'
import { useState } from 'react'
import { toast } from 'sonner'
import { DecisionToggleGroup } from './components/DecisionToggleGroup'
import { MedalSelector } from './components/MedalSelector'
import { MEDAL_POINTS, type MedalType } from './constants/medals'
import { evaluateSubmission } from './evaluationActions'

// Define the interface for our form values
interface EvaluationFormValues {
  submissionId: string
  feedback: string
  decision: 'approve' | 'reject' | null
  medalAwarded: MedalType | 'none' | undefined
  pointsAwarded: number | undefined
}

// Define the props for the component
interface EvaluationFormProps {
  submissionId: string
  exerciseId: string
  userId: string
  gender: string
  weightCategory: string
  weightLifted: number
  medalThresholds: Json | Record<string, Record<string, number>>
  initialFeedback?: string
  onEvaluationComplete?: () => void
}

export function EvaluationForm({
  submissionId,
  initialFeedback = '',
  onEvaluationComplete,
}: EvaluationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedMedal, setSelectedMedal] = useState<MedalType | null>(null)
  const [decision, setDecision] = useState<'approve' | 'reject' | null>(null)

  // Handle medal selection
  const handleMedalSelect = (medal: MedalType) => {
    if (decision === 'approve') {
      setSelectedMedal(medal)
      form.setFieldValue('medalAwarded', medal)
      form.setFieldValue('pointsAwarded', MEDAL_POINTS[medal])
    }
  }

  // Handle decision selection
  const handleDecisionSelect = (newDecision: 'approve' | 'reject') => {
    setDecision(newDecision)
    form.setFieldValue('decision', newDecision)

    if (newDecision === 'reject') {
      setSelectedMedal(null)
      form.setFieldValue('medalAwarded', 'none')
      form.setFieldValue('pointsAwarded', 0)
    }
  }

  // Initialize the form with TanStack Form
  const form = useForm({
    defaultValues: {
      submissionId,
      feedback: initialFeedback,
      decision: null as 'approve' | 'reject' | null,
      medalAwarded: undefined as MedalType | 'none' | undefined,
      pointsAwarded: 0 as number | undefined,
    },
    onSubmit: async ({ value }: { value: EvaluationFormValues }) => {
      setIsSubmitting(true)
      try {
        if (!value.decision) {
          toast.error('A decision (approve/reject) must be made.')
          setIsSubmitting(false)
          return
        }

        const formData: {
          submissionId: string
          feedback: string
          decision: 'approve' | 'reject'
          medalAwarded: MedalType | 'none' | undefined
          pointsAwarded: number | undefined
        } = {
          ...value,
          decision: value.decision,
        }

        if (formData.decision === 'reject') {
          formData.pointsAwarded = 0
          formData.medalAwarded = 'none'
        } else if (formData.decision === 'approve') {
          if (!formData.medalAwarded || formData.medalAwarded === 'none') {
            toast.error('A medal must be selected for an approved submission.')
            setIsSubmitting(false)
            return
          }
          if (
            typeof formData.pointsAwarded !== 'number' ||
            formData.pointsAwarded === 0
          ) {
            const currentMedal = formData.medalAwarded as MedalType
            if (MEDAL_POINTS[currentMedal]) {
              formData.pointsAwarded = MEDAL_POINTS[currentMedal]
            } else {
              toast.error('Could not determine points for the selected medal.')
              setIsSubmitting(false)
              return
            }
          }
        }

        const result = await evaluateSubmission(formData)

        if (result.success) {
          toast.success(result.message || 'Evaluation submitted successfully')
          if (onEvaluationComplete) {
            onEvaluationComplete()
          }
        } else {
          toast.error(result.error || 'Failed to submit evaluation')
        }
      } catch (error) {
        console.error('Error submitting evaluation:', error)
        toast.error('An unexpected error occurred')
      } finally {
        setIsSubmitting(false)
      }
    },
  })

  const { Field } = form

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
      className="flex flex-col gap-6"
    >
      {/* Decision Toggle */}
      <DecisionToggleGroup
        value={decision}
        onChange={handleDecisionSelect}
        disabled={isSubmitting}
      />

      {/* Medal Selector */}
      <MedalSelector
        selected={selectedMedal}
        onSelect={handleMedalSelect}
        disabled={decision !== 'approve'}
      />

      {/* Feedback Field */}
      <Field name="feedback">
        {(field) => (
          <div className="flex flex-col gap-2">
            <Label htmlFor={field.name}>Feedback (Optional)</Label>
            <Textarea
              id={field.name}
              placeholder="Provide feedback or reasons for your decision..."
              rows={4}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              onBlur={field.handleBlur}
            />
          </div>
        )}
      </Field>

      {/* Submit Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          type="submit"
          disabled={
            isSubmitting ||
            !decision ||
            (decision === 'approve' && !selectedMedal)
          }
        >
          {isSubmitting ? 'Submitting...' : 'Submit Decision'}
        </Button>
      </div>
    </form>
  )
}
