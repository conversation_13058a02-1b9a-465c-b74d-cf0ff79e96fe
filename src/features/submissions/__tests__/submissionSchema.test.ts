import { describe, expect, it } from 'vitest'
import { submissionSchema } from '../submissionSchema'

describe('submissionSchema', () => {
  it('accepts valid data', () => {
    const valid = {
      exerciseId: '123e4567-e89b-12d3-a456-************',
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: 100,
      notes: 'Felt strong today.',
    }
    expect(() => submissionSchema.parse(valid)).not.toThrow()
  })

  it('rejects invalid exerciseId (not uuid)', () => {
    const invalid = {
      exerciseId: 'not-a-uuid',
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: 100,
      notes: '',
    }
    expect(() => submissionSchema.parse(invalid)).toThrow(
      /Exercise is required/,
    )
  })

  it('rejects invalid videoUrl', () => {
    const invalid = {
      exerciseId: '123e4567-e89b-12d3-a456-************',
      videoUrl: 'not-a-url',
      weightLifted: 100,
      notes: '',
    }
    expect(() => submissionSchema.parse(invalid)).toThrow(/valid video URL/)
  })

  it('rejects negative weightLifted', () => {
    const invalid = {
      exerciseId: '123e4567-e89b-12d3-a456-************',
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: -5,
      notes: '',
    }
    expect(() => submissionSchema.parse(invalid)).toThrow(/positive number/)
  })

  it('accepts empty notes', () => {
    const valid = {
      exerciseId: '123e4567-e89b-12d3-a456-************',
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: 100,
      notes: '',
    }
    expect(() => submissionSchema.parse(valid)).not.toThrow()
  })

  it('rejects notes over 500 chars', () => {
    const invalid = {
      exerciseId: '123e4567-e89b-12d3-a456-************',
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: 100,
      notes: 'a'.repeat(501),
    }
    expect(() => submissionSchema.parse(invalid)).toThrow(/500 characters/)
  })
})
