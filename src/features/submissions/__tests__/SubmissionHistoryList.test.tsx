import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import SubmissionHistoryList, {
  type SubmissionWithExercise,
} from '../SubmissionHistoryList'

// Mock date-fns to avoid time-based test failures
vi.mock('date-fns', () => ({
  formatDistanceToNow: () => '2 days ago',
}))

describe('SubmissionHistoryList', () => {
  const mockSubmissions: SubmissionWithExercise[] = [
    {
      id: 1,
      user_id: 'user-123',
      exercise_id: 101,
      exercise_title: 'Bench Press',
      video_url: 'https://example.com/video1',
      weight_lifted: 100,
      notes: 'Felt good',
      status: 'approved',
      submitted_at: '2023-05-01T12:00:00Z',
      evaluated_at: '2023-05-02T12:00:00Z',
      evaluated_by: 'evaluator-123',
      points_awarded: 50,
      medal_awarded: 'gold',
      private_comments: 'Great form!',
    },
    {
      id: 2,
      user_id: 'user-123',
      exercise_id: 102,
      exercise_title: 'Deadlift',
      video_url: 'https://example.com/video2',
      weight_lifted: 150,
      notes: null,
      status: 'pending',
      submitted_at: '2023-05-03T12:00:00Z',
      evaluated_at: null,
      evaluated_by: null,
      points_awarded: 0,
      medal_awarded: 'none',
      private_comments: null,
    },
  ]

  it('renders submissions correctly', () => {
    render(<SubmissionHistoryList submissions={mockSubmissions} />)

    // Check exercise titles
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Deadlift')).toBeInTheDocument()

    // Check status badges
    expect(screen.getByText('Approved')).toBeInTheDocument()
    expect(screen.getByText('Pending')).toBeInTheDocument()

    // Check weights
    expect(screen.getByText('100 kg')).toBeInTheDocument()
    expect(screen.getByText('150 kg')).toBeInTheDocument()

    // Check medal
    expect(screen.getByText('Gold')).toBeInTheDocument()

    // Check points
    expect(screen.getByText('+50')).toBeInTheDocument()

    // Check video links
    const videoLinks = screen.getAllByText('Watch')
    expect(videoLinks).toHaveLength(2)
    expect(videoLinks[0]).toHaveAttribute('href', 'https://example.com/video1')
    expect(videoLinks[1]).toHaveAttribute('href', 'https://example.com/video2')

    // Check feedback
    expect(screen.getByText('Great form!')).toBeInTheDocument()
  })

  it('renders empty state when no submissions', () => {
    render(<SubmissionHistoryList submissions={[]} />)
    expect(
      screen.getByText("You haven't submitted any performances yet."),
    ).toBeInTheDocument()
    expect(
      screen.getByRole('link', { name: 'Submit Your First Performance' }),
    ).toBeInTheDocument()
  })
})
