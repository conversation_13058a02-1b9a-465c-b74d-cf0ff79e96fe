import * as credentialUtils from '@/libs/supabase/credentials' // To mock getSupabaseCredentials
import * as supabaseSsr from '@supabase/ssr' // To mock createBrowserClient
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { toast } from 'sonner' // To mock toast
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ZodIssueCode } from 'zod' // Import ZodIssueCode
import { SubmissionForm } from './SubmissionForm'
import * as actions from './actions' // To mock submitPerformance
import { type SubmissionFormData, submissionSchema } from './submissionSchema' // For types and potentially direct use if not mocking fully

// Mock Next.js navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(),
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock Supabase
const mockSupabaseClient = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
}
vi.mock('@/libs/supabase/credentials')
vi.mock('@supabase/ssr')

// Mock server action
vi.mock('./actions')

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

const mockExercises = [
  { id: '1', title: 'Exercise A' },
  { id: '2', title: 'Exercise B' },
]

describe('SubmissionForm', () => {
  let useSearchParamsMock: any

  beforeEach(() => {
    useSearchParamsMock = vi.mocked(require('next/navigation').useSearchParams)
    useSearchParamsMock.mockReturnValue(new URLSearchParams())

    vi.mocked(credentialUtils.getSupabaseCredentials).mockReturnValue({
      supabaseUrl: 'test-url',
      supabaseAnonKey: 'test-key',
    })
    vi.mocked(supabaseSsr.createBrowserClient).mockReturnValue(
      mockSupabaseClient as any,
    )

    // Reset mocks
    mockPush.mockClear()
    vi.mocked(toast.success).mockClear()
    vi.mocked(toast.error).mockClear()
    vi.mocked(actions.submitPerformance).mockClear()
    mockSupabaseClient.from.mockClear()
    mockSupabaseClient.select.mockClear()
    mockSupabaseClient.order.mockClear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  const renderComponent = () => render(<SubmissionForm />)

  it('renders all form fields', () => {
    renderComponent()
    expect(
      screen.getByRole('heading', { name: /submit performance/i }),
    ).toBeInTheDocument()
    // Exercise field will be tested more specifically
    expect(screen.getByLabelText(/video url/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/weight lifted \(kg\)/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/notes \(optional\)/i)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /submit performance/i }),
    ).toBeInTheDocument()
  })

  describe('Exercise Field', () => {
    it('shows dropdown when no exerciseId param is present and fetches exercises', async () => {
      mockSupabaseClient.select.mockResolvedValueOnce({
        data: mockExercises,
        error: null,
      })
      renderComponent()

      expect(screen.getByLabelText('Select exercise')).toBeInTheDocument()
      expect(
        screen.getByRole('combobox', { name: /select exercise/i }),
      ).toHaveTextContent(/loading.../i)

      await waitFor(() => {
        expect(
          screen.getByRole('combobox', { name: /select exercise/i }),
        ).toHaveTextContent(/select exercise/i)
      })

      fireEvent.mouseDown(
        screen.getByRole('combobox', { name: /select exercise/i }),
      )

      await waitFor(() => {
        expect(
          screen.getByRole('option', { name: 'Exercise A' }),
        ).toBeInTheDocument()
        expect(
          screen.getByRole('option', { name: 'Exercise B' }),
        ).toBeInTheDocument()
      })
    })

    it('shows loading error for exercises if fetch fails', async () => {
      mockSupabaseClient.select.mockResolvedValueOnce({
        data: null,
        error: { message: 'Fetch failed' },
      })
      renderComponent()
      await waitFor(() => {
        expect(
          screen.getByText('Failed to load exercises.'),
        ).toBeInTheDocument()
      })
    })

    it('shows fixed exercise input when exerciseId param is present', () => {
      useSearchParamsMock.mockReturnValue(
        new URLSearchParams('exerciseId=fixedId&title=Fixed%20Exercise'),
      )
      renderComponent()
      const exerciseInput = screen.getByLabelText(
        /exercise/i,
      ) as HTMLInputElement
      expect(exerciseInput).toBeInTheDocument()
      expect(exerciseInput).toHaveValue('Fixed Exercise')
      expect(exerciseInput).toBeDisabled()
    })
  })

  describe('Client-Side Validation', () => {
    beforeEach(async () => {
      // Ensure exercises are loaded for selection
      mockSupabaseClient.select.mockResolvedValueOnce({
        data: mockExercises,
        error: null,
      })
      renderComponent()
      await waitFor(() =>
        expect(
          screen.getByRole('combobox', { name: /select exercise/i }),
        ).not.toHaveTextContent(/loading/i),
      )
    })

    const submitForm = () =>
      fireEvent.submit(
        screen.getByRole('form', { name: /video submission form/i }),
      )

    it('requires exercise selection', async () => {
      submitForm()
      await waitFor(() => {
        expect(
          screen.getByText('String must contain at least 1 character(s)'),
        ).toBeInTheDocument() // Zod default for non-empty
      })
    })

    it('requires video URL and valid format', async () => {
      // Select an exercise first
      fireEvent.mouseDown(
        screen.getByRole('combobox', { name: /select exercise/i }),
      )
      await waitFor(() =>
        screen.getByRole('option', { name: mockExercises[0].title }),
      )
      fireEvent.click(
        screen.getByRole('option', { name: mockExercises[0].title }),
      )

      const videoUrlInput = screen.getByLabelText(/video url/i)

      // Required
      submitForm()
      await waitFor(() => {
        expect(
          screen.getAllByText('String must contain at least 1 character(s)')[0],
        ).toBeInTheDocument()
      })

      // Invalid URL
      fireEvent.change(videoUrlInput, { target: { value: 'not-a-url' } })
      submitForm()
      await waitFor(() => {
        expect(screen.getByText('Invalid url')).toBeInTheDocument()
      })
    })

    it('requires weight lifted and must be positive', async () => {
      // Select an exercise first
      fireEvent.mouseDown(
        screen.getByRole('combobox', { name: /select exercise/i }),
      )
      await waitFor(() =>
        screen.getByRole('option', { name: mockExercises[0].title }),
      )
      fireEvent.click(
        screen.getByRole('option', { name: mockExercises[0].title }),
      )

      const weightInput = screen.getByLabelText(/weight lifted \(kg\)/i)

      // Required
      submitForm()
      await waitFor(() => {
        // Will also show video URL error
        expect(
          screen.getAllByText('Number must be greater than 0').length,
        ).toBeGreaterThanOrEqual(1)
      })

      // Non-positive
      fireEvent.change(weightInput, { target: { value: '0' } })
      submitForm()
      await waitFor(() => {
        expect(
          screen.getByText('Number must be greater than 0'),
        ).toBeInTheDocument()
      })

      fireEvent.change(weightInput, { target: { value: '-10' } })
      submitForm()
      await waitFor(() => {
        expect(
          screen.getByText('Number must be greater than 0'),
        ).toBeInTheDocument()
      })
    })
  })

  describe('Form Submission', () => {
    const validFormData = {
      exerciseId: mockExercises[0].id,
      videoUrl: 'https://example.com/video.mp4',
      weightLifted: '100', // Input value is string
      notes: 'Good form',
    }

    const fillForm = async (data: Partial<SubmissionFormData>) => {
      if (data.exerciseId) {
        // If not preselected via params
        if (!useSearchParamsMock().get('exerciseId')) {
          fireEvent.mouseDown(
            screen.getByRole('combobox', { name: /select exercise/i }),
          )
          await waitFor(() =>
            screen.getByRole('option', {
              name:
                mockExercises.find((ex) => ex.id === data.exerciseId)?.title ??
                '',
            }),
          )
          fireEvent.click(
            screen.getByRole('option', {
              name:
                mockExercises.find((ex) => ex.id === data.exerciseId)?.title ??
                '',
            }),
          )
        }
      }
      if (data.videoUrl) {
        fireEvent.change(screen.getByLabelText(/video url/i), {
          target: { value: data.videoUrl },
        })
      }
      if (data.weightLifted) {
        fireEvent.change(screen.getByLabelText(/weight lifted \(kg\)/i), {
          target: { value: String(data.weightLifted) },
        })
      }
      if (data.notes) {
        fireEvent.change(screen.getByLabelText(/notes \(optional\)/i), {
          target: { value: data.notes },
        })
      }
    }

    const submitForm = () =>
      fireEvent.submit(
        screen.getByRole('form', { name: /video submission form/i }),
      )

    beforeEach(async () => {
      mockSupabaseClient.select.mockResolvedValueOnce({
        data: mockExercises,
        error: null,
      })
      renderComponent()
      await waitFor(() =>
        expect(
          screen.getByRole('combobox', { name: /select exercise/i }),
        ).not.toHaveTextContent(/loading/i),
      )
    })

    it('calls submitPerformance with correct FormData and shows success', async () => {
      vi.mocked(actions.submitPerformance).mockResolvedValueOnce({
        success: true,
        message: 'Submission successful!',
      })

      await fillForm(validFormData as any) // Cast because weightLifted is string here for input
      submitForm()

      const submitButton = screen.getByRole('button', {
        name: /submitting.../i,
      })
      expect(submitButton).toBeDisabled()
      await waitFor(() =>
        expect(submitButton).toHaveTextContent(/submitting.../i),
      )

      await waitFor(() => {
        expect(actions.submitPerformance).toHaveBeenCalledTimes(1)
        const calledWithFormData = vi.mocked(actions.submitPerformance).mock
          .calls[0][1]
        expect(calledWithFormData.get('exerciseId')).toBe(
          validFormData.exerciseId,
        )
        expect(calledWithFormData.get('videoUrl')).toBe(validFormData.videoUrl)
        expect(calledWithFormData.get('weightLifted')).toBe(
          String(validFormData.weightLifted),
        ) // Server action expects string
        expect(calledWithFormData.get('notes')).toBe(validFormData.notes)
      })

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Submission successful!')
        expect(mockPush).toHaveBeenCalledWith('/account')
        // Check if form is reset (e.g., videoUrl is empty)
        expect(screen.getByLabelText(/video url/i)).toHaveValue('')
      })
      expect(
        screen.getByRole('button', { name: /submit performance/i }),
      ).not.toBeDisabled()
    })

    it('handles server action general error', async () => {
      vi.mocked(actions.submitPerformance).mockResolvedValueOnce({
        success: false,
        error: 'Server failed terribly',
      })

      await fillForm(validFormData as any)
      submitForm()

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Server failed terribly')
        expect(screen.getByText('Server failed terribly')).toBeInTheDocument()
      })
      expect(
        screen.getByRole('button', { name: /submit performance/i }),
      ).not.toBeDisabled()
    })

    it('handles server action validation errors', async () => {
      const validationErrors = [
        {
          code: ZodIssueCode.custom,
          path: ['videoUrl'],
          message: 'Server says invalid video URL',
        },
        {
          code: ZodIssueCode.custom,
          path: ['weightLifted'],
          message: 'Server says weight too low',
        },
      ] as any // Cast as any to satisfy ZodIssue[] for the mock, focusing on path/message
      vi.mocked(actions.submitPerformance).mockResolvedValueOnce({
        success: false,
        validationErrors,
      })

      await fillForm(validFormData as any)
      submitForm()

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'Please check the form for validation errors from the server.',
        )
        expect(
          screen.getByText(
            /please correct the following server-detected issues/i,
          ),
        ).toBeInTheDocument()
        expect(
          screen.getByText('videoUrl: Server says invalid video URL'),
        ).toBeInTheDocument()
        expect(
          screen.getByText('weightLifted: Server says weight too low'),
        ).toBeInTheDocument()
      })
      expect(
        screen.getByRole('button', { name: /submit performance/i }),
      ).not.toBeDisabled()
    })
  })
})
