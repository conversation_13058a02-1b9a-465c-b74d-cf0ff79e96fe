'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useAuth } from '@/features/auth/hooks/use-auth'
import { cn } from '@/libs/utils'
import { useForm } from '@tanstack/react-form'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'
import type { z } from 'zod/v4'
import { submitPerformance } from './actions'
import { ExerciseSelector } from './components/ExerciseSelector'
import { SubmissionErrorBoundary } from './components/SubmissionErrorBoundary'
import {
  SUBMISSION_ERRORS,
  SUBMISSION_SUCCESS,
} from './constants/error-messages'
import { type SubmissionFormData, submissionSchema } from './submissionSchema'

/**
 * SubmissionForm allows athletes to submit a video performance.
 * Handles both pre-selected and selectable exercise flows with improved security.
 */
const SubmissionFormContent: React.FC = () => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { user } = useAuth()

  const exerciseIdParam = searchParams.get('exerciseId')
  const exerciseTitleParam = searchParams.get('title')

  const [isSubmittingToServer, setIsSubmittingToServer] = useState(false)
  const [serverError, setServerError] = useState<string | null>(null)
  const [serverFieldErrors, setServerFieldErrors] = useState<z.ZodIssue[]>([])

  const form = useForm({
    defaultValues: {
      exerciseId: exerciseIdParam ?? '',
      videoUrl: '',
      weightLifted: '' as unknown as number, // Schema coerces string to number
      notes: '',
    },
    onSubmit: async ({ value, formApi }) => {
      if (!user) {
        toast.error(SUBMISSION_ERRORS.UNAUTHORIZED)
        return
      }

      setIsSubmittingToServer(true)
      setServerError(null)
      setServerFieldErrors([])

      try {
        const formData = new FormData()
        formData.append('exerciseId', value.exerciseId)
        formData.append('videoUrl', value.videoUrl)
        formData.append('weightLifted', String(value.weightLifted ?? ''))
        if (value.notes) {
          formData.append('notes', value.notes)
        }

        const result = await submitPerformance(undefined, formData)

        if (result.success) {
          toast.success(result.message || SUBMISSION_SUCCESS.SUBMITTED)
          formApi.reset()
          router.push('/account/submissions')
        } else {
          if (result.error) {
            toast.error(result.error)
            setServerError(result.error)
          }
          if (result.validationErrors && result.validationErrors.length > 0) {
            setServerFieldErrors(result.validationErrors)
            if (!result.error) {
              toast.error(
                'Please check the form for validation errors from the server.',
              )
            }
          }
        }
      } catch (error) {
        console.error('Unexpected error during submission:', error)
        const errorMessage =
          error instanceof Error
            ? error.message
            : SUBMISSION_ERRORS.UNEXPECTED_ERROR
        toast.error(errorMessage)
        setServerError(errorMessage)
      } finally {
        setIsSubmittingToServer(false)
      }
    },
  })

  const getClientFieldError = (fieldName: keyof SubmissionFormData) => {
    const fieldMeta = form.getFieldMeta(fieldName)
    return fieldMeta?.errors && fieldMeta.errors.length > 0
      ? fieldMeta.errors.join(', ')
      : undefined
  }

  const validateField = (
    value: unknown,
    fieldName: keyof SubmissionFormData,
  ): string | undefined => {
    // Create a partial form data object with just this field
    const partialData = {
      exerciseId: fieldName === 'exerciseId' ? value : '',
      videoUrl: fieldName === 'videoUrl' ? value : '',
      weightLifted: fieldName === 'weightLifted' ? value : 0,
      notes: fieldName === 'notes' ? value : '',
    }

    const result = submissionSchema.safeParse(partialData)

    if (result.success) {
      return undefined
    }

    // Find the error for this specific field
    const fieldError = result.error.issues.find(
      (err: z.ZodIssue) => err.path.length > 0 && err.path[0] === fieldName,
    )

    return fieldError?.message
  }

  if (!user) {
    return (
      <div
        className={cn(
          'bg-background mx-auto w-full max-w-lg space-y-6 rounded-lg p-6 shadow-md',
        )}
      >
        <h2 className="mb-6 text-center text-2xl font-semibold">
          Submit Performance
        </h2>
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {SUBMISSION_ERRORS.UNAUTHORIZED}
          </p>
          <Button onClick={() => router.push('/?auth=signin')}>Sign In</Button>
        </div>
      </div>
    )
  }

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        form.handleSubmit()
      }}
      className={cn(
        'bg-background mx-auto w-full max-w-lg space-y-6 rounded-lg p-6 shadow-md',
      )}
      aria-label="Video Submission Form"
    >
      <h2 className="mb-6 text-center text-2xl font-semibold">
        Submit Performance
      </h2>

      {/* Exercise Selection */}
      {exerciseIdParam ? (
        <div>
          <Label htmlFor="exerciseFixedId">Exercise</Label>
          <Input
            id="exerciseFixedId"
            value={exerciseTitleParam ?? exerciseIdParam}
            readOnly
            disabled
            className="bg-muted"
          />
        </div>
      ) : (
        <form.Field
          name="exerciseId"
          validators={{
            onChange: ({ value }) => validateField(value, 'exerciseId'),
          }}
        >
          {(field) => (
            <ExerciseSelector
              value={field.state.value}
              onChange={field.handleChange}
              disabled={isSubmittingToServer}
              error={field.state.meta.errors?.[0]}
            />
          )}
        </form.Field>
      )}

      {/* Video URL Field */}
      <form.Field
        name="videoUrl"
        validators={{
          onChange: ({ value }) => validateField(value, 'videoUrl'),
        }}
      >
        {(field) => (
          <div>
            <Label htmlFor={field.name}>Video URL</Label>
            <Input
              id={field.name}
              type="url"
              placeholder="https://example.com/video"
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(evt) => field.handleChange(evt.target.value)}
              required
              aria-invalid={!!field.state.meta.errors?.[0]}
              className={
                field.state.meta.errors?.[0] ? 'border-red-500' : undefined
              }
            />
            {field.state.meta.errors?.[0] && (
              <p className="text-destructive mt-1 text-sm">
                {field.state.meta.errors[0]}
              </p>
            )}
          </div>
        )}
      </form.Field>

      {/* Weight Lifted Field */}
      <form.Field
        name="weightLifted"
        validators={{
          onChange: ({ value }) => validateField(value, 'weightLifted'),
        }}
      >
        {(field) => (
          <div>
            <Label htmlFor={field.name}>Weight Lifted (kg)</Label>
            <Input
              id={field.name}
              type="number"
              min="0"
              step="0.01"
              placeholder="e.g. 100"
              value={field.state.value == null ? '' : String(field.state.value)}
              onBlur={field.handleBlur}
              onChange={(evt) =>
                field.handleChange(
                  evt.target.value === ''
                    ? Number.NaN
                    : Number.parseFloat(evt.target.value),
                )
              }
              required
              aria-invalid={!!field.state.meta.errors?.[0]}
              className={
                field.state.meta.errors?.[0] ? 'border-red-500' : undefined
              }
            />
            {field.state.meta.errors?.[0] && (
              <p className="text-destructive mt-1 text-sm">
                {field.state.meta.errors[0]}
              </p>
            )}
          </div>
        )}
      </form.Field>

      {/* Notes Field */}
      <form.Field
        name="notes"
        validators={{
          onChange: ({ value }) => validateField(value, 'notes'),
        }}
      >
        {(field) => (
          <div>
            <Label htmlFor={field.name}>Notes (optional)</Label>
            <Textarea
              id={field.name}
              placeholder="Add any notes about your performance (optional)"
              value={field.state.value ?? ''}
              onBlur={field.handleBlur}
              onChange={(evt) => field.handleChange(evt.target.value)}
              rows={4}
              aria-invalid={!!field.state.meta.errors?.[0]}
              className={
                field.state.meta.errors?.[0] ? 'border-red-500' : undefined
              }
            />
            {field.state.meta.errors?.[0] && (
              <p className="text-destructive mt-1 text-sm">
                {field.state.meta.errors[0]}
              </p>
            )}
          </div>
        )}
      </form.Field>

      {/* Server Errors */}
      {serverError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 dark:bg-red-950/10 dark:border-red-800">
          <p className="text-red-700 dark:text-red-300 text-sm">
            {serverError}
          </p>
        </div>
      )}

      {serverFieldErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 dark:bg-red-950/10 dark:border-red-800">
          <p className="text-red-700 dark:text-red-300 text-sm font-medium mb-2">
            Please correct the following issues:
          </p>
          <ul className="text-red-700 dark:text-red-300 text-sm space-y-1">
            {serverFieldErrors.map((err) => (
              <li key={`${err.path.join('.')}-${err.message}`}>
                {err.path.join('.')}: {err.message}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={form.state.isSubmitting || isSubmittingToServer}
        className="w-full"
      >
        {form.state.isSubmitting || isSubmittingToServer
          ? 'Submitting...'
          : 'Submit Performance'}
      </Button>
    </form>
  )
}

/**
 * Main SubmissionForm component wrapped with error boundary
 * Provides error handling and fallback UI for the submission form
 */
export const SubmissionForm: React.FC = () => {
  return (
    <SubmissionErrorBoundary>
      <SubmissionFormContent />
    </SubmissionErrorBoundary>
  )
}
