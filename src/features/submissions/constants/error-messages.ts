/**
 * Standardized error messages for the submission feature
 * Provides consistent tone and format across all error scenarios
 */
export const SUBMISSION_ERRORS = {
  // Authentication errors
  UNAUTHORIZED: 'You must be signed in to submit a performance.',
  INSUFFICIENT_PERMISSIONS:
    'You do not have permission to perform this action.',

  // Rate limiting errors
  RATE_LIMITED: 'Please wait 24 hours before submitting another performance.',

  // Validation errors
  INVALID_VIDEO_URL: 'Please provide a valid video URL (https://...).',
  INVALID_WEIGHT: 'Weight must be a positive number.',
  INVALID_EXERCISE: 'Please select a valid exercise.',
  NOTES_TOO_LONG: 'Notes must be 500 characters or less.',

  // Submission errors
  SUBMISSION_FAILED: 'Failed to submit your performance. Please try again.',
  VIDEO_URL_INACCESSIBLE:
    'The video URL appears to be inaccessible. Please check the link.',

  // Evaluation errors
  SUBMISSION_NOT_FOUND: 'The requested submission could not be found.',
  ALREADY_EVALUATED: 'This submission has already been evaluated.',
  EVALUATION_FAILED: 'Failed to evaluate the submission. Please try again.',

  // Data fetching errors
  EXERCISES_FETCH_FAILED: 'Failed to load exercises. Please refresh the page.',
  PROFILE_FETCH_FAILED: 'Failed to load user profile. Please try again.',

  // Generic errors
  UNEXPECTED_ERROR: 'An unexpected error occurred. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
} as const

/**
 * Success messages for positive feedback
 */
export const SUBMISSION_SUCCESS = {
  SUBMITTED:
    'Performance submitted successfully! It will be reviewed by our grandmasters.',
  APPROVED: 'Submission approved successfully.',
  REJECTED: 'Submission rejected successfully.',
  UPDATED: 'Submission updated successfully.',
} as const

/**
 * Get user-friendly error message for common error types
 * @param error - The error object or message
 * @returns A user-friendly error message
 */
export const getErrorMessage = (error: unknown): string => {
  if (typeof error === 'string') {
    return error
  }

  if (error instanceof Error) {
    // Map specific error messages to user-friendly ones
    const message = error.message.toLowerCase()

    if (
      message.includes('unauthorized') ||
      message.includes('authentication')
    ) {
      return SUBMISSION_ERRORS.UNAUTHORIZED
    }

    if (message.includes('rate limit') || message.includes('24 hours')) {
      return SUBMISSION_ERRORS.RATE_LIMITED
    }

    if (message.includes('video url') || message.includes('invalid url')) {
      return SUBMISSION_ERRORS.INVALID_VIDEO_URL
    }

    if (message.includes('not found')) {
      return SUBMISSION_ERRORS.SUBMISSION_NOT_FOUND
    }

    return error.message
  }

  return SUBMISSION_ERRORS.UNEXPECTED_ERROR
}
