import { z } from 'zod/v4'

/**
 * Zod schema for video submission form validation.
 * - exerciseId: required, must be a valid UUID.
 * - videoUrl: required, must be a valid URL.
 * - weightLifted: required, must be a positive number.
 * - notes: optional, max 500 chars.
 */
export const submissionSchema = z.object({
  exerciseId: z.string().uuid({ message: 'Exercise is required.' }),
  videoUrl: z
    .string()
    .url({ message: 'Please enter a valid video URL (https://...).' }),
  weightLifted: z.preprocess(
    (val) => (typeof val === 'string' ? Number(val) : val),
    z
      .number({ message: 'Weight is required.' })
      .positive('Weight must be a positive number.'),
  ),
  notes: z
    .string()
    .max(500, 'Notes must be 500 characters or less.')
    .optional()
    .or(z.literal('')),
})

export type SubmissionFormData = z.infer<typeof submissionSchema>
