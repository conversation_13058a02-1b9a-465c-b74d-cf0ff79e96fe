import { fireEvent, render, screen } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { GoToSubmitButton } from './GoToSubmitButton'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock Supabase client
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(() => ({
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      })),
    },
  })),
}))

// Mock Supabase credentials
vi.mock('@/libs/supabase/credentials', () => ({
  getSupabaseCredentials: vi.fn(() => ({
    supabaseUrl: 'https://example.com',
    supabaseAnonKey: 'test-key',
  })),
}))

describe('GoToSubmitButton Component', () => {
  const mockRouter = {
    push: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as ReturnType<typeof vi.fn>).mockReturnValue(mockRouter)
  })

  it('renders login button when user is not authenticated', async () => {
    // Mock unauthenticated session
    const mockSupabase = require('@supabase/ssr').createBrowserClient()
    mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null } })

    render(<GoToSubmitButton exerciseId="123" exerciseTitle="Test Exercise" />)

    // Wait for auth check to complete
    await vi.waitFor(() => {
      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
    })

    // Button should show login text
    expect(screen.getByText('Login to Submit')).toBeInTheDocument()
  })

  it('renders submit button when user is authenticated', async () => {
    // Mock authenticated session
    const mockSupabase = require('@supabase/ssr').createBrowserClient()
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'user-123' } } },
    })

    render(<GoToSubmitButton exerciseId="123" exerciseTitle="Test Exercise" />)

    // Wait for auth check to complete
    await vi.waitFor(() => {
      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
    })

    // Button should show submit text
    expect(screen.getByText('Submit Performance')).toBeInTheDocument()
  })

  it('navigates to submit page when authenticated user clicks', async () => {
    // Mock authenticated session
    const mockSupabase = require('@supabase/ssr').createBrowserClient()
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'user-123' } } },
    })

    render(<GoToSubmitButton exerciseId="123" exerciseTitle="Test Exercise" />)

    // Wait for auth check to complete
    await vi.waitFor(() => {
      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
    })

    // Click the button
    fireEvent.click(screen.getByText('Submit Performance'))

    // Check if router.push was called with correct URL
    expect(mockRouter.push).toHaveBeenCalledWith(
      '/submit?exerciseId=123&title=Test%20Exercise',
    )
  })

  it('navigates to login page when unauthenticated user clicks', async () => {
    // Mock unauthenticated session
    const mockSupabase = require('@supabase/ssr').createBrowserClient()
    mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null } })

    render(<GoToSubmitButton exerciseId="123" exerciseTitle="Test Exercise" />)

    // Wait for auth check to complete
    await vi.waitFor(() => {
      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
    })

    // Click the button
    fireEvent.click(screen.getByText('Login to Submit'))

    // Check if router.push was called with login URL
    expect(mockRouter.push).toHaveBeenCalledWith('/login')
  })
})
