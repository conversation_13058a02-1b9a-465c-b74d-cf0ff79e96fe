import { Badge } from '@/components/ui/badge'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { SubmissionWithRelations } from '@/types/submission'

interface SubmissionDetailsCardProps {
  submission: SubmissionWithRelations
}

/**
 * Displays submission details including athlete info, exercise, weight, and status
 * Handles null values safely and provides proper formatting
 */
export function SubmissionDetailsCard({
  submission,
}: SubmissionDetailsCardProps) {
  const formatMedalName = (medal: string | null): string => {
    if (!medal || medal === 'none') {
      return 'None'
    }
    return medal.charAt(0).toUpperCase() + medal.slice(1)
  }

  const getStatusBadgeClassName = (status: string): string => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
      default: // pending
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-200'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Submission Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium">Exercise</h3>
          <p>{submission.exercises?.title || 'Unknown Exercise'}</p>
        </div>

        <div>
          <h3 className="font-medium">Athlete</h3>
          <p>
            {submission.profiles?.username || 'Unknown'}
            {submission.profiles?.full_name && (
              <span className="text-sm text-gray-600 ml-1">
                ({submission.profiles.full_name})
              </span>
            )}
          </p>
        </div>

        <div>
          <h3 className="font-medium">Weight Lifted</h3>
          <p>{submission.weight_lifted} kg</p>
        </div>

        <div>
          <h3 className="font-medium">Status</h3>
          <Badge className={getStatusBadgeClassName(submission.status)}>
            {submission.status.charAt(0).toUpperCase() +
              submission.status.slice(1)}
          </Badge>
        </div>

        <div>
          <h3 className="font-medium">Submitted</h3>
          <p>{new Date(submission.submitted_at).toLocaleString()}</p>
        </div>

        {submission.notes && (
          <div>
            <h3 className="font-medium">Athlete Notes</h3>
            <p className="text-sm whitespace-pre-wrap">{submission.notes}</p>
          </div>
        )}

        {submission.status === 'approved' && (
          <div>
            <h3 className="font-medium">Medal Awarded</h3>
            <p>{formatMedalName(submission.medal_awarded)}</p>
          </div>
        )}

        {submission.status === 'approved' &&
          submission.points_awarded !== null &&
          submission.points_awarded > 0 && (
            <div>
              <h3 className="font-medium">Points Awarded</h3>
              <p>{submission.points_awarded}</p>
            </div>
          )}

        {submission.status === 'rejected' && submission.private_comments && (
          <div>
            <h3 className="font-medium">Feedback</h3>
            <p className="text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap">
              {submission.private_comments}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
