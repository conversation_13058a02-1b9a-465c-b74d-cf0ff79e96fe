import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { VideoPlayer } from '@/shared/ui/VideoPlayer'

interface SubmissionVideoProps {
  videoUrl: string
}

export function SubmissionVideo({ videoUrl }: SubmissionVideoProps) {
  return (
    <div className="mb-6">
      <Card>
        <CardHeader>
          <CardTitle>Performance Video</CardTitle>
        </CardHeader>
        <CardContent>
          <VideoPlayer url={videoUrl} />
        </CardContent>
      </Card>
    </div>
  )
}
