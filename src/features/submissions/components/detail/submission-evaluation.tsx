import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { EvaluationForm } from '@/features/submissions/EvaluationForm' // Adjusted path
import type { SubmissionWithRelations } from '@/types/submission'

interface SubmissionEvaluationProps {
  submission: SubmissionWithRelations
}

export function SubmissionEvaluation({
  submission,
}: SubmissionEvaluationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Evaluation</CardTitle>
      </CardHeader>
      <CardContent>
        {submission.status === 'pending' ? (
          <EvaluationForm
            submissionId={submission.id.toString()}
            exerciseId={submission.exercise_id.toString()} // Ensure exercise_id is string
            userId={submission.user_id}
            gender={submission.profiles?.gender || 'male'}
            weightCategory={submission.profiles?.weight_category || 'under_95'}
            weightLifted={Number.parseFloat(submission.weight_lifted)}
            medalThresholds={
              typeof submission.exercises?.medal_thresholds === 'object' &&
              !Array.isArray(submission.exercises?.medal_thresholds) &&
              submission.exercises?.medal_thresholds !== null
                ? (submission.exercises.medal_thresholds as Record<
                    string,
                    Record<string, number>
                  >)
                : {}
            }
            initialFeedback={submission.private_comments || ''}
          />
        ) : (
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">Feedback</h3>
              <p className="mt-1 text-sm">
                {submission.private_comments || 'No feedback provided.'}
              </p>
            </div>

            <div>
              <h3 className="font-medium">Evaluated By</h3>
              <p className="mt-1 text-sm">
                {submission.evaluated_by ? 'Administrator' : 'System'}
              </p>
            </div>

            <div>
              <h3 className="font-medium">Evaluation Date</h3>
              <p className="mt-1 text-sm">
                {submission.evaluated_at
                  ? new Date(submission.evaluated_at).toLocaleString()
                  : 'Not yet evaluated'}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
