'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Component, type ErrorInfo, type ReactNode } from 'react'
import { getErrorMessage } from '../constants/error-messages'

interface SubmissionErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

/**
 * Error fallback component for submission-related errors
 * Displays user-friendly error message with retry option
 */
function SubmissionErrorFallback({
  error,
  resetErrorBoundary,
}: SubmissionErrorFallbackProps) {
  const userFriendlyMessage = getErrorMessage(error)

  return (
    <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertTriangle className="h-5 w-5" />
          Something went wrong
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-red-700 dark:text-red-300">
          {userFriendlyMessage}
        </p>

        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4">
            <summary className="cursor-pointer text-xs text-red-600 dark:text-red-400">
              Technical details (development only)
            </summary>
            <pre className="mt-2 whitespace-pre-wrap text-xs text-red-700 dark:text-red-300">
              {error.message}
              {error.stack && `\n\nStack trace:\n${error.stack}`}
            </pre>
          </details>
        )}

        <div className="flex gap-2">
          <Button
            onClick={resetErrorBoundary}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try again
          </Button>

          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/20"
          >
            Reload page
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

interface SubmissionErrorBoundaryProps {
  children: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

/**
 * Error boundary wrapper for submission-related components
 * Catches errors and displays user-friendly fallback UI
 * Custom implementation since react-error-boundary may not be available
 */
export class SubmissionErrorBoundary extends Component<
  SubmissionErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: SubmissionErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error for debugging
    console.error('Submission Error Boundary caught an error:', {
      error,
      errorInfo,
      timestamp: new Date().toISOString(),
    })

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo })
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      return (
        <SubmissionErrorFallback
          error={this.state.error}
          resetErrorBoundary={this.handleReset}
        />
      )
    }

    return this.props.children
  }
}
