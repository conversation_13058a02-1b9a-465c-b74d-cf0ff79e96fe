'use client'

import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAuth } from '@/features/auth/hooks/use-auth'
import { useEffect, useState } from 'react'
import { SUBMISSION_ERRORS } from '../constants/error-messages'

interface Exercise {
  id: string
  title: string
}

interface ExerciseSelectorProps {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  error?: string
}

/**
 * ExerciseSelector component that fetches exercises from API route
 * Replaces direct database access for security compliance
 */
export function ExerciseSelector({
  value,
  onChange,
  disabled = false,
  error,
}: ExerciseSelectorProps) {
  const { user } = useAuth()
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [loading, setLoading] = useState(false)
  const [fetchError, setFetchError] = useState<string | null>(null)

  useEffect(() => {
    if (!user) return

    const fetchExercises = async () => {
      setLoading(true)
      setFetchError(null)

      try {
        const response = await fetch('/api/exercises')

        if (!response.ok) {
          if (response.status === 401) {
            setFetchError(SUBMISSION_ERRORS.UNAUTHORIZED)
            return
          }
          throw new Error(`HTTP ${response.status}`)
        }

        const data = await response.json()

        if (data.error) {
          setFetchError(data.error)
          return
        }

        setExercises(data.exercises || [])
      } catch (err) {
        console.error('Failed to fetch exercises:', err)
        setFetchError(SUBMISSION_ERRORS.EXERCISES_FETCH_FAILED)
      } finally {
        setLoading(false)
      }
    }

    fetchExercises()
  }, [user])

  if (!user) {
    return (
      <div>
        <Label>Exercise</Label>
        <div className="text-sm text-gray-500 mt-1">
          Please sign in to select an exercise.
        </div>
      </div>
    )
  }

  return (
    <div>
      <Label htmlFor="exercise-select">Exercise</Label>
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled || loading}
        required
      >
        <SelectTrigger
          id="exercise-select"
          aria-label="Select exercise"
          className={error ? 'border-red-500' : undefined}
        >
          <SelectValue
            placeholder={
              loading
                ? 'Loading exercises...'
                : fetchError
                  ? 'Error loading exercises'
                  : 'Select an exercise'
            }
          />
        </SelectTrigger>
        <SelectContent>
          {exercises.map((exercise) => (
            <SelectItem key={exercise.id} value={exercise.id}>
              {exercise.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {(fetchError || error) && (
        <p className="text-destructive mt-1 text-sm">{fetchError || error}</p>
      )}

      {!loading && !fetchError && exercises.length === 0 && (
        <p className="text-sm text-gray-500 mt-1">No exercises available.</p>
      )}
    </div>
  )
}
