'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { AuthDialog } from '@/features/auth/components'
import Link from 'next/link'
import { useState } from 'react'

export function UnauthenticatedSubmissionsView() {
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const [authDialogMode, setAuthDialogMode] = useState<'signin' | 'signup'>(
    'signin',
  )

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthDialogMode(mode)
    setAuthDialogOpen(true)
  }

  return (
    <>
      <div className="container mx-auto max-w-5xl px-4 py-12">
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-4xl font-bold">Performance Submissions</h1>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-400">
            Submit, get grandmaster approval, earn medals & points, and rise in
            global rankings.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <Card className="overflow-hidden">
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-10">
              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full">
                  1
                </div>
                <div>
                  <h3 className="font-medium">Record Your Performance</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Record yourself performing an exercise from our catalog.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full">
                  2
                </div>
                <div>
                  <h3 className="font-medium">Submit for Evaluation</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Upload your video and provide details about your
                    performance.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full">
                  3
                </div>
                <div>
                  <h3 className="font-medium">Get Evaluated</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Grandmasters will review your submission and provide
                    feedback.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full">
                  4
                </div>
                <div>
                  <h3 className="font-medium">Earn Medals & Points</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Successful submissions earn medals and points for rankings.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ready to Submit?</CardTitle>
              <CardDescription>
                Join our community of athletes and showcase your strength.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>To submit your performances, you'll need to:</p>
              <ul className="ml-6 list-disc space-y-2">
                <li>Create an account or sign in</li>
                <li>Complete your athlete profile</li>
                <li>Browse our exercise catalog</li>
                <li>Record your performance</li>
                <li>Submit for evaluation</li>
              </ul>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Our grandmasters will evaluate your technique, form, and
                strength to award medals and points.
              </p>
            </CardContent>
            <CardFooter className="flex flex-col space-y-3">
              <Button
                className="w-full"
                onClick={() => handleAuthClick('signin')}
              >
                Sign In
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleAuthClick('signup')}
              >
                Create Account
              </Button>
              <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                Sign in or create an account to access your submissions.
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
        initialMode={authDialogMode}
      />
    </>
  )
}
