import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import Link from 'next/link'

export function AthleteSubmissionsView() {
  return (
    <div className="container mx-auto max-w-5xl px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Your Submissions</h1>
        <Button asChild>
          <Link href="/submit">Submit New Performance</Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Your Submissions</CardTitle>
          <CardDescription>
            View and track all your submitted performances.
          </CardDescription>
        </CardHeader>
        <CardContent className="py-8 text-center">
          <p className="mb-4">
            You can view all your submissions in your account section.
          </p>
          <Button asChild>
            <Link href="/account/submissions">Go to My Submissions</Link>
          </Button>
        </CardContent>
      </Card>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Ready to Submit a New Performance?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Browse our exercise catalog to find an exercise to perform and
              submit.
            </p>
            <div className="flex justify-center">
              <Button asChild>
                <Link href="/exercises">Browse Exercises</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
