import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { SubmissionWithRelations } from '@/types/submission' // Assuming this type includes profiles and exercises
import Link from 'next/link'

interface AdminSubmissionsManagementViewProps {
  submissions: SubmissionWithRelations[] | null
}

export function AdminSubmissionsManagementView({
  submissions,
}: AdminSubmissionsManagementViewProps) {
  return (
    <div className="container mx-auto max-w-7xl px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Submissions Management</h1>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/exercises">View Exercises</Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          {submissions && submissions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-2 text-left">Athlete</th>
                    <th className="px-4 py-2 text-left">Exercise</th>
                    <th className="px-4 py-2 text-left">Weight</th>
                    <th className="px-4 py-2 text-left">Status</th>
                    <th className="px-4 py-2 text-left">Submitted</th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {submissions.map((submission) => (
                    <tr
                      key={submission.id}
                      className="hover:bg-muted/50 border-b"
                    >
                      <td className="px-4 py-2">
                        {submission.profiles?.username || 'Unknown'}
                      </td>
                      <td className="px-4 py-2">
                        {submission.exercises?.title || 'Unknown Exercise'}
                      </td>
                      <td className="px-4 py-2">
                        {submission.weight_lifted} kg
                      </td>
                      <td className="px-4 py-2">
                        <span
                          className={`inline-block rounded px-2 py-1 text-xs font-medium ${
                            submission.status === 'approved'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : submission.status === 'rejected'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          }`}
                        >
                          {submission.status.charAt(0).toUpperCase() +
                            submission.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        {new Date(submission.submitted_at).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-2">
                        <Button asChild size="sm" variant="outline">
                          <Link href={`/submissions/${submission.id}`}>
                            Review
                          </Link>
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="py-8 text-center text-gray-500">
              No submissions found.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
