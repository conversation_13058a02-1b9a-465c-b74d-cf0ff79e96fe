import { Label } from '@/components/ui/label'
import { useCallback, useEffect, useRef } from 'react'
import { MEDALS, type MedalType } from '../constants/medals'
import { MedalButton } from './MedalButton'

interface MedalSelectorProps {
  selected: MedalType | null
  onSelect: (medal: MedalType) => void
  disabled?: boolean
}

/**
 * MedalSelector component with full accessibility support
 * Supports keyboard navigation, screen readers, and focus management
 */
export function MedalSelector({
  selected,
  onSelect,
  disabled = false,
}: MedalSelectorProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const selectedIndex = selected ? MEDALS.indexOf(selected) : -1

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (disabled) return

      const { key } = event
      let newIndex = selectedIndex

      switch (key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault()
          newIndex = selectedIndex > 0 ? selectedIndex - 1 : MEDALS.length - 1
          break
        case 'ArrowRight':
        case 'ArrowDown':
          event.preventDefault()
          newIndex = selectedIndex < MEDALS.length - 1 ? selectedIndex + 1 : 0
          break
        case 'Home':
          event.preventDefault()
          newIndex = 0
          break
        case 'End':
          event.preventDefault()
          newIndex = MEDALS.length - 1
          break
        case 'Enter':
        case ' ':
          event.preventDefault()
          if (selectedIndex >= 0) {
            onSelect(MEDALS[selectedIndex])
          }
          return
        default:
          return
      }

      const newMedal = MEDALS[newIndex]
      if (newMedal) {
        onSelect(newMedal)

        // Focus the newly selected medal button
        setTimeout(() => {
          const buttonElement = containerRef.current?.querySelector(
            `[data-medal="${newMedal}"]`,
          ) as HTMLButtonElement
          buttonElement?.focus()
        }, 0)
      }
    },
    [selectedIndex, onSelect, disabled],
  )

  // Focus the selected medal when component mounts or selection changes
  useEffect(() => {
    if (selected && containerRef.current) {
      const buttonElement = containerRef.current.querySelector(
        `[data-medal="${selected}"]`,
      ) as HTMLButtonElement

      if (buttonElement && document.activeElement !== buttonElement) {
        buttonElement.focus()
      }
    }
  }, [selected])

  const getAriaLabel = () => {
    if (disabled) {
      return 'Medal selection disabled'
    }

    if (selected) {
      return `Medal selection. Currently selected: ${selected}. Use arrow keys to navigate, Enter or Space to select.`
    }

    return 'Medal selection. Use arrow keys to navigate, Enter or Space to select.'
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="mb-1 flex items-center gap-2">
        <Label>Select Medal</Label>
      </div>

      <div
        ref={containerRef}
        className="grid gap-2"
        style={{
          gridTemplateColumns: 'repeat(auto-fit, minmax(88px, 1fr))',
        }}
        role="radiogroup"
        aria-label={getAriaLabel()}
        aria-describedby={disabled ? 'medal-disabled-help' : undefined}
        aria-disabled={disabled}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
      >
        {MEDALS.map((medal, index) => (
          <MedalButton
            key={medal}
            medal={medal}
            selected={selected === medal}
            disabled={disabled}
            onClick={onSelect}
            data-medal={medal}
            aria-checked={selected === medal}
            aria-posinset={index + 1}
            aria-setsize={MEDALS.length}
            tabIndex={
              disabled
                ? -1
                : selected === medal || (selected === null && index === 0)
                  ? 0
                  : -1
            }
          />
        ))}
      </div>
    </div>
  )
}
