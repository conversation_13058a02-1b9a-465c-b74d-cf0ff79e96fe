'use server'

import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import { z } from 'zod/v4'
import {
  SUBMISSION_ERRORS,
  SUBMISSION_SUCCESS,
  getErrorMessage,
} from './constants/error-messages'

// Define the schema for the evaluation form with strict validation
const evaluationSchema = z.object({
  submissionId: z.string().min(1, 'Submission ID is required'),
  feedback: z
    .string()
    .max(500, 'Feedback must be 500 characters or less.')
    .optional()
    .or(z.literal('')),
  decision: z.enum(['approve', 'reject'], {
    message: 'Decision must be either approve or reject',
  }),
  medalAwarded: z
    .enum(['none', 'bronze', 'silver', 'gold', 'platinum', 'diamond'])
    .optional(),
  pointsAwarded: z
    .number()
    .int()
    .nonnegative()
    .max(100, 'Points cannot exceed 100')
    .optional(),
})

// Define the shape of the state object returned by the action
export interface EvaluationActionState {
  success?: boolean
  message?: string
  error?: string
  validationErrors?: z.ZodIssue[]
}

/**
 * Server action to evaluate a submission
 * Validates all input data, checks permissions, and updates submission status
 * @param formData - The evaluation form data (treated as unknown for security)
 * @returns A state object with the result of the evaluation
 */
export async function evaluateSubmission(
  formData: unknown,
): Promise<EvaluationActionState> {
  try {
    const cookieStore = await cookies()
    const supabase = getSupabaseRouteHandlerClient(cookieStore)

    // 1. Authentication Check
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return { error: SUBMISSION_ERRORS.UNAUTHORIZED }
    }

    // 2. Role Check (only admins and grandmasters can evaluate)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      console.error('Profile fetch error:', profileError)
      return { error: SUBMISSION_ERRORS.PROFILE_FETCH_FAILED }
    }

    if (profile.role !== 'admin' && profile.role !== 'grandmaster') {
      return { error: SUBMISSION_ERRORS.INSUFFICIENT_PERMISSIONS }
    }

    // 3. Validate input data (treat as unknown for security)
    const validationResult = evaluationSchema.safeParse(formData)
    if (!validationResult.success) {
      console.error(
        'Evaluation validation errors:',
        z.treeifyError(validationResult.error),
      )
      return {
        error: 'Invalid evaluation data. Please check your inputs.',
        validationErrors: validationResult.error.issues,
      }
    }

    const validatedData = validationResult.data

    // 4. Check if submission exists and is pending
    const { data: submission, error: submissionError } = await supabase
      .from('submissions')
      .select('id, status, user_id')
      .eq('id', validatedData.submissionId)
      .single()

    if (submissionError || !submission) {
      console.error('Submission fetch error:', submissionError)
      return { error: SUBMISSION_ERRORS.SUBMISSION_NOT_FOUND }
    }

    if (submission.status !== 'pending') {
      return { error: SUBMISSION_ERRORS.ALREADY_EVALUATED }
    }

    // 5. Validate business logic for approval
    if (validatedData.decision === 'approve') {
      if (
        !validatedData.medalAwarded ||
        validatedData.medalAwarded === 'none'
      ) {
        return {
          error: 'A medal must be selected for an approved submission.',
          validationErrors: [
            {
              code: 'custom',
              message: 'Medal is required for approval',
              path: ['medalAwarded'],
              input: validatedData.medalAwarded,
            },
          ],
        }
      }

      if (!validatedData.pointsAwarded || validatedData.pointsAwarded <= 0) {
        return {
          error: 'Points must be awarded for an approved submission.',
          validationErrors: [
            {
              code: 'custom',
              message: 'Points are required for approval',
              path: ['pointsAwarded'],
              input: validatedData.pointsAwarded,
            },
          ],
        }
      }
    }

    // 6. Prepare update data
    const status =
      validatedData.decision === 'approve' ? 'approved' : 'rejected'
    const medalAwarded =
      validatedData.decision === 'approve' ? validatedData.medalAwarded : 'none'
    const pointsAwarded =
      validatedData.decision === 'approve' ? validatedData.pointsAwarded : 0

    // 7. Update submission in a transaction-like approach
    const { error: updateError } = await supabase
      .from('submissions')
      .update({
        status,
        medal_awarded: medalAwarded,
        points_awarded: pointsAwarded,
        private_comments: validatedData.feedback || null,
        evaluated_at: new Date().toISOString(),
        evaluated_by: user.id,
      })
      .eq('id', validatedData.submissionId)
      .eq('status', 'pending') // Additional safety check

    if (updateError) {
      console.error('Error updating submission:', updateError)
      return { error: SUBMISSION_ERRORS.EVALUATION_FAILED }
    }

    // 8. If approved, update user's total points
    if (status === 'approved' && pointsAwarded && pointsAwarded > 0) {
      const { error: pointsUpdateError } = await supabase.rpc(
        'increment_user_points',
        {
          user_id_param: submission.user_id,
          points_param: pointsAwarded,
        },
      )

      if (pointsUpdateError) {
        console.error('Error updating user points:', pointsUpdateError)
        // Don't fail the entire operation, but log the issue
        // In a real system, you might want to queue this for retry
        return {
          success: true,
          message:
            'Submission evaluated, but failed to update user points. Please contact an administrator.',
        }
      }
    }

    // 9. Revalidate relevant paths
    revalidatePath('/submissions')
    revalidatePath(`/submissions/${validatedData.submissionId}`)
    revalidatePath('/account')
    revalidatePath('/rankings')

    // 10. Return success
    const successMessage =
      status === 'approved'
        ? SUBMISSION_SUCCESS.APPROVED
        : SUBMISSION_SUCCESS.REJECTED

    return {
      success: true,
      message: successMessage,
    }
  } catch (error) {
    console.error('Unexpected error in evaluateSubmission:', error)
    return {
      error: getErrorMessage(error),
    }
  }
}
