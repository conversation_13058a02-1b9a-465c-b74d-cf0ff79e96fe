import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { formatDistanceToNow } from 'date-fns'
import Link from 'next/link'

/**
 * Type definition for a submission with exercise title
 */
export type SubmissionWithExercise = {
  id: number
  user_id: string
  exercise_id: number
  exercise_title: string // Joined from exercises table
  video_url: string
  weight_lifted: number
  notes: string | null
  status: 'pending' | 'approved' | 'rejected'
  submitted_at: string
  evaluated_at: string | null
  evaluated_by: string | null
  points_awarded: number
  medal_awarded: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'
  private_comments: string | null
}

interface SubmissionHistoryListProps {
  submissions: SubmissionWithExercise[]
  limit?: number
}

/**
 * SubmissionHistoryList - Displays a user's submission history
 * Shows exercise title, date, status, weight, medals, points, video link, and feedback
 * Optional limit parameter to show only a certain number of submissions
 */
export default function SubmissionHistoryList({
  submissions,
  limit,
}: SubmissionHistoryListProps) {
  if (!submissions || submissions.length === 0) {
    return (
      <div className="py-8 text-center text-gray-500">
        <p className="mb-4">You haven't submitted any performances yet.</p>
        <Button asChild>
          <Link href="/submit">Submit Your First Performance</Link>
        </Button>
      </div>
    )
  }

  // Helper function to get badge variant based on status
  const getStatusVariant = (
    status: 'pending' | 'approved' | 'rejected',
  ): 'default' | 'secondary' | 'destructive' => {
    switch (status) {
      case 'approved':
        return 'default'
      case 'rejected':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  // Helper function to format date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return 'Unknown date'
    }
  }

  // Helper function to get medal badge variant
  const getMedalVariant = (
    medal: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond',
  ): 'default' | 'secondary' | 'outline' => {
    if (medal === 'none') return 'outline'
    return 'default'
  }

  // Apply limit if provided
  const displaySubmissions = limit ? submissions.slice(0, limit) : submissions
  const hasMoreSubmissions = limit && submissions.length > limit

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Exercise</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Weight</TableHead>
            <TableHead>Medal</TableHead>
            <TableHead>Points</TableHead>
            <TableHead>Video</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {displaySubmissions.map((submission) => (
            <TableRow key={submission.id}>
              <TableCell className="font-medium">
                {submission.exercise_title}
              </TableCell>
              <TableCell>{formatDate(submission.submitted_at)}</TableCell>
              <TableCell>
                <Badge variant={getStatusVariant(submission.status)}>
                  {submission.status.charAt(0).toUpperCase() +
                    submission.status.slice(1)}
                </Badge>
              </TableCell>
              <TableCell>{submission.weight_lifted} kg</TableCell>
              <TableCell>
                {submission.medal_awarded !== 'none' ? (
                  <Badge variant={getMedalVariant(submission.medal_awarded)}>
                    {submission.medal_awarded.charAt(0).toUpperCase() +
                      submission.medal_awarded.slice(1)}
                  </Badge>
                ) : (
                  <span className="text-gray-400">—</span>
                )}
              </TableCell>
              <TableCell>
                {submission.points_awarded > 0 ? (
                  <span className="font-medium text-green-600 dark:text-green-400">
                    +{submission.points_awarded}
                  </span>
                ) : (
                  <span className="text-gray-400">—</span>
                )}
              </TableCell>
              <TableCell>
                <Button asChild size="sm" variant="outline">
                  <a
                    href={submission.video_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Watch
                  </a>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Show "View All" button if there are more submissions */}
      {hasMoreSubmissions && (
        <div className="mt-4 flex justify-center">
          <Button asChild variant="outline" size="sm">
            <Link href="/account/submissions">View All Submissions</Link>
          </Button>
        </div>
      )}

      {/* Feedback section - only show for submissions with feedback */}
      <div className="mt-6 space-y-4">
        {displaySubmissions
          .filter(
            (s) =>
              s.private_comments &&
              (s.status === 'approved' || s.status === 'rejected'),
          )
          .map((submission) => (
            <div
              key={`feedback-${submission.id}`}
              className="rounded-lg border p-4"
            >
              <div className="mb-2 flex items-center justify-between">
                <h4 className="font-medium">
                  Feedback for {submission.exercise_title}
                </h4>
                <Badge variant={getStatusVariant(submission.status)}>
                  {submission.status}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {submission.private_comments}
              </p>
              <div className="mt-2 text-xs text-gray-500">
                {submission.evaluated_at &&
                  `Evaluated ${formatDate(submission.evaluated_at)}`}
              </div>
            </div>
          ))}
      </div>
    </div>
  )
}
