'use server'

import { profileRateLimit } from '@/libs/rate-limit'
import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import type { z } from 'zod/v4'
import { EditProfileSchema } from './profileSchema'

// Action response type for consistent error handling
interface ActionResponse {
  success?: boolean;
  error?: string;
  data?: { avatarUrl?: string };
}

/**
 * Server action to update the user's profile.
 * - Validates input
 * - Checks username uniqueness if changed
 * - Updates allowed fields
 * - Revalidates relevant paths
 * - Returns { error?: string }
 */
export async function updateProfile(form: z.infer<typeof EditProfileSchema>) {
  // SECURITY: Apply rate limiting to prevent profile update abuse
  const rateLimitResult = await profileRateLimit()
  if (!rateLimitResult.success) {
    return {
      error: rateLimitResult.error || 'Too many profile updates. Please try again later.',
    }
  }
  // Validate input
  const parsed = EditProfileSchema.safeParse(form)
  if (!parsed.success) {
    return {
      error: parsed.error.issues
        .map((e: { message: string }) => e.message)
        .join(', '),
    }
  }
  const values = parsed.data

  // Get authenticated user
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Not authenticated' }
  }

  // Fetch current profile
  const { data: currentProfile, error: profileError } = await supabase
    .from('profiles')
    .select('username')
    .eq('id', user.id)
    .single()

  if (profileError || !currentProfile) {
    return { error: 'Profile not found' }
  }

  // If username changed, check uniqueness
  if (values.username !== currentProfile.username) {
    const { data: existing } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', values.username)
      .neq('id', user.id)
      .maybeSingle()
    if (existing) {
      return { error: 'Username already taken' }
    }
  }

  // Prepare update payload
  interface UpdateProfilePayload {
    full_name: string
    username: string
    country: string
    gender: 'male' | 'female' | 'other'
    weight_category: 'under_95kg' | 'over_95kg'
    avatar_url?: string
    titles: string[]
    social_links: Record<string, string>
  }

  const updatePayload: UpdateProfilePayload = {
    full_name: values.full_name,
    username: values.username,
    country: values.country,
    gender: values.gender,
    weight_category: values.weight_category,
    avatar_url: values.avatar_url,
    titles: values.titles
      ? values.titles
          .split(',')
          .map((t) => t.trim())
          .filter(Boolean)
      : [],
    social_links: values.social_links
      ? (() => {
          try {
            return JSON.parse(values.social_links)
          } catch {
            return {}
          }
        })()
      : {},
  }

  // Update profile
  const { error: updateError } = await supabase
    .from('profiles')
    .update(updatePayload)
    .eq('id', user.id)

  if (updateError) {
    return { error: updateError.message }
  }

  // Revalidate relevant paths
  revalidatePath('/account')
  revalidatePath(`/profile/${values.username}`)

  return { error: null }
}

/**
 * Secure server action for avatar upload
 * Implements comprehensive security measures:
 * - Server-side file validation with magic byte checking
 * - File size limits and type restrictions
 * - Secure filename generation
 * - Proper error handling and cleanup
 * CVSS 5.4 Insecure File Upload Fix
 */
export async function uploadProfileAvatar(formData: FormData): Promise<ActionResponse> {
  try {
    // SECURITY: Apply rate limiting to prevent avatar upload abuse
    const rateLimitResult = await profileRateLimit()
    if (!rateLimitResult.success) {
      return {
        error: rateLimitResult.error || 'Too many avatar uploads. Please try again later.',
      }
    }

    const cookieStore = await cookies();
    const supabase = getSupabaseRouteHandlerClient(cookieStore);

    // Authentication check
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { error: 'Authentication required' };
    }

    // Extract file from form data
    const file = formData.get('avatar') as File;
    if (!file) {
      return { error: 'No file provided' };
    }

    // Server-side validation - File size
    if (file.size > MAX_FILE_SIZE) {
      return { error: 'File size must be less than 2MB' };
    }

    // Server-side validation - MIME type
    if (!ALLOWED_IMAGE_TYPES.includes(file.type as typeof ALLOWED_IMAGE_TYPES[number])) {
      return { error: 'Only JPEG, PNG, WebP, and GIF files are allowed' };
    }

    // Critical security check - Magic byte validation
    const buffer = await file.arrayBuffer();
    const magicByteValidation = await validateImageBuffer(buffer, file.name);
    if (!magicByteValidation.valid) {
      return { error: magicByteValidation.error || 'Invalid file format' };
    }

    // Generate secure filename to prevent conflicts and path traversal
    const secureFileName = generateSecureFilename(user.id, file.name);
    const filePath = `avatars/${secureFileName}`;

    // Upload to Supabase Storage with security headers
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false, // Prevent overwriting for security
        contentType: file.type
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      return { error: 'Failed to upload file' };
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    // Update user profile with new avatar URL
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: publicUrl })
      .eq('id', user.id);

    if (updateError) {
      // Clean up uploaded file if profile update fails
      await supabase.storage.from('avatars').remove([filePath]);
      return { error: 'Failed to update profile' };
    }

    // Revalidate relevant paths
    revalidatePath('/account');
    return { success: true, data: { avatarUrl: publicUrl } };
  } catch (error) {
    console.error('Avatar upload error:', error);
    return { error: 'Internal server error' };
  }
}
