import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { updateProfile } from './actions'
import type { EditProfileFormValues } from './profileSchema'

// Create a mock Postgrest builder with proper chaining
const builder = {
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  neq: vi.fn().mockReturnThis(),
  single: vi.fn().mockResolvedValue({ data: null, error: null }),
  maybeSingle: vi.fn().mockResolvedValue({ data: null }),
  update: vi.fn().mockReturnThis(),
}

// Mock Supabase client
const mockClient = {
  auth: { getUser: vi.fn() },
  from: vi.fn(() => builder),
}

// Mock Supabase module and Next APIs
vi.mock('@/libs/supabase', () => ({
  getSupabaseRouteHandlerClient: (cookieStore: ReadonlyRequestCookies) =>
    mockClient,
}))
vi.mock('next/headers', () => ({ cookies: () => ({}) }))
vi.mock('next/cache', () => ({ revalidatePath: vi.fn() }))

describe('updateProfile', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('returns error if not authenticated', async () => {
    mockClient.auth.getUser.mockResolvedValueOnce({
      data: { user: null },
    })
    const result = await updateProfile({} as EditProfileFormValues)
    expect(result.error).toMatch(/not authenticated/i)
  })

  it('returns error if profile not found', async () => {
    mockClient.auth.getUser.mockResolvedValueOnce({
      data: { user: { id: 'u1' } },
    })
    builder.single.mockResolvedValueOnce({ data: null, error: true })
    const result = await updateProfile({
      username: 'test',
      full_name: 'Test',
      country: 'PL',
      gender: 'male',
      weight_category: 'under_95kg',
    } as EditProfileFormValues)
    expect(result.error).toMatch(/profile not found/i)
  })

  it('returns error if username taken', async () => {
    mockClient.auth.getUser.mockResolvedValueOnce({
      data: { user: { id: 'u1' } },
    })
    builder.single.mockResolvedValueOnce({
      data: { username: 'old' },
      error: null,
    })
    builder.maybeSingle.mockResolvedValueOnce({ data: { id: 'u2' } })
    const result = await updateProfile({
      username: 'new',
      full_name: 'Test',
      country: 'PL',
      gender: 'male',
      weight_category: 'under_95kg',
    } as EditProfileFormValues)
    expect(result.error).toMatch(/username.*taken/i)
  })

  it('updates profile and returns success', async () => {
    mockClient.auth.getUser.mockResolvedValueOnce({
      data: { user: { id: 'u1' } },
    })
    builder.single.mockResolvedValueOnce({
      data: { username: 'old' },
      error: null,
    })
    builder.maybeSingle.mockResolvedValueOnce({ data: null })
    // Simulate update().eq() resolved with no error
    builder.update.mockReturnThis()
    builder.eq.mockReturnValue(Promise.resolve({ data: null, error: null }))

    const result = await updateProfile({
      username: 'new',
      full_name: 'Test',
      country: 'PL',
      gender: 'male',
      weight_category: 'under_95kg',
    } as EditProfileFormValues)

    expect(result.error).toBeNull()
  })
})
