import { z } from 'zod/v4'

/**
 * Zod schema for profile editing.
 */
export const EditProfileSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  username: z
    .string()
    .min(4, 'Username must be at least 4 characters')
    .regex(/^[a-z0-9_]+$/, 'Lowercase letters, numbers, underscores only'),
  country: z.string().min(1, 'Country is required'),
  gender: z.enum(['male', 'female', 'other']),
  weight_category: z.enum(['under_95kg', 'over_95kg']),
  titles: z.string().optional(), // comma-separated, will be split
  social_links: z.string().optional(), // JSON string, will be parsed
  avatar_url: z.string().optional(),
})

export type EditProfileFormValues = z.infer<typeof EditProfileSchema>

export interface EditProfileFormProps {
  initialProfile: {
    id: string
    username: string
    full_name: string | null
    country: string | null
    gender: 'male' | 'female' | 'other' | null
    weight_category: 'under_95kg' | 'over_95kg' | null
    avatar_url: string | null
    titles: string[] | null
    social_links: Record<string, string> | null
  }
}
