import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import type React from 'react'
import { toast } from 'sonner'
/// <reference types="vitest/globals" />
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ExerciseForm, type ExerciseFormData } from './ExerciseForm' // Need form type
import { ExerciseModalForm } from './ExerciseModalForm'
import * as actions from './actions' // Import actions to mock

// --- Mocks --- //

// Mock ExerciseForm entirely
vi.mock('./ExerciseForm', async (importOriginal) => {
  const original = await importOriginal<typeof import('./ExerciseForm')>()
  return {
    ...original,
    // Replace ExerciseForm with a mock that captures props, especially onSubmit
    ExerciseForm: vi.fn(({ onSubmit, initialData }) => (
      <form
        data-testid="mock-exercise-form"
        onSubmit={(e) => {
          e.preventDefault()
          onSubmit(initialData || {})
        }}
      >
        <input
          type="hidden"
          data-testid="form-initial-data"
          value={JSON.stringify(initialData || {})}
        />
        <button type="submit">Mock Submit</button>
      </form>
    )),
  }
})

// Mock actions
vi.mock('./actions', () => ({
  createExercise: vi.fn(),
  updateExercise: vi.fn(),
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

// Mock ShadCN Dialog components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: { children: React.ReactNode; open: boolean }) =>
    open ? <div>{children}</div> : null,
  DialogContent: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  DialogHeader: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  DialogTitle: ({ children }: { children: React.ReactNode }) => (
    <h1>{children}</h1>
  ),
  DialogDescription: ({ children }: { children: React.ReactNode }) => (
    <p>{children}</p>
  ),
  DialogFooter: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  DialogClose: ({ children }: { children: React.ReactNode }) => (
    <button>{children}</button>
  ),
}))

describe('ExerciseModalForm', () => {
  const mockOnOpenChange = vi.fn()
  const user = userEvent.setup()

  const mockCreateExercise = actions.createExercise as ReturnType<typeof vi.fn>
  const mockUpdateExercise = actions.updateExercise as ReturnType<typeof vi.fn>
  const mockToastSuccess = toast.success as ReturnType<typeof vi.fn>
  const mockToastError = toast.error as ReturnType<typeof vi.fn>
  const MockExerciseForm = ExerciseForm as ReturnType<typeof vi.fn> // Get reference to the mock component

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
  }

  const initialData: Partial<ExerciseFormData> = {
    id: 'edit-id-456',
    title: 'Edit Title',
  }

  it('renders create title and description when no initialData', () => {
    render(<ExerciseModalForm {...defaultProps} />)
    expect(
      screen.getByRole('heading', { name: /create new exercise/i }),
    ).toBeInTheDocument()
    expect(screen.getByText(/fill in the details/i)).toBeInTheDocument()
  })

  it('renders edit title and description when initialData is present', () => {
    render(<ExerciseModalForm {...defaultProps} initialData={initialData} />)
    expect(
      screen.getByRole('heading', { name: /edit exercise/i }),
    ).toBeInTheDocument()
    expect(screen.getByText(/update the details/i)).toBeInTheDocument()
  })

  it('renders ExerciseForm with correct initialData and key when open', () => {
    render(<ExerciseModalForm {...defaultProps} initialData={initialData} />)
    const mockForm = screen.getByTestId('mock-exercise-form')
    expect(mockForm).toBeInTheDocument()
    // Check that the mock form received the initialData
    const hiddenInput = screen.getByTestId(
      'form-initial-data',
    ) as HTMLInputElement
    expect(JSON.parse(hiddenInput.value)).toEqual(initialData)
    // Check key prop (indirectly via mock call args)
    expect(MockExerciseForm).toHaveBeenCalledWith(
      expect.objectContaining({ key: initialData.id }),
      {},
    )
  })

  it('does not render ExerciseForm when not open', () => {
    render(<ExerciseModalForm {...defaultProps} open={false} />)
    expect(screen.queryByTestId('mock-exercise-form')).not.toBeInTheDocument()
  })

  it('calls createExercise when form submitted without initialData', async () => {
    mockCreateExercise.mockResolvedValue({ success: true })
    render(<ExerciseModalForm {...defaultProps} />)

    // Simulate form submission via the mock form
    const mockSubmitButton = screen.getByRole('button', {
      name: /mock submit/i,
    })
    await user.click(mockSubmitButton)

    await waitFor(() => {
      expect(mockCreateExercise).toHaveBeenCalledTimes(1)
      expect(mockUpdateExercise).not.toHaveBeenCalled()
      expect(mockToastSuccess).toHaveBeenCalledWith(
        'Exercise created successfully!',
      )
      expect(mockOnOpenChange).toHaveBeenCalledWith(false)
    })
  })

  it('calls updateExercise when form submitted with initialData', async () => {
    mockUpdateExercise.mockResolvedValue({ success: true })
    render(<ExerciseModalForm {...defaultProps} initialData={initialData} />)

    // Simulate form submission
    const mockSubmitButton = screen.getByRole('button', {
      name: /mock submit/i,
    })
    await user.click(mockSubmitButton)

    await waitFor(() => {
      expect(mockUpdateExercise).toHaveBeenCalledTimes(1)
      expect(mockUpdateExercise).toHaveBeenCalledWith(
        initialData.id,
        initialData,
      ) // Check args
      expect(mockCreateExercise).not.toHaveBeenCalled()
      expect(mockToastSuccess).toHaveBeenCalledWith(
        'Exercise updated successfully!',
      )
      expect(mockOnOpenChange).toHaveBeenCalledWith(false)
    })
  })

  it('shows error toast and keeps modal open on create failure', async () => {
    const errorMsg = 'DB constraint failed'
    mockCreateExercise.mockResolvedValue({ success: false, error: errorMsg })
    render(<ExerciseModalForm {...defaultProps} />)

    const mockSubmitButton = screen.getByRole('button', {
      name: /mock submit/i,
    })
    await user.click(mockSubmitButton)

    await waitFor(() => {
      expect(mockCreateExercise).toHaveBeenCalledTimes(1)
      expect(mockToastError).toHaveBeenCalledWith(
        `Failed to create exercise: ${errorMsg}`,
      )
      expect(mockOnOpenChange).not.toHaveBeenCalled() // Modal stays open
    })
  })

  it('shows error toast with validation issues on update failure', async () => {
    const issues = { fieldErrors: { title: ['Too short'] }, formErrors: [] }
    mockUpdateExercise.mockResolvedValue({
      success: false,
      error: 'Validation failed',
      issues,
    })
    render(<ExerciseModalForm {...defaultProps} initialData={initialData} />)

    const mockSubmitButton = screen.getByRole('button', {
      name: /mock submit/i,
    })
    await user.click(mockSubmitButton)

    await waitFor(() => {
      expect(mockUpdateExercise).toHaveBeenCalledTimes(1)
      expect(mockToastError).toHaveBeenCalledWith(
        expect.stringContaining('Validation failed: title: Too short'),
      )
      expect(mockOnOpenChange).not.toHaveBeenCalled()
    })
  })

  it('shows generic error toast on unexpected action error', async () => {
    const error = new Error('Unexpected network issue')
    mockCreateExercise.mockRejectedValue(error)
    render(<ExerciseModalForm {...defaultProps} />)

    const mockSubmitButton = screen.getByRole('button', {
      name: /mock submit/i,
    })
    await user.click(mockSubmitButton)

    await waitFor(() => {
      expect(mockCreateExercise).toHaveBeenCalledTimes(1)
      expect(mockToastError).toHaveBeenCalledWith(
        'An unexpected error occurred during submission.',
      )
      expect(mockOnOpenChange).not.toHaveBeenCalled()
    })
  })
})
