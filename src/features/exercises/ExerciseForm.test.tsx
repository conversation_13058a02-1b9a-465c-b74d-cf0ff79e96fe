import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
/// <reference types="vitest/globals" />
import type React from 'react'
import { vi } from 'vitest' // Import vi for mocking
import { ExerciseForm, type ExerciseFormData } from './ExerciseForm'

// Mock ShadCN components used
vi.mock('@/components/ui/button', () => ({
  Button: ({
    children,
    ...props
  }: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
    <button {...props}>{children}</button>
  ),
}))
vi.mock('@/components/ui/input', () => ({
  Input: (props: React.InputHTMLAttributes<HTMLInputElement>) => (
    <input {...props} />
  ),
}))
vi.mock('@/components/ui/label', () => ({
  Label: ({
    children,
    ...props
  }: React.LabelHTMLAttributes<HTMLLabelElement>) => (
    <label {...props}>{children}</label>
  ),
}))
vi.mock('@/components/ui/textarea', () => ({
  Textarea: (props: React.TextareaHTMLAttributes<HTMLTextAreaElement>) => (
    <textarea {...props} />
  ),
}))
vi.mock('@/libs/utils', () => ({
  cn: (...inputs: any[]) => inputs.filter(Boolean).join(' '),
}))

describe('ExerciseForm', () => {
  const mockSubmit = vi.fn()
  const user = userEvent.setup()

  beforeEach(() => {
    mockSubmit.mockClear()
  })

  const defaultProps = {
    onSubmit: mockSubmit,
  }

  const initialData: Partial<ExerciseFormData> = {
    id: 'test-id-123',
    title: 'Initial Title',
    description: 'Initial Description',
    video_tutorial_url: 'https://initial.com',
    equipment_required: 'Initial Equip',
    evaluation_criteria: 'Initial Criteria',
    medal_thresholds: '{ "bronze": 5 }',
  }

  it('renders all form fields', () => {
    render(<ExerciseForm {...defaultProps} />)

    expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/video tutorial url/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/equipment required/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/evaluation criteria/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/medal thresholds/i)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /save exercise/i }),
    ).toBeInTheDocument()
  })

  it('renders with default empty values when no initialData provided', () => {
    render(<ExerciseForm {...defaultProps} />)

    expect(screen.getByLabelText<HTMLInputElement>(/title/i).value).toBe('')
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/description/i).value,
    ).toBe('')
    expect(
      screen.getByLabelText<HTMLInputElement>(/video tutorial url/i).value,
    ).toBe('')
    expect(
      screen.getByLabelText<HTMLInputElement>(/equipment required/i).value,
    ).toBe('')
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/evaluation criteria/i).value,
    ).toBe('')
    // Check default JSON string for medal thresholds
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/medal thresholds/i).value,
    ).toBe('{ "bronze": 0, "silver": 0, "gold": 0 }')
  })

  it('pre-fills form fields with initialData', () => {
    render(<ExerciseForm {...defaultProps} initialData={initialData} />)

    expect(screen.getByLabelText<HTMLInputElement>(/title/i).value).toBe(
      initialData.title,
    )
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/description/i).value,
    ).toBe(initialData.description)
    expect(
      screen.getByLabelText<HTMLInputElement>(/video tutorial url/i).value,
    ).toBe(initialData.video_tutorial_url)
    expect(
      screen.getByLabelText<HTMLInputElement>(/equipment required/i).value,
    ).toBe(initialData.equipment_required)
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/evaluation criteria/i).value,
    ).toBe(initialData.evaluation_criteria)
    expect(
      screen.getByLabelText<HTMLTextAreaElement>(/medal thresholds/i).value,
    ).toBe(initialData.medal_thresholds)
  })

  it('updates form state on input change', async () => {
    render(<ExerciseForm {...defaultProps} />)
    const titleInput = screen.getByLabelText<HTMLInputElement>(/title/i)
    await user.type(titleInput, 'New Title')
    expect(titleInput.value).toBe('New Title')

    const descTextarea =
      screen.getByLabelText<HTMLTextAreaElement>(/description/i)
    await user.type(descTextarea, 'New Desc')
    expect(descTextarea.value).toBe('New Desc')
  })

  it('calls onSubmit with form data when submitted', async () => {
    render(<ExerciseForm {...defaultProps} />)
    const expectedData: ExerciseFormData = {
      // id is not part of the form values, added back in ExerciseModalForm
      title: 'Submitted Title',
      description: 'Submitted Desc',
      video_tutorial_url: 'https://submitted.com',
      equipment_required: 'Submitted Equip',
      evaluation_criteria: 'Submitted Criteria',
      medal_thresholds: '{ "bronze": 10 }',
    }

    await user.type(screen.getByLabelText(/title/i), expectedData.title)
    await user.type(
      screen.getByLabelText(/description/i),
      expectedData.description,
    )
    await user.type(
      screen.getByLabelText(/video tutorial url/i),
      expectedData.video_tutorial_url,
    )
    await user.type(
      screen.getByLabelText(/equipment required/i),
      expectedData.equipment_required,
    )
    await user.type(
      screen.getByLabelText(/evaluation criteria/i),
      expectedData.evaluation_criteria,
    )
    // Clear default and type new value for textarea
    await user.clear(screen.getByLabelText(/medal thresholds/i))
    await user.type(
      screen.getByLabelText(/medal thresholds/i),
      expectedData.medal_thresholds,
    )

    const submitButton = screen.getByRole('button', { name: /save exercise/i })
    await user.click(submitButton)

    await waitFor(() => {
      // Check that mockSubmit was called
      expect(mockSubmit).toHaveBeenCalledTimes(1)
      // Check the data passed to mockSubmit
      // Note: tanstack-form might pass extra internal state; focus on core data
      const submittedData = mockSubmit.mock.calls[0][0]
      expect(submittedData).toMatchObject(expectedData)
    })
  })

  it('calls onSubmit with form data including id when editing', async () => {
    render(<ExerciseForm {...defaultProps} initialData={initialData} />)

    const updatedTitle = 'Updated Title'
    await user.clear(screen.getByLabelText(/title/i))
    await user.type(screen.getByLabelText(/title/i), updatedTitle)

    const submitButton = screen.getByRole('button', { name: /save exercise/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledTimes(1)
      const submittedData = mockSubmit.mock.calls[0][0]
      expect(submittedData).toMatchObject({
        ...initialData,
        title: updatedTitle, // Check the updated field
        id: initialData.id, // Verify ID is included from initialData context
      })
    })
  })

  it('disables submit button when isSubmitting (simulated)', async () => {
    // We can't directly test isSubmitting state easily without deeper TanStack Form mocking
    // But we can check the button is initially enabled
    render(<ExerciseForm {...defaultProps} />)
    expect(screen.getByRole('button', { name: /save exercise/i })).toBeEnabled()
    // TODO: A more advanced test could involve mocking useForm state to test disabled state
  })
})
