'use client'

import { AuthContext } from '@/shared/libs/auth/context'
import type { AuthContextType } from '@/shared/types/auth'
import { useContext } from 'react'

/**
 * Authentication hook
 * Re-exports the main AuthContext for convenience
 *
 * @returns Authentication context value
 * @throws Error if used outside AuthProvider
 *
 * @example
 * ```typescript
 * const { user, profile, signInWithEmail } = useAuth()
 * if (user) {
 *   console.log('User is authenticated')
 * }
 * ```
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}
