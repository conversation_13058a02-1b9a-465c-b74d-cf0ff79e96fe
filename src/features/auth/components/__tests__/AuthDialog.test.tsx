import type { AuthContextType } from '@/shared/types/auth'
import { fireEvent, render, screen } from '@testing-library/react'
import type { ReactNode } from 'react'
import { describe, expect, it, vi } from 'vitest'
import { AuthDialog } from '../AuthDialog'

const mockAuth: Partial<AuthContextType> = {
  signInWithEmail: vi.fn().mockResolvedValue({ success: true, error: null }),
  signUpWithEmail: vi.fn().mockResolvedValue({ success: true, error: null }),
  signInWithSocial: vi.fn().mockResolvedValue({ success: true, error: null }),
  signOut: vi.fn(),
  user: null,
  profile: null,
  userRole: null,
  loading: false,
  isAuthenticated: false,
  resetPassword: vi.fn(),
  updatePassword: vi.fn(),
  updateProfile: vi.fn(),
  refreshProfile: vi.fn(),
}

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}))

function Wrapper({ children }: { children: ReactNode }) {
  return <div>{children}</div>
}

describe('AuthDialog', () => {
  it('renders sign in mode with email/password fields and social buttons', () => {
    render(
      <AuthDialog open={true} onOpenChange={() => {}} initialMode="signin" />,
      { wrapper: Wrapper },
    )
    expect(
      screen.getByRole('heading', { name: /sign in/i }),
    ).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /continue with google/i }),
    ).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /continue with facebook/i }),
    ).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /continue with apple/i }),
    ).toBeInTheDocument()
  })

  it('renders sign up mode when toggled', () => {
    render(
      <AuthDialog open={true} onOpenChange={() => {}} initialMode="signup" />,
      { wrapper: Wrapper },
    )
    expect(
      screen.getByRole('heading', { name: /sign up/i }),
    ).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
  })

  it('calls signInWithEmail on form submit', async () => {
    render(
      <AuthDialog open={true} onOpenChange={() => {}} initialMode="signin" />,
      { wrapper: Wrapper },
    )
    fireEvent.change(screen.getByLabelText(/email address/i), {
      target: { value: '<EMAIL>' },
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'pw' },
    })
    fireEvent.click(screen.getByRole('button', { name: /^sign in$/i }))
    expect(mockAuth.signInWithEmail).toHaveBeenCalled()
  })

  it('calls signUpWithEmail on form submit in sign up mode', async () => {
    render(
      <AuthDialog open={true} onOpenChange={() => {}} initialMode="signup" />,
      { wrapper: Wrapper },
    )
    fireEvent.change(screen.getByLabelText(/email address/i), {
      target: { value: '<EMAIL>' },
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'pw' },
    })
    fireEvent.click(screen.getByRole('button', { name: /^sign up$/i }))
    expect(mockAuth.signUpWithEmail).toHaveBeenCalled()
  })
})
