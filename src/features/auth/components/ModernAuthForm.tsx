'use client'

import {
  type AuthActionState,
  signInAction,
  signUpAction,
} from '@/app/auth/actions'
import { useActionState, useState } from 'react'

interface ModernAuthFormProps {
  mode?: 'signin' | 'signup'
  onSuccess?: () => void
}

/**
 * Modern authentication form using React 19 useActionState
 * Demonstrates the new pattern replacing complex useEffect state management
 */
export function ModernAuthForm({
  mode = 'signin',
  onSuccess,
}: ModernAuthFormProps) {
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>(mode)

  // React 19 useActionState - replaces multiple useState + useEffect patterns
  const [signInState, signInFormAction, isSignInPending] = useActionState(
    signInAction,
    null,
  )
  const [signUpState, signUpFormAction, isSignUpPending] = useActionState(
    signUpAction,
    null,
  )

  // Get current state based on mode
  const currentState = authMode === 'signin' ? signInState : signUpState
  const currentAction =
    authMode === 'signin' ? signInFormAction : signUpFormAction
  const isPending = authMode === 'signin' ? isSignInPending : isSignUpPending

  // Helper to get error styling
  const getErrorClassName = (errorType?: string) => {
    switch (errorType) {
      case 'EMAIL_ALREADY_REGISTERED':
        return 'bg-primary/10 text-primary border-primary/20 dark:bg-primary/20 dark:text-primary'
      case 'INVALID_CREDENTIALS':
        return 'bg-destructive/10 text-destructive border-destructive/20 dark:bg-destructive/20 dark:text-destructive'
      case 'EMAIL_NOT_CONFIRMED':
        return 'bg-accent/10 text-accent-foreground border-accent/20 dark:bg-accent/20 dark:text-accent'
      default:
        return 'bg-destructive/10 text-destructive border-destructive/20 dark:bg-destructive/20 dark:text-destructive'
    }
  }

  // Handle successful authentication
  if (currentState?.success && !currentState.needsConfirmation) {
    onSuccess?.()
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-card dark:bg-card shadow-lg rounded-lg p-6">
        <h2 className="text-2xl font-semibold text-center mb-6">
          {authMode === 'signin' ? 'Sign In' : 'Sign Up'}
        </h2>

        {/* Show confirmation message for signup */}
        {currentState?.success && currentState.needsConfirmation && (
          <div className="mb-4 p-4 bg-secondary/10 border border-secondary/20 rounded-md">
            <div className="text-sm text-secondary">
              <p className="font-medium">Check your email!</p>
              <p className="mt-1">
                {currentState.data?.message ||
                  'Please check your email and click the confirmation link.'}
              </p>
            </div>
          </div>
        )}

        {/* Error display */}
        {currentState?.error && (
          <div
            className={`mb-4 p-3 text-sm rounded-md border ${getErrorClassName(currentState.errorType)}`}
          >
            <p>{currentState.error}</p>
            {currentState.errorType === 'EMAIL_ALREADY_REGISTERED' &&
              authMode === 'signup' && (
                <button
                  type="button"
                  onClick={() => setAuthMode('signin')}
                  className="mt-2 text-sm underline hover:no-underline font-medium"
                >
                  Sign in instead
                </button>
              )}
          </div>
        )}

        {/* The form - notice how clean this is with server actions */}
        <form action={currentAction} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              disabled={isPending}
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium mb-1"
            >
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              disabled={isPending}
              minLength={6}
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50"
              placeholder="Enter your password"
            />
          </div>

          <button
            type="submit"
            disabled={isPending}
            className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isPending
              ? authMode === 'signin'
                ? 'Signing in...'
                : 'Signing up...'
              : authMode === 'signin'
                ? 'Sign In'
                : 'Sign Up'}
          </button>
        </form>

        {/* Mode toggle */}
        <div className="mt-4 text-center">
          {authMode === 'signin' ? (
            <p className="text-sm">
              Don't have an account?{' '}
              <button
                type="button"
                onClick={() => setAuthMode('signup')}
                disabled={isPending}
                className="text-primary hover:underline disabled:opacity-50"
              >
                Sign up
              </button>
            </p>
          ) : (
            <p className="text-sm">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => setAuthMode('signin')}
                disabled={isPending}
                className="text-primary hover:underline disabled:opacity-50"
              >
                Sign in
              </button>
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
