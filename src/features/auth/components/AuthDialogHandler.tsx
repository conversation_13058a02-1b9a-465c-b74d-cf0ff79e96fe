'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { AuthDialog } from './AuthDialog'

/**
 * AuthDialogHandler - Client component that handles opening the auth dialog
 * based on URL parameters.
 *
 * This component should be included in the root layout to enable auth dialog
 * opening from any page via URL parameters.
 */
export function AuthDialogHandler() {
  const [isOpen, setIsOpen] = useState(false)
  const searchParams = useSearchParams()

  // Determine the mode directly from searchParams for this render
  const currentRequestedAuthMode = searchParams?.get('auth')

  useEffect(() => {
    // Effect to open the dialog if a valid auth param is present
    if (
      currentRequestedAuthMode === 'signin' ||
      currentRequestedAuthMode === 'signup'
    ) {
      setIsOpen(true)
    }
    // Note: This effect doesn't close the dialog if the param disappears.
    // Dialog closure is handled by its onOpenChange (e.g., user interaction or auth success).
  }, [currentRequestedAuthMode]) // Re-run if the requested mode from URL changes

  // Determine the actual initialMode to pass to AuthDialog.
  // If currentRequestedAuthMode is 'signup', use 'signup'. Otherwise, default to 'signin'.
  // This ensures AuthDialog always gets a valid 'signin' or 'signup' string.
  const dialogInitialMode: 'signin' | 'signup' =
    currentRequestedAuthMode === 'signup' ? 'signup' : 'signin'

  return (
    <AuthDialog
      open={isOpen}
      onOpenChange={setIsOpen}
      initialMode={dialogInitialMode}
    />
  )
}
