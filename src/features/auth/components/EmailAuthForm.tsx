'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useState } from 'react'

interface EmailAuthFormProps {
  authMode: 'signin' | 'signup'
  onSubmit: (email: string, password: string) => Promise<void>
  isLoading: boolean
  error: string | null
}

export function EmailAuthForm({
  authMode,
  onSubmit,
  isLoading,
  error,
}: EmailAuthFormProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (email && password) {
      await onSubmit(email, password)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="grid gap-2">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 dark:bg-destructive/20 dark:text-destructive rounded-md border border-destructive/20">
          {error}
        </div>
      )}

      <div className="grid gap-1">
        <Label htmlFor="email">Email address</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email address"
          disabled={isLoading}
          required
          className="flex h-10 w-full rounded-md border border-input bg-transparent py-2 px-3 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:border-input dark:text-foreground"
        />
      </div>
      <div className="grid gap-1">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Enter your password"
          disabled={isLoading}
          required
          className="flex h-10 w-full rounded-md border border-input bg-transparent py-2 px-3 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:border-input dark:text-foreground"
        />
      </div>
      <Button
        type="submit"
        className="w-full mt-2 bg-primary text-primary-foreground hover:bg-primary/90"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <title>Loading</title>
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            {authMode === 'signin' ? 'Signing In...' : 'Signing Up...'}
          </>
        ) : authMode === 'signin' ? (
          'Sign In'
        ) : (
          'Sign Up'
        )}
      </Button>
    </form>
  )
}
