---
type: "always_apply"
---

# Security

## Sensitive Files

DO NOT read or modify:

- .env files
- \*_/config/secrets._
- \*_/_.pem
- Any file containing API keys, tokens, or credentials

## Security Practices

- Never commit sensitive files
- Use environment variables for secrets
- Keep credentials out of logs and output
- Don't touch sensitive data unless you absolutely need to (eg. payment processing)
- Don't needlessly collect users personal information without real requirement (if you do, be aware about the requirements on data protection)
- Don't trust any input from users (tools usually handle that pretty well, still keep it in mind)
- Set CORS, CSP, X-XSS-Protection, X-Frame-Options, X-Content-Type-Options, Strict-Transport-Security, X-Permitted-Cross-Domain-Policies
- Use ORM to sanitize raw SQL
- Check all redirects.
- Check webhook secrets
- Only allow redirects in webhook callbacks that are from known origin
- Set request limit per minute
- Use right hashing algorithm
- Sanitizing user input
- Sanitizing search parameters
- Using HttpOnly cookies
- Never using dangerouslyInnerHTML
