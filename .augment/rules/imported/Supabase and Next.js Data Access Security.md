---
type: "agent_requested"
description: "Supabase and Next.js Data Access Security"
---
# Rule File: Supabase and Next.js Data Access Security

**Core Principle:** To protect the integrity and security of the database, direct access to the Supabase `public` schema from the client-side should be prevented or heavily restricted. All database interactions must be routed through Next.js server-side logic (i.e., API Routes/Serverless Functions or `getServerSideProps`/App Router Route Handlers/Server Components).

**Detailed Rules:**

1.  **Restrict `public` Schema Access:**
    *   **Rule:** Configure Supabase to remove or severely restrict default privileges (SELECT, INSERT, UPDATE, DELETE) for `anon` and `authenticated` roles on the `public` schema for application-specific tables. Supabase-managed schemas (like `auth`, `storage`) will still function as intended.
    *   **Rationale:** The `public` schema is the default and often targeted. Exposing it directly for custom tables increases the attack surface. By restricting access, you force all operations through controlled backend endpoints.
    *   **Implementation:**
        *   In Supabase SQL editor, revoke default privileges for your custom tables/schema:
            ```sql
            -- For the entire public schema (use with caution, understand implications for Supabase features)
            -- REVOKE ALL ON SCHEMA public FROM anon;
            -- REVOKE ALL ON SCHEMA public FROM authenticated;

            -- More granularly for all tables in public schema (preferred for existing projects)
            REVOKE SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public FROM anon;
            REVOKE SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public FROM authenticated;
            -- Then, selectively GRANT necessary permissions to roles that Next.js server uses, if not using service_role.
            ```
        *   Ensure Row Level Security (RLS) is enabled for all tables, but prefer backend mediation as the primary security layer.

2.  **Utilize a Custom Private Schema (Recommended):**
    *   **Rule:** Create a new, dedicated schema (e.g., `app_private` or `private`) for your application's tables, functions, and views. This schema should NOT be exposed via the PostgREST API in `api.schemas` in your Supabase project settings.
    *   **Rationale:** Separating your application's data into a custom, non-exposed schema adds a significant layer of security and organization. It makes it clear that this schema is not intended for direct public consumption.
    *   **Implementation:**
        *   Create a new schema:
            ```sql
            CREATE SCHEMA private;
            ```
        *   Grant usage to your `service_role` (used by Next.js backend) and potentially a specific role for your Next.js application if not using `service_role` directly. Do NOT grant usage to `anon` or `authenticated` client-side roles.
            ```sql
            GRANT USAGE ON SCHEMA private TO service_role; -- Or your specific app role
            ALTER DEFAULT PRIVILEGES IN SCHEMA private GRANT ALL ON TABLES TO service_role; -- Or your specific app role
            ALTER DEFAULT PRIVILEGES IN SCHEMA private GRANT ALL ON FUNCTIONS TO service_role; -- Or your specific app role
            ALTER DEFAULT PRIVILEGES IN SCHEMA private GRANT ALL ON SEQUENCES TO service_role; -- Or your specific app role
            ```
        *   When creating tables, specify this schema: `CREATE TABLE private.my_table (...);`
        *   In Supabase Project Settings -> API -> Schema, ensure your custom private schema (e.g., `private`) is NOT listed in "Exposed schemas". Typically, only `public` (for Supabase Auth, Storage), `graphql_public` (if using GraphQL), and `storage` might be exposed.

3.  **Prioritize Next.js API Routes (Serverless Functions) for Data Operations:**
    *   **Rule:** All CUD (Create, Update, Delete) operations and most R (Read) operations should be handled by Next.js API Routes (e.g., in `pages/api/` or `app/api/`). These routes will use the Supabase JavaScript library initialized with the `service_role` key (or a dedicated secure role) to interact with the database.
    *   **Rationale:**
        *   **Security:** API Routes run on the server, so your database credentials (especially the powerful `service_role` key) are never exposed to the client.
        *   **Validation & Business Logic:** You can implement robust input validation, business logic, and error handling centrally before data hits the database.
        *   **Abstraction:** The client-side interacts with your defined API endpoints, not directly with database tables, making your frontend more resilient to backend data model changes.
    *   **Implementation:**
        *   Create API routes in your `pages/api/` directory (or `app/api/` for App Router).
        *   Initialize Supabase client with the `service_role` key (stored securely in environment variables) within these server-side functions.
            ```javascript
            // Example: pages/api/mydata.js
            import { createClient } from '@supabase/supabase-js';

            const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
            const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Ensure this is NOT exposed to client
            const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

            export default async function handler(req, res) {
              if (req.method === 'POST') {
                const { body } = req;
                // TODO: Add input validation for 'body'
                const { data, error } = await supabaseAdmin
                  .from('my_table', { schema: 'private' }) // Accessing table in private schema
                  .insert([body.payload_for_insert]);

                if (error) return res.status(400).json({ error: error.message });
                return res.status(201).json(data);
              }
              // ... other methods
            }
            ```

4.  **Use `getServerSideProps` or Route Handlers (App Router) for Server-Side Data Fetching:**
    *   **Rule:** For pages that require data from Supabase at request time to be server-rendered, use `getServerSideProps` (Pages Router) or fetch within Route Handlers/Server Components (App Router). These also run on the server and can securely use the `service_role` key or a user-session-based client if appropriate for RLS-protected data.
    *   **Rationale:** Keeps database credentials secure and allows data to be fetched and rendered on the server, improving SEO and initial page load performance.
    *   **Implementation (Pages Router - `getServerSideProps`):**
        ```javascript
        // Example: pages/my-secure-page.js
        import { createClient } from '@supabase/supabase-js';

        export async function getServerSideProps(context) {
          const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
          const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
          const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

          const { data, error } = await supabaseAdmin
            .from('my_table', { schema: 'private' })
            .select('*');

          if (error) console.error('Error fetching data in gSSP:', error);
          return { props: { initialData: data || [] } };
        }
        // ... component uses initialData
        ```
    *   **Implementation (App Router - Server Component):**
        ```javascript
        // Example: app/my-secure-page/page.js
        import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
        import { cookies } from 'next/headers';
        // For admin actions, you might still create a separate service_role client
        import { createClient } from '@supabase/supabase-js';


        async function getData() {
          // For data accessible by user based on RLS:
          // const supabaseUserClient = createServerComponentClient({ cookies });
          // const { data: userData, error: userError } = await supabaseUserClient.from(...);

          // For admin/service level access:
          const supabaseAdmin = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL,
            process.env.SUPABASE_SERVICE_ROLE_KEY,
            { auth: { persistSession: false } } // Important for server-side admin client
          );
          const { data, error } = await supabaseAdmin
            .from('my_table', { schema: 'private' })
            .select('*');

          if (error) throw new Error(`Failed to fetch data: ${error.message}`);
          return data;
        }

        export default async function MySecurePage() {
          const data = await getData();
          // Render page with data
        }
        ```

5.  **Limited and Secure Client-Side Supabase SDK Usage:**
    *   **Rule:** The Supabase client-side SDK (initialized with the `anon` key) should primarily be used for authentication, managing user sessions, real-time subscriptions (on specifically prepared, RLS-protected tables/views if necessary), and invoking Edge Functions. It should NOT be used for direct CUD queries to your primary data tables in the `private` schema or a locked-down `public` schema.
    *   **Rationale:** The `anon` key has limited, publicly accessible privileges. Relying on it for sensitive data access would necessitate exposing your schema and relying solely on RLS, which should be a defense-in-depth measure, not the primary shield.
    *   **Permissible client-side SDK uses:**
        *   User authentication (`supabase.auth.signInWithPassword()`, etc.).
        *   Managing user sessions and listening to auth state changes.
        *   Real-time subscriptions *only if* the tables/channels are specifically designed for safe client-side exposure and data is strictly controlled by RLS.
        *   Invoking Supabase Edge Functions (which can then use the `service_role` key securely on the server-side).

**Summary & Benefits:**

*   **Enhanced Security:** Dramatically reduces the risk of unauthorized database access, SQL injection, or data leakage by centralizing data operations through a controlled backend layer using elevated privileges.
*   **Centralized Logic:** Business rules, validation, and data transformation are handled in one place (Next.js backend), making the system easier to maintain, audit, and debug.
*   **Scalability & Flexibility:** An API-driven architecture is more scalable and allows different frontends (e.g., mobile apps) to consume the same secure backend logic.
*   **Improved Testability:** Backend API routes and server-side data fetching logic can be tested independently of the UI.
