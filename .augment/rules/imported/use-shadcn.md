---
description: Guidelines for using shadcn/ui components in the project, including installation commands, import patterns, styling conventions, and best practices for component composition. Essential when working with UI components, forms, dialogs, menus, or any interface elements.
globs:
alwaysApply: false
type: "agent_requested"
---
# Shadcn Component Usage Guide

This project uses shadcn/ui components for consistent UI elements. The components are installed and configured in the `app/src/components/ui` directory.

## Component Installation

To add new shadcn components, use the CLI:

```bash
npx shadcn@latest add component-name
```

For example, to add the context menu component:
```bash
npx shadcn@latest add context-menu
```

## Component Location

All shadcn components are installed in [app/src/components/ui](mdc:app/src/components/ui). When importing components, use the relative path to this directory.

Example usage:

```typescript
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "../../../app/src/components/ui/context-menu";
```

## Styling

Shadcn components use Tailwind CSS for styling. The project's Tailwind configuration is in [tailwind.config.js](mdc:tailwind.config.js).

Common styling patterns:
- Use `className="w-64"` for fixed widths
- Use `className="ml-auto"` for right-aligned content
- Use `className="text-muted-foreground"` for secondary text

## Best Practices

1. Always wrap shadcn components in your own component with a specific interface for your use case
2. Use the `asChild` prop when you need to customize the trigger element
3. Follow the component's documentation for proper composition
4. Use the provided className props for styling customization

Example of proper component wrapping:
```typescript
type PathEditorContextMenuProps = {
  children: ReactNode;
  onFlipHorizontal: () => void;
  // ... other props
};

export function PathEditorContextMenu({ children, ...props }: PathEditorContextMenuProps) {
  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      {/* ... menu content */}
    </ContextMenu>
  );
}
```