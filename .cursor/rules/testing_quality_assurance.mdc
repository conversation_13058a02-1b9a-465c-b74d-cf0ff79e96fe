---
description: Comprehensive testing guidelines covering unit tests (Vitest + React Testing Library), integration tests, E2E tests (Playwright), and local Supabase testing strategies. Includes test coverage requirements, mocking patterns for Supabase clients, and quality assurance practices for the entire application stack
globs: 
alwaysApply: false
---
---
auto_attach_globs:
  - "**/__tests__/**/*.{ts,tsx,js,jsx}"
  - "**/*.{test,spec}.{ts,tsx,js,jsx}"
  - "**/tests/**/*.{ts,tsx,js,jsx}"
  - "vitest.config.{ts,js}"
  - "jest.config.{ts,js}"
  - "playwright.config.{ts,js}"
  - ".github/workflows/*test*.{yml,yaml}"
---

# Testing & Quality Assurance

This document outlines the testing and quality assurance practices for the project.

## Testing Strategy

Our testing strategy encompasses multiple levels to ensure code quality and application stability.

### Unit Tests
- **Focus:** Individual functions, utilities, and React hooks.
- **Goal:** Verify the correctness of isolated pieces of logic.
- **Tools:** Vitest, React Testing Library.
- **Details:** Ensure all helper functions, custom hooks, and utility modules have comprehensive unit tests covering various scenarios, including edge cases and error conditions.

### Integration Tests
- **Focus:** Interactions between multiple components, services, or modules.
- **Goal:** Ensure that different parts of the application work together as expected.
- **Tools:** Vitest, React Testing Library.
- **Details:** Test complex components that involve state management, context, or interactions with other components. For instance, a form component with its input fields and submission logic, or a data display component that fetches and renders data.

### End-to-End (E2E) Tests
- **Focus:** Complete user workflows and critical application flows.
- **Goal:** Simulate real user scenarios from the user interface to the backend and database.
- **Tools:** Playwright (preferred, can be added progressively).
- **Details:** Cover critical user journeys such as user registration, login, core feature usage (e.g., submitting an exercise, viewing rankings), and profile management. These tests ensure the entire system functions correctly from the user's perspective.

### Local Supabase Testing
- **Focus:** Verifying interactions with the Supabase backend in a local development environment.
- **Goal:** Ensure that database operations, authentication, and RLS policies work as expected before deploying.
- **Tools:** Supabase CLI for local development and testing.
- **Details:** Utilize the Supabase local development environment (`supabase start`) to test schema migrations, database functions, RLS policies, and API interactions. Mock Supabase client calls in unit/integration tests where direct DB interaction isn't the focus, but use the local instance for testing actual DB-dependent logic.

## Test Coverage
- **Requirement:** Maintain a minimum test coverage threshold (e.g., 80%) across the codebase.
- **Goal:** Ensure that a significant portion of the code is covered by automated tests, reducing the likelihood of regressions.
- **Tools:** Coverage reporting tools integrated with Vitest.
- **Details:** Regularly monitor test coverage reports. Prioritize writing tests for new features and critical bug fixes. Strive to increase coverage for existing, untested code over time.
