---
description: Enforces general code quality and style guidelines across the entire project, including TypeScript best practices, functional programming principles, and code review processes.
globs: **/*.*
---
- Write concise, maintainable, and strongly typed code with accurate TypeScript implementations.
- Embrace functional, declarative programming. Avoid OOP and classes.
- Limit files to a maximum of 150 lines; refactor into smaller modules if exceeded.
- Prefer iteration and modularization over duplication.
- Use descriptive, semantic variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
- Use lowercase with dashes for directories and files (e.g., `components/auth-wizard`).
- Favor named exports for components.
- Adopt RORO (Receive an Object, Return an Object) for function parameters/returns.
- Always attain to use DRY (Don't Repeat Yourself) principles.
- Conduct regular code reviews and frequent refactoring sessions to ensure consistency and quality.
- Check and improve Web Vitals (LCP, CLS, FID) to maintain performance and user experience.