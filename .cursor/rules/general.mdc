---
description: 
globs: **/*.*
alwaysApply: false
---
- Follow best practices, lean towards agile methodologies
- Prioritize modularity, DRY, performance, and security
- First break tasks into distinct prioritized steps, then follow the steps
- Prioritize tasks/steps you'll address in each response
- Don't repeat yourself
- Keep responses very short, unless I include a Vx value:
  - VO default, code golf
  - V1 concise
  - V2 simple
  - V3 verbose, DRY with extracted functions