---
description: 
globs: 
alwaysApply: true
---
**General Instructions for LLM:**
*   **Testing:** Each prompt includes specific "Testing Requirements". Implement tests using Vitest and React Testing Library primarily. Use `vi.mock` for Supabase clients. E2E tests (Playwright) can be added later but consider basic testability now.
*   **Documentation:** Each prompt includes a "Documentation Update" step. Ensure `docs/TODO.md` is updated, and code includes relevant JSDoc/TSDoc comments.
*   **Code Style:** Strictly adhere to project-specific linting and formatting rules defined in Biome configurations (assumed setup). Refer to `.cursor/rules/*` where applicable.
*   **Error Handling:** Implement robust error handling for async operations, Supabase calls, and user inputs.