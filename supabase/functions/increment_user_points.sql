-- Function to increment a user's total points
-- This function is used when a submission is approved and points are awarded
-- It atomically updates the user's total_points in the profiles table
CREATE OR REPLACE FUNCTION increment_user_points(user_id_param UUID, points_param INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Update the user's total points
  UPDATE profiles
  SET 
    total_points = total_points + points_param,
    updated_at = NOW()
  WHERE id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
