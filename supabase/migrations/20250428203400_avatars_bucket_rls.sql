-- Migration: Avatars Storage Bucket RLS Policies
-- Description: Ensures public read and authenticated user write access to their own avatar in the 'avatars' bucket.
-- Created At: 2025-04-28 20:34:00 UTC

-- 1. Ensure the avatars bucket exists (idempotent)
insert into storage.buckets (id, name, public)
values ('avatars', 'avatars', true)
on conflict (id) do nothing;

-- 2. Public SELECT access to all avatars
-- Allows anyone (anon or authenticated) to read avatars at public/*
create policy "Public can read all avatars"
on storage.objects
for select
to authenticated, anon
using (
  bucket_id = 'avatars'
  and name like 'public/%' -- Simplified: Only check path prefix
);

-- 3. Authenticated users can INSERT/UPDATE/DELETE only their own avatar at public/{uid}/*
create policy "User can write their own avatar"
on storage.objects
for insert
to authenticated
with check (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);

create policy "User can update their own avatar"
on storage.objects
for update
to authenticated
using (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
)
with check (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);

create policy "User can delete their own avatar"
on storage.objects
for delete
to authenticated
using (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);

-- 4. (Optional) Comment for clarity
comment on policy "Public can read all avatars" on storage.objects is 'Allows public read access to all avatars in the avatars bucket.';
comment on policy "User can write their own avatar" on storage.objects is 'Allows authenticated users to upload their own avatar to public/{uid}/* in the avatars bucket.';
comment on policy "User can update their own avatar" on storage.objects is 'Allows authenticated users to update their own avatar in public/{uid}/* in the avatars bucket.';
comment on policy "User can delete their own avatar" on storage.objects is 'Allows authenticated users to delete their own avatar in public/{uid}/* in the avatars bucket.';
