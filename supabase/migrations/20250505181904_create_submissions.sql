-- Migration: Create Submissions Table, ENUMs, Indexes, and RLS Policies
-- Description: Implements the public.submissions table for athlete video submissions, including ENUM types, all required constraints, indexes, and Row Level Security (RLS) policies for athletes, grandmasters, and admins. Also ensures strict video_url validation and composite index for evaluation queue.
-- Created At: 2025-05-05 18:19:04 UTC

-- 1. ENUM Types

create type public.submission_status as enum ('pending', 'approved', 'rejected');
comment on type public.submission_status is 'Workflow status of a submission: pending, approved, or rejected.';

create type public.medal_type as enum ('none', 'bronze', 'silver', 'gold', 'platinum', 'diamond');
comment on type public.medal_type is 'Medal awarded after evaluation: none, bronze, silver, gold, platinum, or diamond.';

-- 2. Submissions Table

create table public.submissions (
    id bigint generated by default as identity primary key,
    user_id uuid not null references public.profiles(id) on delete cascade,
    exercise_id bigint not null references public.exercises(id) on delete cascade,
    video_url text not null check (
        video_url ~* '^https?://(www\.)?(youtube\.com|youtu\.be|tiktok\.com|www\.tiktok\.com|instagram\.com|www\.instagram\.com)/.+'
    ),
    weight_lifted numeric not null check (weight_lifted > 0),
    notes text,
    status public.submission_status not null default 'pending',
    submitted_at timestamptz not null default now(),
    evaluated_at timestamptz,
    evaluated_by uuid references public.profiles(id) on delete set null,
    points_awarded integer not null default 0 check (points_awarded >= 0),
    medal_awarded public.medal_type not null default 'none',
    private_comments text
);

comment on table public.submissions is 'Athlete video submissions for exercise evaluation and ranking.';

comment on column public.submissions.id is 'Unique identifier for the submission.';
comment on column public.submissions.user_id is 'User (athlete) who made the submission. FK to profiles(id).';
comment on column public.submissions.exercise_id is 'Exercise being performed. FK to exercises(id).';
comment on column public.submissions.video_url is 'URL to the performance video. Must be YouTube, TikTok, or Instagram.';
comment on column public.submissions.weight_lifted is 'Weight lifted in the submission (kg). Must be positive.';
comment on column public.submissions.notes is 'Optional notes provided by the athlete.';
comment on column public.submissions.status is 'Submission status: pending, approved, or rejected.';
comment on column public.submissions.submitted_at is 'Timestamp when the submission was created.';
comment on column public.submissions.evaluated_at is 'Timestamp when the submission was evaluated.';
comment on column public.submissions.evaluated_by is 'User (GM/Admin) who evaluated the submission. FK to profiles(id).';
comment on column public.submissions.points_awarded is 'Points awarded for the submission. Zero if rejected.';
comment on column public.submissions.medal_awarded is 'Medal awarded for the submission. None if rejected.';
comment on column public.submissions.private_comments is 'Private evaluator comments, visible only to the athlete.';

-- 3. Indexes

create index idx_submissions_user_id on public.submissions(user_id);
comment on index idx_submissions_user_id is 'Accelerates look-ups for a user''s submissions.';

create index idx_submissions_exercise_id on public.submissions(exercise_id);
comment on index idx_submissions_exercise_id is 'Speeds up per-exercise feeds and analytics.';

create index idx_submissions_status_submitted on public.submissions(status, submitted_at desc);
comment on index idx_submissions_status_submitted is 'Supports evaluation queue (status = pending) and recent-first listings.';

-- 4. Enable Row Level Security

alter table public.submissions enable row level security;

-- 5. Helper Functions for RLS

-- NOTE: Do NOT drop or redefine is_admin(user_id uuid) if it already exists.
-- The function is already defined in the user profiles migration and is used by RLS policies on profiles.
-- If you need to ensure it exists, you may optionally re-create it here with the same signature and body, but do NOT drop it.

-- Rationale: Use security definer to allow RLS policies to use this function even if the calling role lacks direct privileges to read public.profiles.
create or replace function public.is_admin(user_id uuid)
returns boolean
language plpgsql
security definer
set search_path = ''
as $$
begin
    return exists(
        select 1 from public.profiles p where p.id = user_id and p.role = 'admin'
    );
end;
$$;

comment on function public.is_admin(user_id uuid) is 'Returns true if the given user ID has the admin role.';

-- Rationale: Use security definer to allow RLS policies to use this function even if the calling role lacks direct privileges to read public.profiles.
create or replace function public.is_grandmaster(uid uuid)
returns boolean
language sql
security definer
set search_path = ''
as $$
    select exists(
        select 1 from public.profiles p where p.id = uid and p.role = 'grandmaster'
    );
$$;

comment on function public.is_grandmaster(uid uuid) is 'Returns true if the given user ID has the grandmaster role.';

-- 6. RLS Policies

-- 6.1 Athletes: SELECT own submissions
-- Rationale: Only allow authenticated athletes to read their own submissions for privacy.
create policy "Athletes can read their own submissions"
    on public.submissions
    for select
    to authenticated
    using (user_id = (select auth.uid()));

-- 6.2 Athletes: INSERT own submissions (enforce defaults)
-- Rationale: Allow athletes to insert submissions only for themselves and enforce default status, points, and medals.
create policy "Athletes can create their own submissions"
    on public.submissions
    for insert
    to authenticated
    with check (
        user_id = (select auth.uid())
        and status = 'pending'
        and points_awarded = 0
        and medal_awarded = 'none'
    );

-- 6.3 GMs/Admins: SELECT all submissions
-- Rationale: Grant grandmasters and admins permission to view all submissions for oversight.
create policy "GMs and Admins can read all submissions"
    on public.submissions
    for select
    to authenticated
    using (
        public.is_grandmaster(auth.uid()) or public.is_admin(auth.uid())
    );

-- 6.4 GMs/Admins: UPDATE for evaluation
-- Rationale: Allow grandmasters and admins to update submission status and record the evaluator.
create policy "GMs and Admins can evaluate submissions"
    on public.submissions
    for update
    to authenticated
    using (
        public.is_grandmaster(auth.uid()) or public.is_admin(auth.uid())
    )
    with check (
        status in ('approved', 'rejected')
        and evaluated_by = (select auth.uid())
    );

-- 6.5 Admins: DELETE any submission
-- Rationale: Allow admins to delete any submission for moderation purposes.
create policy "Admins can delete any submission"
    on public.submissions
    for delete
    to authenticated
    using (public.is_admin(auth.uid()));

-- End of migration
