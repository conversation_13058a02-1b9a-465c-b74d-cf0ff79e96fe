-- Migration: Add JWT Custom Claims Auth Hook
-- Purpose: Optimize middleware performance by adding user role to JWT tokens
-- This eliminates the need for database queries on every request
-- CVSS 7.2 Performance DoS Fix

-- Create the custom access token hook function
-- This function runs when JWT tokens are issued/refreshed
create or replace function public.custom_access_token_hook(event jsonb)
returns jsonb
language plpgsql
stable
security definer
as $$
declare
  claims jsonb;
  user_role text;
begin
  -- Extract existing claims from the event
  claims := event->'claims';
  
  -- Fetch the user role from profiles table
  select role into user_role 
  from public.profiles 
  where id = (event->>'user_id')::uuid;
  
  -- Add the role to JWT custom claims if found
  if user_role is not null then
    claims := jsonb_set(claims, '{user_role}', to_jsonb(user_role));
  end if;
  
  -- Return the updated event with new claims
  return jsonb_set(event, '{claims}', claims);
end;
$$;

-- Grant necessary permissions for the auth hook
grant execute on function public.custom_access_token_hook(jsonb) to supabase_auth_admin;

-- Add comment for documentation
comment on function public.custom_access_token_hook(jsonb) is 
'Auth hook that adds user role to JWT custom claims for performance optimization. Eliminates database queries in middleware.';
