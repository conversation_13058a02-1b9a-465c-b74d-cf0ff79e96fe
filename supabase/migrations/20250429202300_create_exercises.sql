-- Migration: Create the 'exercises' table
-- This table stores information about different exercises available in the application.

create table public.exercises (
  id bigint generated always as identity primary key,
  title text not null,
  alias text not null,
  description text,
  video_tutorial_url text,
  equipment_required text[],
  evaluation_criteria jsonb not null,
  medal_thresholds jsonb not null,
  created_by uuid references public.profiles(id) on delete set null,
  created_at timestamptz default timezone('utc'::text, now()) not null,
  updated_at timestamptz default timezone('utc'::text, now()) not null
);

-- Add comments to the table and columns
comment on table public.exercises is 'Stores details about various exercises, including descriptions, video links, equipment, evaluation methods, medal criteria, and thresholds.';
comment on column public.exercises.id is 'Unique identifier for the exercise.';
comment on column public.exercises.title is 'The name or title of the exercise.';
comment on column public.exercises.alias is 'Short alias or code name for the exercise (e.g., "bench_press", "deadlift").';
comment on column public.exercises.description is 'A detailed description of how to perform the exercise.';
comment on column public.exercises.video_tutorial_url is 'URL link to a video demonstrating the exercise.';
comment on column public.exercises.equipment_required is 'List of equipment needed for the exercise (e.g., {\"dumbbell\", \"bench\"}).';
comment on column public.exercises.evaluation_criteria is 'JSON object describing how exercise performance is measured. Structure: {"type": "reps_in_time", "time_limit": 60, "description": "Max reps in 1 minute"} or {"type": "time_to_complete", "target_reps": 50, "description": "Time to complete 50 reps"}.';
comment on column public.exercises.medal_thresholds is 'JSON object defining the scores/reps required for different medals based on categories. Structure: {"women_under_95": {"bronze": 5, "silver": 10, "gold": 15, "platinum": 20}, "men_over_95": {"bronze": 8, "silver": 15, "gold": 22, "platinum": 30}} etc. Keys represent categories (e.g., gender_weight), values are objects mapping medal names to required scores/reps.';
comment on column public.exercises.created_by is 'Identifier of the user (from public.profiles) who created the exercise record. Null if the user is deleted.';
comment on column public.exercises.created_at is 'Timestamp when the exercise record was created.';
comment on column public.exercises.updated_at is 'Timestamp when the exercise record was last updated.';

-- Enable Row Level Security (RLS) for the table
alter table public.exercises enable row level security;

-- Add unique constraint on alias column
alter table public.exercises add constraint exercises_alias_unique unique (alias);

-- Add basic indexes for frequently queried columns
create index idx_exercises_created_by on public.exercises(created_by);

-- Add index on the title column for faster lookups
create index exercises_title_idx on public.exercises(title);

-- Add index on the alias column for faster lookups
create index exercises_alias_idx on public.exercises(alias);

-- RLS Policies
-- Allow public read access
create policy "Allow public read access" on public.exercises
for select
to authenticated, anon
using (true);

-- Allow admin insert access
create policy "Allow admin insert access" on public.exercises
for insert to authenticated
with check (public.is_admin(auth.uid()));

-- Allow admin update access
create policy "Allow admin update access" on public.exercises
for update to authenticated
using (public.is_admin(auth.uid()))
with check (public.is_admin(auth.uid()));

-- Allow admin delete access
create policy "Allow admin delete access" on public.exercises
for delete to authenticated
using (public.is_admin(auth.uid()));
