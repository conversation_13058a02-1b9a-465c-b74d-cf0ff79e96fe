-- Migration: Create User Profiles Schema
-- Description: Sets up the profiles table linked to auth.users, including roles, RLS, and auto-creation trigger.
-- Created At: 2024-05-02 17:59:45 UTC

-- 1. Define ENUM types
create type public.user_role as enum ('athlete', 'grandmaster', 'admin');
comment on type public.user_role is 'Defines the roles a user can have within the application.';

create type public.user_gender as enum ('male', 'female', 'other'); -- Added 'other' based on specs
comment on type public.user_gender is 'Defines the gender of the user.';

create type public.weight_category as enum ('under_95kg', 'over_95kg');
comment on type public.weight_category is 'Defines the weight category for competitive ranking.';

-- 2. Create profiles table
create table public.profiles (
    id uuid primary key references auth.users(id) on delete cascade,
    username text unique not null check (
        username = lower(username) and -- Enforce lowercase
        length(username) > 3 and -- Enforce minimum length
        username ~ '^[a-z0-9_]+$' -- Allow lowercase letters, numbers, and underscores
    ),
    full_name text check (length(full_name) > 0), -- Basic check for non-empty string
    avatar_url text check (avatar_url ~ '^https?://.+'), -- Basic URL format check
    country text,
    gender public.user_gender,
    weight_category public.weight_category,
    titles text[],
    social_links jsonb,
    total_points integer not null default 0 check (total_points >= 0),
    role public.user_role not null default 'athlete',
    last_submission_at timestamptz,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

comment on table public.profiles is 'Stores user profile information, extending auth.users.';
comment on column public.profiles.id is 'Link to the corresponding user in auth.users.';
comment on column public.profiles.username is 'Unique, public-facing username for the user.';
comment on column public.profiles.full_name is 'Full name of the user.';
comment on column public.profiles.avatar_url is 'URL to the user''s profile picture.';
comment on column public.profiles.country is 'User''s country of residence.';
comment on column public.profiles.gender is 'User''s gender.';
comment on column public.profiles.weight_category is 'User''s weight category for rankings.';
comment on column public.profiles.titles is 'Array of armwrestling titles held by the user.';
comment on column public.profiles.social_links is 'JSON object containing links to user''s social media profiles.';
comment on column public.profiles.total_points is 'Accumulated points from evaluated submissions.';
comment on column public.profiles.role is 'User role determining permissions within the app.';
comment on column public.profiles.last_submission_at is 'Timestamp of the user''s last submission for rate limiting.';
comment on column public.profiles.created_at is 'Timestamp when the profile was created.';
comment on column public.profiles.updated_at is 'Timestamp when the profile was last updated.';

-- 3. Handle updated_at trigger function
-- Function to update the updated_at column
create or replace function public.handle_updated_at()
returns trigger
language plpgsql
security invoker -- Use invoker security as it doesn't involve sensitive data access
set search_path = '' -- Ensure predictable schema resolution
as $$
begin
  new.updated_at = now();
  return new;
end;
$$;

-- Trigger to automatically update updated_at timestamp on row modification
create trigger on_profile_update
  before update on public.profiles
  for each row
  execute function public.handle_updated_at();

comment on function public.handle_updated_at() is 'Updates the updated_at timestamp for a record.';
comment on trigger on_profile_update on public.profiles is 'Calls handle_updated_at function before any update operation on the profiles table.';

-- 4. Enable Row Level Security (RLS)
alter table public.profiles enable row level security;

-- 5. Define RLS policies
-- 5.1. Profiles are viewable by everyone.
create policy "Profiles are viewable by everyone."
  on public.profiles for select
  to authenticated, anon
  using ( true );

comment on policy "Profiles are viewable by everyone." on public.profiles is 'Allows any user (logged in or not) to view profiles.';

-- 5.2. Users can insert their own profile.
create policy "Users can insert their own profile."
  on public.profiles for insert
  to authenticated
  with check ( (select auth.uid()) = id );

comment on policy "Users can insert their own profile." on public.profiles is 'Allows authenticated users to insert a profile record for themselves, typically handled by the handle_new_user trigger.';

-- 5.3. Users can update their own profile.
create policy "Users can update their own profile."
  on public.profiles for update
  to authenticated
  using ( (select auth.uid()) = id )
  with check ( (select auth.uid()) = id );

comment on policy "Users can update their own profile." on public.profiles is 'Allows authenticated users to update their own profile information. Specific field restrictions (role, points) are handled by a trigger.';

-- 5.4. Admin users can manage all profiles.
-- Helper function to check if user is admin (SECURITY DEFINER for necessary access)
create or replace function public.is_admin(user_id uuid)
returns boolean
language plpgsql
security definer -- Needs elevated privileges to check roles potentially before RLS allows access
set search_path = '' -- Ensure predictable schema resolution
as $$
declare
  user_role public.user_role;
begin
  -- Ensure the function caller is authenticated before proceeding
  if auth.role() = 'authenticated' then
    select role into user_role from public.profiles where id = user_id;
    return user_role = 'admin';
  else
    -- Deny access if the caller is not authenticated
    return false;
  end if;
exception
  -- Handle cases where the user profile might not exist yet or other errors
  when others then
    return false;
end;
$$;

comment on function public.is_admin(uuid) is 'Checks if the specified user ID has the admin role. Requires SECURITY DEFINER.';

-- Admin SELECT Policy
create policy "Admin users can view all profiles."
  on public.profiles for select
  to authenticated
  using ( public.is_admin(auth.uid()) );

comment on policy "Admin users can view all profiles." on public.profiles is 'Allows users with the admin role to view any profile.';

-- Admin INSERT Policy (Admins typically shouldn't directly insert profiles, user creation trigger handles it, but provide if needed)
create policy "Admin users can insert any profile."
  on public.profiles for insert
  to authenticated
  with check ( public.is_admin(auth.uid()) );

comment on policy "Admin users can insert any profile." on public.profiles is 'Allows users with the admin role to insert any profile (use with caution, normally handled by trigger).';

-- Admin UPDATE Policy
create policy "Admin users can update any profile."
  on public.profiles for update
  to authenticated
  using ( public.is_admin(auth.uid()) )
  with check ( public.is_admin(auth.uid()) ); -- Admins can update any profile, including roles/points

comment on policy "Admin users can update any profile." on public.profiles is 'Allows users with the admin role to update any profile, including role and points.';

-- Admin DELETE Policy
create policy "Admin users can delete any profile."
  on public.profiles for delete
  to authenticated
  using ( public.is_admin(auth.uid()) );

comment on policy "Admin users can delete any profile." on public.profiles is 'Allows users with the admin role to delete any profile.';


-- 6. Trigger function to prevent non-admins from updating role/points
create or replace function public.prevent_profile_field_update()
returns trigger
language plpgsql
security invoker -- Standard invoker security is sufficient
set search_path = ''
as $$
declare
  is_caller_admin boolean;
begin
  -- Check if the user performing the update is an admin
  select public.is_admin(auth.uid()) into is_caller_admin;

  -- If the user is NOT an admin AND they are trying to update their own profile
  if not is_caller_admin and OLD.id = (select auth.uid()) then
    -- Prevent updates to 'role' and 'total_points'
    if NEW.role is distinct from OLD.role then
      raise exception 'You do not have permission to change your role.';
    end if;
    if NEW.total_points is distinct from OLD.total_points then
      raise exception 'Total points cannot be updated directly.';
    end if;
     if NEW.id is distinct from OLD.id then
       raise exception 'User ID cannot be changed.';
     end if;
  elsif not is_caller_admin and OLD.id != (select auth.uid()) then
     -- If the user is not an admin and trying to update someone else's profile (RLS should prevent this anyway)
     raise exception 'You do not have permission to update this profile.';
  end if;

  -- Allow the update if the user is an admin or if the restricted fields are not being changed by a non-admin user
  return NEW;
end;
$$;

-- Trigger to enforce field update restrictions before update
create trigger check_profile_field_updates
  before update on public.profiles
  for each row
  execute function public.prevent_profile_field_update();

comment on function public.prevent_profile_field_update() is 'Prevents non-admin users from updating their own role, total_points, or id.';
comment on trigger check_profile_field_updates on public.profiles is 'Calls prevent_profile_field_update before update to enforce column restrictions.';


-- 7. Trigger function to handle new user creation
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer -- Needs elevated privileges to insert into public.profiles
set search_path = public
as $$
declare
  base_username text;
  final_username text;
  username_suffix integer := 0;
  user_email text;
begin
  -- Extract username part from email, fallback if email is null or doesn't contain '@'
  user_email := new.email;
  if user_email is null or position('@' in user_email) = 0 then
     -- Fallback: Generate a random base username if email is not usable
     base_username := 'user_' || substr(md5(random()::text), 1, 8);
  else
     base_username := lower(substring(user_email from '^[^@]+'));
     -- Sanitize: remove invalid characters (allow letters, numbers, underscore)
     base_username := regexp_replace(base_username, '[^a-z0-9_]+', '', 'g');
     -- Ensure minimum length
     if length(base_username) <= 3 then
       base_username := base_username || substr(md5(random()::text), 1, 4-length(base_username));
     end if;
  end if;

  -- Ensure username is unique
  final_username := base_username;
  while exists (select 1 from public.profiles where username = final_username) loop
    username_suffix := username_suffix + 1;
    final_username := base_username || '_' || username_suffix::text;
    -- Optional: Add a limit to prevent infinite loops in extreme cases
    if username_suffix > 1000 then
       final_username := 'user_' || new.id::text; -- Fallback to user ID based username
       if exists (select 1 from public.profiles where username = final_username) then
           raise exception 'Failed to generate a unique username for user %', new.id;
       end if;
       exit; -- Exit loop after fallback
    end if;
  end loop;

  -- Insert the new profile record
  insert into public.profiles (id, username, role)
  values (new.id, final_username, 'athlete'); -- Set default role to 'athlete'

  return new;
end;
$$;

-- Trigger to call handle_new_user on new user signup
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute function public.handle_new_user();

comment on function public.handle_new_user() is 'Automatically creates a profile entry with a generated unique username when a new user signs up in auth.users.';
comment on trigger on_auth_user_created on auth.users is 'Calls handle_new_user after a new user is inserted into auth.users.';


-- 8. Add indexes for performance
create index idx_profiles_username on public.profiles (username);
create index idx_profiles_role on public.profiles (role);
create index idx_profiles_total_points on public.profiles (total_points); -- For ranking

comment on index public.idx_profiles_username is 'Index on the username column for faster lookups.';
comment on index public.idx_profiles_role is 'Index on the role column for faster filtering by role (e.g., admin checks).';
comment on index public.idx_profiles_total_points is 'Index on total_points for efficient ranking queries.';
