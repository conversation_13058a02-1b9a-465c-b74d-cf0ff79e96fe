-- File: supabase/migrations/20250508103604_create_submission_and_update_profile_rpc.sql

/**
 * -----------------------------------------------------------------------------
 * Rpc function: create_submission_and_update_profile
 * -----------------------------------------------------------------------------
 * Description:
 *   Atomically creates a new submission in the `public.submissions` table and
 *   updates the `last_submission_at` timestamp in the `public.profiles` table
 *   for the given user.
 *
 * Parameters:
 *   - user_id_param: uuid - The ID of the user submitting the performance.
 *   - exercise_id_param: bigint - The ID of the exercise.
 *   - video_url_param: text - The URL of the video submission.
 *   - weight_lifted_param: numeric - The weight lifted in the performance.
 *   - notes_param: text - Optional notes for the submission.
 *
 * Returns:
 *   bigint - The ID of the newly created submission.
 *
 * Notes:
 *   - Assumes `public.submissions` table has an `id` column that is a bigint (e.g., identity).
 *   - Assumes `public.profiles` table has `id` (uuid) and `last_submission_at` (timestamptz).
 *   - Uses `SECURITY INVOKER` to run with the permissions of the calling user.
 *   - Sets `search_path` to empty to ensure explicit schema qualification.
 */
create or replace function public.create_submission_and_update_profile(
  user_id_param uuid,
  exercise_id_param bigint,
  video_url_param text,
  weight_lifted_param numeric,
  notes_param text
)
returns bigint
language plpgsql
security invoker
set search_path = ''
as $$
declare
  new_submission_id bigint;
begin
  -- insert into the submissions table
  insert into public.submissions (
    user_id,
    exercise_id,
    video_url,
    weight_lifted,
    notes,
    status, -- default 'pending'
    created_at,
    updated_at
  )
  values (
    user_id_param,
    exercise_id_param,
    video_url_param,
    weight_lifted_param,
    notes_param,
    'pending',
    now(),
    now()
  )
  returning id into new_submission_id;

  -- update the last_submission_at timestamp in the profiles table
  update public.profiles
  set last_submission_at = now()
  where id = user_id_param;

  return new_submission_id;
end;
$$;

comment on function public.create_submission_and_update_profile(uuid, bigint, text, numeric, text)
  is 'Atomically creates a submission and updates the user profile with the last submission time. Returns the new submission ID.'; 