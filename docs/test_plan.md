# Armwrestling Power Arena - Comprehensive Test Plan

## 1. Introduction

### 1.1 Purpose
This document outlines the comprehensive testing strategy for the Armwrestling Power Arena application. It consolidates insights from previous test plans, the technical specification (`arm_power_arena_specs_v2.md`), and the development roadmap (`todo.md`). The goal is to ensure the application is robust, secure, performant, accessible, and meets all functional requirements across different development stages.

### 1.2 Scope
This plan covers testing for:
- **Core Features:** Authentication, User Profiles, Exercise Library, Video Submissions, Evaluation, Ranking System, Admin Functions.
- **Non-Functional Requirements:** Security, Performance, Responsiveness, Accessibility, PWA Capabilities.
- **All Development Stages:** From foundational setup to future enhancements outlined in `todo.md`.

### 1.3 Key Principles Addressed
Testing activities will adhere to the mandatory key principles:
- **Test-Driven Development (TDD):** Writing tests before or alongside feature implementation.
- **Progressive Enhancement:** Testing core functionality first, then enhancements.
- **Responsive Design:** Testing across various screen sizes and devices.
- **Accessibility:** Incorporating accessibility checks (WCAG AA minimum).
- **Security:** Validating authentication, authorization (RLS), input sanitization, and Supabase security features.
- **Performance:** Measuring load times, query efficiency, and responsiveness.
- **Code Quality:** Ensuring tests contribute to maintainable and well-documented code.

## 2. Testing Strategy

### 2.1 Levels of Testing
- **Unit Tests (Vitest):** Isolate and test individual functions, hooks, utilities (e.g., `calculateAward`, validation schemas, `cn`). Focus on logic correctness and edge cases.
- **Component Tests (Vitest + RTL):** Test individual UI components in isolation. Verify rendering based on props, event handling, state changes, and accessibility attributes. Mock dependencies (hooks, child components, API calls).
- **Integration Tests (Vitest + RTL/Mocks):** Test the interaction between multiple units or components. Focus on Server Actions, Route Handlers, Middleware, Context providers, data fetching/mutation logic (mocking Supabase/external services), and interactions between components.
- **End-to-End (E2E) Tests (Playwright):** Simulate real user scenarios in a browser environment. Cover critical user flows across multiple pages (e.g., signup -> submit -> evaluate -> rank). Validate integration of frontend and backend.
- **Manual Testing:** Exploratory testing, usability testing, visual verification, testing complex scenarios not easily automated, and verifying PWA installation/offline behavior.
- **Security Testing:** Focused tests for RLS policies (SQL unit tests), input validation (Zod server-side), authorization checks in actions/middleware, dependency vulnerability scanning.
- **Performance Testing:** Use browser dev tools, Lighthouse audits, and potentially load testing tools (future) to measure page load times, bundle size (`@next/bundle-analyzer`), and database query performance.
- **Accessibility Testing:** Use browser extensions (e.g., Axe DevTools), Lighthouse audits, and manual checks (keyboard navigation, screen reader compatibility) to ensure WCAG AA compliance.

### 2.2 Test Environments
- **Local:** Development environment with local Supabase (`npx supabase start`) for running unit, component, and integration tests during development.
- **Staging:** A dedicated environment mirroring production (e.g., Vercel preview deployment) connected to a staging Supabase instance. Used for E2E tests and manual QA before production release.
- **Production:** Live environment. Minimal smoke testing post-deployment.

### 2.3 Automation Strategy
- **High Automation:** Unit, Integration, and Component tests should be heavily automated and run frequently (pre-commit hooks, CI).
- **Selective Automation:** E2E tests automated for critical user flows.
- **Manual Focus:** Usability, complex edge cases, visual regression (initially), and exploratory testing.

### 2.4 Tools & Frameworks
- **Testing Framework:** Vitest
- **Assertion Library:** Vitest `expect`, `@testing-library/jest-dom`
- **Component Testing:** React Testing Library (RTL)
- **E2E Testing:** Playwright
- **Mocking:** Vitest Mocks (`vi.mock`), MSW (optional, for complex API mocking)
- **Code Coverage:** Vitest built-in coverage (`coverage: { reporter: ['text', 'html'] }`)
- **CI/CD:** GitHub Actions (Linting, Testing, Building, Deployment)
- **Accessibility Auditing:** Axe DevTools, Lighthouse
- **Performance Auditing:** Lighthouse, `@next/bundle-analyzer`

### 2.5 Coverage Goals
- **Target:** ≥ 80% statement coverage, ≥ 70% branch coverage for Unit & Integration tests by the end of core feature development (Phase 5/6). Coverage reports generated in CI.

## 3. Test Execution Plan (Organized by Feature & Development Phase)

*(Note: Priorities: P0-Critical, P1-High, P2-Medium, P3-Low. Levels: U-Unit, C-Component, I-Integration, E2E-End-to-End, M-Manual, SEC-Security, PERF-Performance, ACC-Accessibility)*

### Phase 1: Project Setup & Foundational Infrastructure

| Feature/Task                 | Test Scenario                                                               | Level       | Priority | Status     |
|------------------------------|-----------------------------------------------------------------------------|-------------|----------|------------|
| **Next.js Project Init**     | Dev server starts, base page renders, linters pass                          | M           | P0       | Planned    |
| **ShadCN UI & Theme**        | Components render, theme toggle works, toaster present, base styles apply | C, M        | P1       | Done       |
|                              | Basic responsiveness check across breakpoints                               | M, ACC      | P1       | Planned    |
| **Supabase Client Setup**    | Client utils (`client.ts`, `server.ts`, `middleware.ts`) created correctly  | U           | P0       | Done       |
|                              | Utility functions read correct env vars, throw if missing                   | U           | P0       | Done       |
| **Routing, Layout, Nav**     | Routes accessible, nav links work, layout structure correct                 | C, I, E2E   | P1       | Done       |
|                              | Base loading/error states display correctly                                 | C           | P1       | Done       |
|                              | `Navbar` shows correct state (auth vs. unauth - mocked)                     | C           | P0       | Done       |
| **PWA Capabilities**         | Lighthouse PWA audit passes basic checks                                    | M, PERF     | P2       | Planned    |
|                              | Manifest/icons configured, install prompt hook setup                        | U, C, M     | P2       | Done       |
| **Core Utils (`utils.ts`)**  | `cn` function merges classes correctly                                      | U           | P0       | Done       |
|                              | Date/string formatting utils handle edge cases                              | U           | P1       | Planned    |

### Phase 2: Authentication & User Management

| Feature/Task                      | Test Scenario                                                                      | Level          | Priority | Status     |
|-----------------------------------|------------------------------------------------------------------------------------|----------------|----------|------------|
| **DB Schema (Profiles)**          | Migration applies, RLS policies functional (SQL tests), trigger works              | I, SEC, M      | P0       | Planned    |
| **Auth Forms (Sign Up/In)**       | Forms render, inputs accept valid/invalid data, validation works (Zod)             | U, C           | P0       | Planned    |
|                                   | Submit button enabled/disabled correctly, calls action on submit                   | C              | P0       | Planned    |
| **Auth Actions (`actions.ts`)**   | `signUp` success/failure (duplicate email/user), redirects                         | I              | P0       | Planned    |
|                                   | `signIn` success/failure (wrong credentials), redirects                            | I              | P0       | Planned    |
|                                   | `signOut` logs out user, clears session                                            | I              | P0       | Planned    |
| **Middleware (`middleware.ts`)**  | Protects routes (`/account`, `/admin/*`) - redirects unauthenticated               | I, SEC         | P0       | Planned    |
|                                   | Allows authenticated users access                                                  | I              | P0       | Planned    |
|                                   | Refreshes session correctly using `@supabase/ssr` helpers                        | I              | P0       | Planned    |
| **Auth Callback (`/auth/callback`)**| Code exchange works, redirects user correctly                                      | I, E2E         | P1       | Planned    |
| **Social Auth (Planned)**         | Verify flows for configured providers (Google, etc.)                               | E2E, M         | P2       | Planned    |
| **Account Page (`/account`)**     | Displays logged-in user's profile data, protected route                            | I, C           | P1       | Planned    |
| **Public Profile (`/profile/[username]`)** | Displays public data for valid username, `notFound()` for invalid             | I, C           | P1       | Planned    |
| **RBAC**                          | Middleware blocks non-admins from `/admin`, `Navbar` hides admin links             | I, C, SEC      | P0       | Planned    |
|                                   | Role checks within Server Actions prevent unauthorized actions                     | I, SEC         | P0       | Planned    |
| **Profile Editing (`/account/edit`)** | Form pre-fills, updates fields, validates input (Zod)                          | C, I           | P1       | Planned    |
|                                   | Avatar upload works (Storage RLS policies checked)                                 | I, E2E, SEC, M | P1       | Planned    |
|                                   | `updateProfile` action updates DB, handles username uniqueness                     | I              | P1       | Planned    |
| **E2E Auth Flow**                 | Register -> Verify -> Login -> Logout                                              | E2E            | P0       | Planned    |

### Phase 3: Exercise Library

| Feature/Task                       | Test Scenario                                                                      | Level       | Priority | Status     |
|------------------------------------|------------------------------------------------------------------------------------|-------------|----------|------------|
| **DB Schema (Exercises)**          | Migration applies, RLS policies functional (public read, admin manage)             | I, SEC, M   | P0       | Planned    |
| **Admin Exercise Mgmt (`/admin/exercises`)** | Admin access only, list displays exercises                                         | I, C, SEC   | P1       | Planned    |
|                                    | Create/Edit modal form validation works, calls actions                             | C, I        | P1       | Planned    |
|                                    | `create/update/deleteExercise` actions work (admin check, DB changes)              | I, SEC      | P1       | Planned    |
|                                    | Delete confirmation dialog works                                                   | C           | P1       | Planned    |
| **Public Listing (`/exercises`)**  | List renders correctly, filters/search update list (URL params)                  | C, I        | P1       | Planned    |
|                                    | Pagination works correctly                                                         | C, I        | P1       | Planned    |
| **Detail View (`/exercises/[id]`)**| Displays correct details, video player, criteria, thresholds                       | I, C        | P1       | Planned    |
|                                    | `notFound()` for invalid ID                                                        | I           | P1       | Planned    |
|                                    | "Submit Performance" button visible only to authenticated users, navigates correctly | C, I        | P1       | Planned    |
| **E2E Admin Exercise Flow**        | Admin Login -> Create Exercise -> Verify Public List -> Edit -> Delete             | E2E         | P1       | Planned    |

### Phase 4: Video Submission System

| Feature/Task                        | Test Scenario                                                                       | Level       | Priority | Status     |
|-------------------------------------|-------------------------------------------------------------------------------------|-------------|----------|------------|
| **Submission Form (`/submit`)**     | Form renders, protected route, pre-fills exercise from params                     | C, I        | P1       | Planned    |
|                                     | Validation works (Video URL format, Weight numeric/positive) (Zod)                | U, C        | P0       | Planned    |
|                                     | Submit button calls `submitPerformance` action                                      | C           | P1       | Planned    |
| **DB Schema (Submissions)**         | Migration applies, RLS policies functional (athlete CRUD own, GM/Admin eval)        | I, SEC, M   | P0       | Planned    |
| **Rate Limiting (`profiles.last_submission_at`)** | Field exists and is updated                                                     | I           | P0       | Planned    |
| **Submit Action (`submitPerformance`)** | Auth check, Rate limit check (blocks/allows non-admins, admin bypass)             | I, SEC      | P0       | Planned    |
|                                     | Server-side URL validation, DB insert works (RPC or direct)                       | I           | P0       | Planned    |
|                                     | Updates `last_submission_at` on success                                           | I           | P0       | Planned    |
|                                     | Handles errors gracefully, revalidates/redirects                                  | I           | P1       | Planned    |
| **History View (`/account`)**       | Displays user's submission history correctly (status, medal, feedback)              | I, C        | P1       | Planned    |
| **E2E Submission Flow**             | Athlete Login -> View Exercise -> Submit Form (Valid/Invalid) -> Check History      | E2E         | P0       | Planned    |
|                                     | Test rate limiting block/allow over 24h period                                    | E2E, M      | P0       | Planned    |
| **DB Seeding**               | `seed.sql` applies, `db:seed` script runs & creates data idempotently       | I, M        | P1       | Planned    |

### Phase 5: Evaluation & Medal System

| Feature/Task                         | Test Scenario                                                                            | Level       | Priority | Status     |
|--------------------------------------|------------------------------------------------------------------------------------------|-------------|----------|------------|
| **Evaluation UI (`/admin/evaluate`)**| GM/Admin access only, queue displays pending submissions                                   | C, I, SEC   | P1       | Planned    |
|                                      | Evaluation modal displays details, form inputs work                                        | C           | P1       | Planned    |
| **Award Logic (`calculateAward`)**   | Returns correct medal/points for all gender/weight categories & weight thresholds (edges) | U           | P0       | Planned    |
| **DB Logic (`increment_user_points`)** | RPC function exists and updates points atomically                                        | I, M        | P0       | Planned    |
| **Evaluate Action (`evaluateSubmission`)** | Role check (GM/Admin), fetches data correctly                                        | I, SEC      | P0       | Planned    |
|                                      | Approval: Calls `calculateAward`, updates submission, calls points RPC                   | I           | P0       | Planned    |
|                                      | Rejection: Updates submission status/comments, no points                                 | I           | P0       | Planned    |
|                                      | Saves `private_comments`, `evaluated_at`, `evaluated_by`                                 | I           | P1       | Planned    |
|                                      | Handles errors, revalidates relevant pages                                               | I           | P1       | Planned    |
| **Notification System (Schema)**     | `notifications` table migration, RLS policies work                                       | I, SEC, M   | P1       | Planned    |
| **Notification System (Triggers)**   | DB triggers (`notify_evaluators_on_new_submission`, `notify_athlete_on_evaluation`) work | I, M        | P1       | Planned    |
| **Notification System (Realtime)**   | Realtime enabled, `NotificationListener` subscribes & receives updates                 | I, C, E2E   | P2       | Planned    |
|                                      | `NotificationBell` displays count, list, triggers mark read action                       | C, I        | P2       | Planned    |
| **Achievements Display (`/profile`)**| `get_user_medal_counts` RPC works                                                        | I, M        | P1       | Planned    |
|                                      | Profile displays correct `total_points` and `MedalsGallery`                                | C, I        | P1       | Planned    |
| **E2E Evaluation Flow**              | GM Login -> Evaluate Pending Submission (Approve/Reject) -> Check Athlete History/Points/Notification | E2E         | P0       | Planned    |

### Phase 6: Ranking System

| Feature/Task                        | Test Scenario                                                                     | Level       | Priority | Status     |
|-------------------------------------|-----------------------------------------------------------------------------------|-------------|----------|------------|
| **DB View (`ranked_users`)**        | View exists, returns correct ranks (`DENSE_RANK`), points, medals, handles ties | I, M        | P0       | Planned    |
| **Rankings Page (`/rankings`)**     | Displays leaderboard table correctly (rank, user, points, medals), public access  | C, I        | P1       | Planned    |
|                                     | Fetches from `ranked_users` view, handles pagination                              | I           | P1       | Planned    |
|                                     | Current logged-in user row is highlighted                                         | C, I        | P2       | Planned    |
| **Ranking Filters (`/rankings`)**   | Filter controls update URL params, server fetch filters view correctly            | C, I        | P1       | Planned    |
|                                     | Combined filters work, clearing filters works                                     | C, I        | P1       | Planned    |
| **Profile Rank Display (`/profile`)** | Profile page fetches & displays user's rank from `ranked_users` view            | I, C        | P1       | Planned    |
|                                     | Displays "Unranked" if user not in view                                           | I, C        | P1       | Planned    |
| **E2E Ranking Flow**                | View Rankings -> Apply Filters -> Verify Results -> Check Profile Rank              | E2E         | P1       | Planned    |

### Phase 7: Admin Features

| Feature/Task                    | Test Scenario                                                                      | Level       | Priority | Status     |
|---------------------------------|------------------------------------------------------------------------------------|-------------|----------|------------|
| **User Mgmt (`/admin/users`)**  | Admin access only, displays user list, filters/search work                         | C, I, SEC   | P1       | Planned    |
|                                 | `EditUserRole` component allows role change, calls action                          | C, I        | P1       | Planned    |
|                                 | `updateUserRole` action works (admin check, self-check prevents, DB update)      | I, SEC      | P1       | Planned    |
|                                 | (Future) User blocking functionality                                               | C, I, E2E   | P2       | Planned    |
| **Other Admin (Future)**        | Placeholder tests for Content Moderation, Settings, Analytics                      | U, C, I     | P3       | Planned    |
| **E2E Admin User Mgmt Flow**    | Admin Login -> View Users -> Filter -> Change Role -> Verify Change                  | E2E         | P1       | Planned    |

### Phase 8: Testing Refinement & Deployment

| Feature/Task                     | Test Scenario                                                                          | Level          | Priority | Status     |
|----------------------------------|----------------------------------------------------------------------------------------|----------------|----------|------------|
| **Test Coverage Improvement**    | Review coverage reports, add tests for gaps in Units/Integrations/Components           | U, C, I        | P1       | Planned    |
| **E2E Test Suite Implementation**| Setup Playwright config, auth strategy, implement core flow specs                       | E2E            | P0       | Planned    |
| **CI/CD Pipeline Setup**         | Configure GH Actions for lint, test (unit/int/comp), build, coverage check, deployment | M              | P0       | Planned    |
|                                  | (Future) Add E2E test execution step to CI                                             | M              | P2       | Planned    |
| **Performance Optimization**     | Run Lighthouse audits, analyze bundles, check query performance, optimize images       | PERF, M        | P2       | Planned    |
| **Accessibility Refinement**     | Run Axe DevTools scans, perform manual keyboard/screen reader checks                   | ACC, M         | P1       | Planned    |
| **Deployment Checks**            | Run migrations on prod DB, verify deployment, perform post-deployment smoke tests      | M, E2E (smoke) | P0       | Planned    |
| **Security Hardening**           | Review RLS, input validation, dependencies; potentially run automated scans           | SEC, M         | P1       | Planned    |
| **Responsiveness Checks**        | Manual testing across target device sizes/browsers                                     | M              | P1       | Planned    |
| **PWA Advanced Checks**          | Test installation flow on different devices, verify offline functionality              | M, E2E         | P2       | Planned    |

## 4. Test Data Requirements

- **Users:**
    - Multiple `athlete` role users (different genders, weight categories, countries, varying points/medals).
    - At least one `grandmaster` role user.
    - At least one `admin` role user.
    - Blocked user (future).
    - User with social login (future).
- **Exercises:**
    - Multiple exercises with varying details (equipment, criteria).
    - Exercises with fully defined `medal_thresholds` covering all categories.
- **Submissions:**
    - Submissions in `pending`, `approved`, `rejected` states.
    - Submissions linked to different users and exercises.
    - Submissions with varying `weight_lifted` values to test medal boundaries.
    - Submissions older/newer than 24 hours for rate limiting tests.
- **Notifications:**
    - Read and unread notifications for different users.

*Test data should be generated via the `seed.sql` and `scripts/seed.ts` scripts and easily reset (`npx supabase db reset`).*

## 5. Defect Management

- **Tracking:** GitHub Issues will be used to track defects found during testing.
- **Reporting:** Issues should include:
    - Descriptive Title
    - Steps to Reproduce
    - Expected Result
    - Actual Result
    - Environment (Local, Staging, Browser/OS)
    - Screenshots/Videos (if applicable)
    - Severity (Critical, High, Medium, Low)
    - Priority
- **Lifecycle:** New -> Assigned -> In Progress -> Resolved -> Verified -> Closed.

## 6. Risks & Mitigation

| Risk                             | Mitigation Strategy                                                                 |
|----------------------------------|-------------------------------------------------------------------------------------|
| Incomplete Core Feature Coverage | Prioritize P0/P1 tests covering critical paths (Auth, Submit, Evaluate, Rank).      |
| Security Vulnerabilities         | Implement SEC tests (RLS, Input Validation, Auth checks), dependency scanning, review. |
| Performance Bottlenecks          | Integrate PERF testing early, optimize queries/bundles based on findings.           |
| Poor Accessibility               | Integrate ACC testing throughout development, use automated tools and manual checks.  |
| Data Integrity Issues            | Thoroughly test award calculation, point updates (RPC), ranking logic; use DB constraints. |
| Regression Bugs                  | Maintain high test coverage, run tests frequently in CI, focus E2E on critical flows. |
| Environment Differences          | Use Staging environment for E2E/QA that closely mirrors Production.                 |
| Testing Time Constraints         | Prioritize tests based on risk/impact, automate repetitive checks (Unit, Int, Comp). |
| PWA/Offline Complexity           | Isolate PWA logic where possible, use browser dev tools and manual testing.         |
| Social Auth Integration          | Use mock providers or dedicated test accounts for E2E testing.                      |

## 7. Conclusion

This comprehensive test plan provides a roadmap for ensuring the quality, reliability, and security of the Armwrestling Power Arena application. By following this plan, integrating testing throughout the development lifecycle, and adapting as needed, we can deliver a high-quality product that meets user expectations and business requirements.

## Appendix A: Migration Test Plans

### Migration: 20250429202300_create_exercises.sql

#### 1. Testing Approach

The validation will primarily involve manual testing using `psql` connected to the local development database after applying the migration via `supabase db reset`. We will simulate different user roles (`anon`, `authenticated` non-admin, `authenticated` admin) using `SET ROLE` and `SET request.jwt.claims` commands in `psql` to verify the RLS policies. We will also inspect the schema directly using `psql` meta-commands. Finally, we'll verify the migration runs successfully during `supabase db reset` and doesn't interfere with potential seed data loading.

#### 2. Testing Checklist

**Schema Verification:**

*   [ ] Verify `public.exercises` table exists (`\dt public.exercises`).
*   [ ] Verify all columns exist with correct types, nullability, and defaults (`\d+ public.exercises`).
    *   [ ] `id`: `bigint`, `not null`, `generated always as identity`
    *   [ ] `title`: `text`, `not null`
    *   [ ] `description`: `text`, `null`
    *   [ ] `video_tutorial_url`: `text`, `null`
    *   [ ] `equipment_required`: `text[]`, `null`
    *   [ ] `evaluation_criteria`: `text`, `not null`
    *   [ ] `medal_thresholds`: `jsonb`, `not null`
    *   [ ] `created_by`: `uuid`, `null`, Foreign key to `public.profiles(id)` `ON DELETE SET NULL`
    *   [ ] `created_at`: `timestamp with time zone`, `not null`, `default timezone('utc'::text, now())`
    *   [ ] `updated_at`: `timestamp with time zone`, `not null`, `default timezone('utc'::text, now())`
*   [ ] Verify table comment is present and correct (`\d+ public.exercises`).
*   [ ] Verify all column comments are present and correct (`\d+ public.exercises`).
*   [ ] Verify `idx_exercises_created_by` index exists on `created_by` (`\di public.exercises*`).
*   [ ] Verify `exercises_title_idx` index exists on `title` (`\di public.exercises*`).

**RLS Verification:**

*   [ ] Verify RLS is enabled on `public.exercises` (`\d+ public.exercises` - check "Row security: enabled").
*   [ ] Verify `"Allow public read access"` policy exists and is correct (Check `pg_policies` table or use a GUI tool).
*   [ ] Verify `"Allow admin insert access"` policy exists and is correct.
*   [ ] Verify `"Allow admin update access"` policy exists and is correct.
*   [ ] Verify `"Allow admin delete access"` policy exists and is correct.

**Data Integrity & Seeding:**

*   [ ] Verify `NOT NULL` constraints are enforced on relevant columns (attempt `INSERT` with nulls).
*   [ ] Verify `supabase db reset` completes without errors related to this migration.
*   [ ] Verify seed data (if applicable for `public.exercises` in `supabase/seed.sql`) is loaded correctly after `db reset`.

#### 3. RLS Policy Test Cases

Use `psql` and the following commands to simulate roles. Replace `"..."` with a valid UUID for testing.

```sql
-- Reset role before each test set
reset role;

-- Test Case 1: Anonymous User
set role anon;
select * from public.exercises limit 1; -- EXPECTED: Success (or empty result if no data)
insert into public.exercises (title, evaluation_criteria, medal_thresholds) values ('Test', 'Test', '{}'); -- EXPECTED: Fails (permission denied)
update public.exercises set description = 'Test' where id = 1; -- EXPECTED: Fails (permission denied, or 0 rows updated if no ID 1)
delete from public.exercises where id = 1; -- EXPECTED: Fails (permission denied, or 0 rows deleted if no ID 1)
reset role;

-- Test Case 2: Authenticated User (Non-Admin)
set role authenticated;
set request.jwt.claims = '{"role": "authenticated", "sub": "...", "app_metadata": {"is_admin": false}}';
select * from public.exercises limit 1; -- EXPECTED: Success
insert into public.exercises (title, evaluation_criteria, medal_thresholds, created_by) values ('Test Auth', 'Test', '{}', '...'); -- EXPECTED: Fails (permission denied)
update public.exercises set description = 'Test Auth' where id = 1; -- EXPECTED: Fails (permission denied)
delete from public.exercises where id = 1; -- EXPECTED: Fails (permission denied)
reset role;

-- Test Case 3: Authenticated User (Admin)
set role authenticated;
set request.jwt.claims = '{"role": "authenticated", "sub": "...", "app_metadata": {"is_admin": true}}';
-- Attempt INSERT
insert into public.exercises (title, evaluation_criteria, medal_thresholds, created_by) values ('Test Admin Insert', 'Test', '{"cat": {"gold": 1}}', '...') returning id; -- EXPECTED: Success, returns new ID
-- Assume the above returned id = 1 for subsequent tests
-- Attempt SELECT (already covered by public policy, but good to confirm)
select * from public.exercises where id = 1; -- EXPECTED: Success, returns inserted row
-- Attempt UPDATE
update public.exercises set description = 'Test Admin Update' where id = 1; -- EXPECTED: Success (1 row updated)
select description from public.exercises where id = 1; -- EXPECTED: Success, shows 'Test Admin Update'
-- Attempt DELETE
delete from public.exercises where id = 1; -- EXPECTED: Success (1 row deleted)
select * from public.exercises where id = 1; -- EXPECTED: Success (0 rows returned)
reset role;

```

#### 4. Validating Seed Data with `db reset`

To validate that the migration works correctly with the seeding process during `supabase db reset`:

1.  **Ensure Seed Script Exists (Optional but Recommended):** If you intend to have default exercises, add `INSERT` statements for `public.exercises` into your `supabase/seed.sql` file. Make sure these inserts comply with the table constraints.
2.  **Run Reset:** Execute `supabase stop --no-backup` (if running) followed by `supabase db reset` in your terminal.
3.  **Check Output:** Carefully observe the terminal output from `supabase db reset`. Look for any errors, especially ones mentioning `20250429202300_create_exercises.sql` or issues during the seeding phase (`Executing supabase/seed.sql...`).
4.  **Connect and Verify:** After the reset completes successfully, connect to the database using `psql` (`supabase db psql`).
5.  **Query the Table:** Run `SELECT COUNT(*) FROM public.exercises;`.
    *   If you added seed data for this table, verify the count matches the number of records you intended to seed.
    *   If you *did not* add seed data for this table, verify the count is `0`.
    *   Perform a `SELECT * FROM public.exercises LIMIT 5;` to spot-check the actual data if seeding occurred.
