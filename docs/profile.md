# User Profile

...

## Profile Completion Prompt

On the `/account` page, if any required profile fields (`full_name`, `country`, `gender`, `weight_category`) are missing, a prominent completion prompt is displayed. This encourages users to complete their profile to unlock all features and improve their public profile visibility.

- The prompt appears as a ShadCN alert card at the top of the account page.
- Users can click the "Edit Account" link to fill in missing information.
- Required fields are enforced for a complete profile experience.

...

## Profile Editing Flow

- The `/account/edit` page allows authenticated users to update their profile, including avatar, username, country, gender, weight category, titles, and social links.
- The form is pre-filled with the user's current data and uses robust client-side validation.
- Avatar upload is handled via Supabase Storage (`avatars` bucket, path `public/{user_id}`), with real-time progress and error feedback.
- On successful update, the user is redirected to `/account` and changes are reflected immediately.

### Data Flow Diagram

```mermaid
sequenceDiagram
  participant U as User (client)
  participant C as EditProfileForm (client)
  participant S as Next.js Server Action
  participant DB as Supabase DB
  participant ST as Supabase Storage

  U->>C: open /account/edit
  C->>S: (implicit) server fetched profile
  Note right of C: initialValues hydrated

  U->>C: change fields & select avatar
  C->>ST: upload avatar file (PUT)
  ST-->>C: public URL

  U->>C: submit form
  C->>S: updateProfile(data)
  S->>DB: validate & update row
  DB-->>S: success
  S->>C: redirect /account
  C->>U: Success toast
```

- All updates are subject to Row Level Security (RLS) policies.
- Username changes are checked for uniqueness.
- Only permitted fields are updated; titles are stored as an array, social_links as JSON.
