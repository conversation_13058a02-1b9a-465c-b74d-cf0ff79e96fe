# Routing Overview

...

## User Account & Profile Routes

| Route                 | Access    | Description                                                                                                                                                                                             |
| --------------------- | --------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `/account`            | Protected | Private account page for the logged-in user. Shows settings, overview, and prompts for profile completion if needed.                                                                                    |
| `/account/edit`       | Protected | Edit account/profile details.                                                                                                                                                                           |
| `/profile/[username]` | Public    | Public profile page for any user, accessible by username (case-insensitive). Shows avatar, username, full name, country, gender, weight category, and titles. No sensitive info (e.g., email) is shown. |
| `/profile`            | Public    | Redirects authenticated users to `/account`, otherwise shows sign-in prompt.                                                                                                                            |
| `/profile/[id]`       | Public    | Legacy: Redirects to `/profile/[username]` if found, otherwise shows not found.                                                                                                                         |

- The `/account` page is protected by middleware and only accessible to authenticated users.
- The `/profile/[username]` page is public and accessible to anyone.
- Profile completion prompt appears on `/account` if required fields are missing.

...
