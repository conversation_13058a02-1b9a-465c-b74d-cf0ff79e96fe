# Armwrestling Power Arena - Features Documentation

This document provides an overview of the key features and functionality of the Armwrestling Power Arena application.

## Authentication System

The application uses Supabase Authentication with the following features:

- Email/password authentication
- Social login (Google, Facebook)
- Protected routes via middleware
- Role-based access control (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
- User profile management

## User Profiles

Users can manage their profiles with the following capabilities:

- Public profile page at `/profile/[username]`
- Private account management at `/account`
- Profile editing at `/account/edit`
- Avatar upload and management
- Display of achievements, medals, and points

## Exercise Library

The exercise library provides a comprehensive collection of armwrestling exercises:

### Exercise Listing (`/exercises`)

- Public page displaying all available exercises
- Card-based grid layout with exercise thumbnails
- Filtering and search capabilities
- Pagination for browsing large collections

### Exercise Detail View (`/exercises/[id]`)

- Detailed view of a single exercise
- Server-side data fetching with proper error handling
- Video tutorial display using the `VideoPlayer` component
  - Support for YouTube embeds
  - Fallback handling for TikTok and Instagram links
  - Error states for invalid URLs
- Exercise information display:
  - Title and description
  - Equipment required (displayed as a list)
  - Evaluation criteria
  - Medal thresholds (displayed in a table format by category)
- "Submit Performance" button for authenticated users
  - Only visible to logged-in users
  - Navigates to the submission form with pre-filled exercise information

## Video Submission System

Athletes can submit videos of their performances:

- Submission form at `/submit`
- Video URL validation
- Weight and notes fields
- Submission history view

## Evaluation System

Grandmasters and Admins can evaluate submissions:

- Pending submissions queue
- Evaluation form with medal awarding
- Points calculation based on performance

## Ranking System

The application includes a global ranking system:

- Rankings page at `/rankings`
- Sorting by points and medals
- Filtering options
- Profile links for each ranked athlete

## Admin Features

Admin-specific features include:

- User management
- Exercise management (CRUD operations)
- Site configuration

## PWA Capabilities

The application is installable as a Progressive Web App:

- Offline support
- Home screen installation
- Responsive design for all device sizes
