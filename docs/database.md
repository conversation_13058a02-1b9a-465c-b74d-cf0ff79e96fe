# Database Documentation

This document outlines the structure and policies of the database tables.

## Table: `public.exercises`

Stores details about various exercises, including descriptions, video links, equipment, evaluation methods, medal criteria, and thresholds.

| Column              | Type                     | Nullable | Default Value                     | Comment                                                                                                                                                                                                                                                                                       |
| :------------------ | :----------------------- | :------- | :-------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                | bigint                   | NOT NULL | `generated always as identity`    | Unique identifier for the exercise.                                                                                                                                                                                                                                                           |
| `title`             | text                     | NOT NULL |                                   | The name or title of the exercise.                                                                                                                                                                                                                                                            |
| `description`       | text                     | YES      |                                   | A detailed description of how to perform the exercise.                                                                                                                                                                                                                                        |
| `video_tutorial_url`| text                     | YES      |                                   | URL link to a video demonstrating the exercise.                                                                                                                                                                                                                                               |
| `equipment_required`| text[]                   | YES      |                                   | List of equipment needed for the exercise (e.g., `{\"dumbbell\", \"bench\"}`).                                                                                                                                                                                                                     |
| `evaluation_criteria`| text                    | NOT NULL |                                   | Describes how the exercise performance is measured (e.g., "Max reps in 1 minute", "Time to complete 50 reps").                                                                                                                                                                               |
| `medal_thresholds`  | jsonb                    | NOT NULL |                                   | JSON object defining the scores/reps required for different medals based on categories. Structure: `{\"women_under_95\": {\"bronze\": 5, \"silver\": 10, \"gold\": 15, \"platinum\": 20}, \"men_over_95\": {\"bronze\": 8, \"silver\": 15, \"gold\": 22, \"platinum\": 30}}` etc. Keys represent categories (e.g., gender_weight), values are objects mapping medal names to required scores/reps. |
| `created_by`        | uuid                     | YES      |                                   | Identifier of the user (from `public.profiles`) who created the exercise record. Null if the user is deleted. References `public.profiles(id)` ON DELETE SET NULL.                                                                                                                               |
| `created_at`        | timestamptz              | NOT NULL | `timezone('utc'::text, now())`    | Timestamp when the exercise record was created.                                                                                                                                                                                                                                               |
| `updated_at`        | timestamptz              | NOT NULL | `timezone('utc'::text, now())`    | Timestamp when the exercise record was last updated.                                                                                                                                                                                                                                          |

**Indexes:**
*   `idx_exercises_created_by` on (`created_by`)
*   `exercises_title_idx` on (`title`)

## RLS Policies: `public.exercises`

Row Level Security is enabled for the `public.exercises` table.

*   **SELECT:**
    *   Policy: `"Allow public read access"`
    *   Allowed Roles: `authenticated`, `anon`
    *   Condition: `true` (Anyone can read any exercise record).
*   **INSERT:**
    *   Policy: `"Allow admin insert access"`
    *   Allowed Roles: `authenticated`
    *   Condition: `(auth.jwt() ->> 'app_metadata' ->> 'is_admin' = 'true')` (Only authenticated users with the `is_admin` custom claim set to `true` can insert new exercises).
*   **UPDATE:**
    *   Policy: `"Allow admin update access"`
    *   Allowed Roles: `authenticated`
    *   Condition: `(auth.jwt() ->> 'app_metadata' ->> 'is_admin' = 'true')` (Only authenticated users with the `is_admin` custom claim set to `true` can update existing exercises).
*   **DELETE:**
    *   Policy: `"Allow admin delete access"`
    *   Allowed Roles: `authenticated`
    *   Condition: `(auth.jwt() ->> 'app_metadata' ->> 'is_admin' = 'true')` (Only authenticated users with the `is_admin` custom claim set to `true` can delete exercises). 