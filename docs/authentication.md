# Authentication System Documentation

*Single Source of Truth for Authentication in Arm Power Arena*

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Authentication Flow](#authentication-flow)
4. [Components Reference](#components-reference)
5. [Supabase SSR Best Practices](#supabase-ssr-best-practices)
6. [Security Implementation](#security-implementation)
7. [Role-Based Access Control](#role-based-access-control)
8. [Development Guidelines](#development-guidelines)
9. [Common Patterns](#common-patterns)
10. [Troubleshooting](#troubleshooting)
11. [Migration Notes](#migration-notes)

---

## Overview

The Arm Power Arena authentication system provides secure user authentication with role-based access control (RBAC). The system has been architected as a **unified, single-source-of-truth** solution that eliminates the complexity and security vulnerabilities that existed in previous iterations.

### Key Features

- **Email/Password Authentication** with secure validation
- **OAuth Social Login** (Google, Facebook, Apple)
- **Role-Based Access Control** (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
- **Server-Side Rendering (SSR) Safe** with Supabase
- **Type-Safe** throughout the entire stack
- **Security-First** design with CSRF and XSS protection

### Architecture Philosophy

The authentication system follows **Feature Sliced Design** principles with a clear separation of concerns:

- **Single Provider**: One unified AuthContext eliminates conflicts
- **Type Safety**: Unified types prevent runtime errors
- **Security by Default**: Open Redirect and CSRF protection built-in
- **Developer Experience**: Consistent APIs across all components

---

## Architecture

### Directory Structure

```
src/
├── shared/
│   ├── types/auth.ts                 # ✅ Unified authentication types
│   └── libs/auth/
│       ├── context.tsx               # ✅ Main AuthContext provider
│       ├── validation.ts             # ✅ Unified validation & error handling
│       └── index.ts                  # ✅ Main exports
├── features/auth/
│   ├── hooks/use-auth.ts             # ✅ Convenient hook for components
│   └── components/
│       ├── AuthDialog.tsx            # Authentication modal
│       ├── UserMenu.tsx              # User dropdown menu
│       └── AuthGuard.tsx             # Conditional rendering guard
├── libs/auth/
│   ├── serverPermissions.ts          # Server-side auth utilities
│   ├── protectedApi.ts               # API route protection
│   └── roleGuard.tsx                 # Role-based route guards
└── app/auth/
    ├── actions.ts                    # Server actions for auth
    └── callback/route.ts             # OAuth callback handler
```

### Data Architecture

The authentication system combines data from two sources:

- **`auth.users`** (Supabase managed): `id`, `email`, authentication data
- **`public.profiles`** (Application managed): `username`, `role`, profile data

The unified `UserProfile` interface combines both sources into a single, type-safe object.

---

## Authentication Flow

### 1. User Registration Flow

```
User → Client → Server → Database → Email
 ↓       ↓        ↓        ↓         ↓
Form → Validate → Action → SignUp → Confirm
 ↓       ↓        ↓        ↓         ↓
UI ← Response ← Success ← Profile ← Click
```

### 2. User Login Flow

```
User → Client → Server → Database
 ↓       ↓        ↓        ↓
Creds → Validate → Action → SignIn
 ↓       ↓        ↓        ↓
UI ← Redirect ← Success ← Session
```

### 3. OAuth Flow

```
User → Provider → Callback → Server → Database
 ↓       ↓          ↓         ↓        ↓
Click → Consent → Code → Exchange → Profile
 ↓       ↓          ↓         ↓        ↓
UI ← Redirect ← Validate ← Token ← Create
```

---

## Components Reference

### Core Authentication Hook

```typescript
import { useAuth } from '@/features/auth/hooks/use-auth'

function MyComponent() {
  const { 
    user,              // Supabase User object
    profile,           // Combined user profile data
    userRole,          // User's role (athlete|grandmaster|admin)
    loading,           // Auth loading state
    isAuthenticated,   // Boolean auth status
    signInWithEmail,   // Email/password sign in
    signUpWithEmail,   // Email/password sign up
    signInWithSocial,  // OAuth sign in
    signOut,           // Sign out method
    updateProfile,     // Update profile data
    refreshProfile     // Refresh profile from DB
  } = useAuth()
}
```

### Authentication Components

#### AuthDialog
Modal component for sign in/sign up with email or OAuth.

```typescript
<AuthDialog
  open={isOpen}
  onOpenChange={setIsOpen}
  initialMode="signin" // or "signup"
/>
```

#### AuthGuard
Conditionally renders content based on authentication or permissions.

```typescript
<AuthGuard requireAuth={true} requiredPermission="admin.access">
  <AdminPanel />
</AuthGuard>
```

#### ProtectedRoute
Protects entire routes with authentication and permission requirements.

```typescript
<ProtectedRoute 
  requiredPermissions={['exercises:create']}
  fallback={<AccessDenied />}
>
  <CreateExercise />
</ProtectedRoute>
```

#### UserMenu
Dropdown menu showing user info and navigation options.

```typescript
<UserMenu /> // Automatically handles authenticated state
```

### Server-Side Components

#### Server Actions
React 19 server actions for authentication operations.

```typescript
// In your form component
<form action={signInAction}>
  <input name="email" type="email" required />
  <input name="password" type="password" required />
  <button type="submit">Sign In</button>
</form>
```

#### Protected API Routes

```typescript
import { createProtectedApiRoute } from '@/libs/auth/protectedApi'

export const POST = createProtectedApiRoute(
  async (request, user) => {
    // user is guaranteed to be authenticated
    return NextResponse.json({ message: 'Success' })
  },
  { permissions: ['content:create'] }
)
```

#### Server-Side Permission Checks

```typescript
import { requireAdmin, getCurrentUser } from '@/libs/auth/serverPermissions'

export default async function AdminPage() {
  const user = await requireAdmin() // Redirects if not admin
  
  return <AdminDashboard user={user} />
}
```

---

## Supabase SSR Best Practices

### Critical Rules (MUST FOLLOW)

These rules are **mandatory** for proper SSR functionality and security:

#### 1. Cookie Handling
```typescript
// ✅ CORRECT - Use only getAll/setAll
const supabase = createServerClient(url, key, {
  cookies: {
    getAll() {
      return cookieStore.getAll()
    },
    setAll(cookiesToSet) {
      cookiesToSet.forEach(({ name, value, options }) =>
        cookieStore.set(name, value, options)
      )
    }
  }
})

// ❌ WRONG - Never use get/set/remove
const supabase = createServerClient(url, key, {
  cookies: {
    get: (name) => cookieStore.get(name),  // DON'T DO THIS
    set: (name, value) => cookieStore.set(name, value),  // DON'T DO THIS
    remove: (name) => cookieStore.delete(name)  // DON'T DO THIS
  }
})
```

#### 2. Import Sources
```typescript
// ✅ CORRECT - Use @supabase/ssr
import { createServerClient, createBrowserClient } from '@supabase/ssr'

// ❌ WRONG - Never use auth-helpers
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
```

#### 3. Server Client Pattern
```typescript
// ✅ CORRECT - Server component pattern
export async function createClient() {
  const cookieStore = await cookies()
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // Ignore errors in Server Components
          }
        },
      },
    }
  )
}
```

#### 4. Middleware Pattern
```typescript
// ✅ CORRECT - Middleware client pattern
export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({ request })
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          )
          supabaseResponse = NextResponse.next({ request })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )
  
  // ALWAYS call getUser() after client creation
  const { data: { user } } = await supabase.auth.getUser()
  
  return supabaseResponse
}
```

---

## Security Implementation

### Security Measures Implemented

#### 1. Open Redirect Protection
```typescript
// Secure URL sanitization prevents redirects to external domains
function sanitizeRedirectUrl(url: string, baseUrl: string): string {
  try {
    const parsed = new URL(url, baseUrl)
    const requestOrigin = new URL(baseUrl).origin
    
    // Only allow same-origin redirects
    if (parsed.origin !== requestOrigin) {
      return '/profile' // Safe fallback
    }
    
    return parsed.pathname + parsed.search
  } catch {
    return '/profile' // Safe fallback on invalid URLs
  }
}
```

#### 2. CSRF Protection
```typescript
// OAuth flows include state parameter validation
function validateCSRFToken(token: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return uuidRegex.test(token)
}
```

#### 3. Input Validation
All authentication inputs are validated using Zod schemas:

```typescript
// Strong password requirements
export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password is too long')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Password must contain uppercase, lowercase, and number')
```

#### 4. XSS Prevention
- No use of `dangerouslySetInnerHTML` anywhere in the authentication system
- All user inputs are properly escaped and validated
- Secure content rendering patterns throughout

### Security Headers

Ensure these headers are configured in your deployment:

```typescript
// In next.config.js or middleware
const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; ..."
}
```

---

## Role-Based Access Control

### User Roles

| Role | Description | Access Level |
|------|-------------|--------------|
| `athlete` | Standard user | View exercises, create submissions, manage own profile |
| `grandmaster` | Trusted community member | All athlete permissions + evaluate submissions |
| `admin` | Administrator | Full system access |

### Route-Level Access Control

Access is controlled by a central ACL in `src/libs/permissions/acl.ts`:

```typescript
export const roleAccessMap = {
  admin: ["/", "/admin", "/submissions", "/exercises", "/rankings", "/profile", "/account"],
  grandmaster: ["/", "/submissions", "/exercises", "/rankings", "/profile", "/account"],
  athlete: ["/", "/exercises", "/rankings", "/profile", "/account"]
}
```

### Page-Level Permissions

Fine-grained permissions are controlled by `src/libs/permissions/pageRoles.ts`:

```typescript
export const pagePermissions = {
  admin: {
    "/profile": { edit: 1, remove: 1 },
    "/submissions": { edit: 1, remove: 1 },
    "/exercises": { edit: 1, remove: 1 }
  },
  // ... other roles
}
```

### Usage Patterns

#### Server-Side Protection
```typescript
import { requireAdmin } from '@/libs/auth/serverPermissions'

export default async function AdminPage() {
  const user = await requireAdmin() // Redirects if not admin
  return <AdminDashboard />
}
```

#### Client-Side Protection
```typescript
import { usePermissions } from '@/hooks/usePermissions'

function MyComponent() {
  const { isAdmin, canEdit } = usePermissions()
  
  return (
    <div>
      {isAdmin() && <AdminControls />}
      {canEdit('exercises') && <EditButton />}
    </div>
  )
}
```

#### Route Guards
```typescript
import { AdminRoute } from '@/libs/auth/roleGuard'

function App() {
  return (
    <AdminRoute>
      <AdminDashboard />
    </AdminRoute>
  )
}
```

---

## Development Guidelines

### Adding Authentication to New Components

#### 1. For Client Components
```typescript
'use client'
import { useAuth } from '@/features/auth/hooks/use-auth'

export function MyComponent() {
  const { user, profile, loading } = useAuth()
  
  if (loading) return <LoadingSpinner />
  if (!user) return <LoginPrompt />
  
  return <AuthenticatedContent />
}
```

#### 2. For Server Components
```typescript
import { getCurrentUser } from '@/libs/auth/serverPermissions'

export default async function MyServerComponent() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return <AuthenticatedContent user={user} />
}
```

#### 3. For API Routes
```typescript
import { createProtectedApiRoute } from '@/libs/auth/protectedApi'

export const POST = createProtectedApiRoute(
  async (request, user) => {
    // Handle authenticated request
    return NextResponse.json({ success: true })
  },
  { roles: ['admin'] }
)
```

### Testing Authentication

#### Mock Setup
```typescript
import { createMockAuthValue } from '@/tests/test-utils'

const mockAuth = createMockAuthValue({
  user: { id: '123', email: '<EMAIL>' },
  profile: { role: 'admin' },
  isAuthenticated: true
})
```

#### Component Testing
```typescript
import { renderWithProviders } from '@/tests/test-utils'

test('renders admin content for admin users', () => {
  renderWithProviders(<MyComponent />, {
    authMock: { userRole: 'admin', isAuthenticated: true }
  })
  
  expect(screen.getByText('Admin Panel')).toBeInTheDocument()
})
```

### Error Handling Patterns

#### Unified Error Handling
```typescript
import { getAuthErrorMessage } from '@/shared/libs/auth/validation'

try {
  const result = await someAuthOperation()
} catch (error) {
  const { message, type } = getAuthErrorMessage(error)
  setError({ message, type })
}
```

#### Error Type Handling
```typescript
const errorInfo = getErrorInfo(errorType)
const className = errorInfo.className
const showSignInSuggestion = errorInfo.showSignInSuggestion
```

---

## Common Patterns

### 1. Conditional Navigation
```typescript
const { isAuthenticated, profile } = useAuth()

const navItems = [
  { href: '/', label: 'Home' },
  { href: '/exercises', label: 'Exercises' },
  ...(isAuthenticated ? [
    { href: '/submit', label: 'Submit' },
    { href: `/profile/${profile?.username}`, label: 'Profile' }
  ] : []),
  ...(isAdmin() ? [
    { href: '/admin', label: 'Admin' }
  ] : [])
]
```

### 2. Profile Links
```typescript
// Safe profile linking that handles missing usernames
const getProfileLink = (profile?: UserProfile) => {
  return profile?.username 
    ? `/profile/${profile.username}`
    : '/profile'
}
```

### 3. Loading States
```typescript
function AuthenticatedComponent() {
  const { loading, isAuthenticated } = useAuth()
  
  if (loading) {
    return <LoadingSpinner />
  }
  
  if (!isAuthenticated) {
    return <LoginPrompt />
  }
  
  return <MainContent />
}
```

### 4. Role-Based UI
```typescript
function ActionButtons() {
  const { canEdit, canDelete } = useRoleAccess()
  
  return (
    <div>
      {canEdit && <EditButton />}
      {canDelete && <DeleteButton />}
    </div>
  )
}
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. "useAuth must be used within an AuthProvider"
**Cause**: Component is rendered outside the AuthProvider context.

**Solution**: Ensure your component tree includes the AuthProvider:
```typescript
// In app/layout.tsx
export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
```

#### 2. User is null but should be authenticated
**Causes**:
- Middleware not properly configured
- Session expired
- Cookie issues

**Debug Steps**:
1. Check browser cookies for `sb-*` cookies
2. Verify middleware is calling `supabase.auth.getUser()`
3. Check console for authentication errors
4. Verify environment variables are set

#### 3. Infinite redirect loops
**Cause**: Route protection logic conflicts.

**Solution**: Check middleware configuration and ensure proper route exclusions:
```typescript
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

#### 4. Permission denied errors
**Causes**:
- User role not properly set
- Permission check logic incorrect
- Profile data not loaded

**Debug Steps**:
1. Verify user has correct role in database
2. Check permission mapping in ACL files
3. Ensure profile data is loaded before permission checks

#### 5. OAuth callback errors
**Common Issues**:
- Incorrect redirect URL configuration
- CSRF state parameter mismatch
- Provider configuration issues

**Solution**: 
1. Verify redirect URLs in OAuth provider settings
2. Check callback route handles all error cases
3. Ensure proper CSRF token validation

### Development Tools

#### 1. Auth State Inspector
```typescript
function AuthDebugger() {
  const auth = useAuth()
  
  if (process.env.NODE_ENV === 'development') {
    console.log('Auth State:', auth)
  }
  
  return null
}
```

#### 2. Role Inspector
```typescript
function RoleDebugger() {
  const { userRole } = useAuth()
  const permissions = usePermissions()
  
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-0 right-0 bg-black text-white p-2">
        Role: {userRole || 'None'} | 
        Admin: {permissions.isAdmin() ? 'Yes' : 'No'}
      </div>
    )
  }
  
  return null
}
```

---

## Migration Notes

### From Previous Auth System

The authentication system has been completely refactored from a chaotic multi-provider system to a unified architecture. Key changes:

#### What Was Removed
- ❌ `src/hooks/useAuth.tsx` (duplicate provider)
- ❌ `src/components/providers/AuthProvider.tsx` (wrapper)
- ❌ `src/contexts/AuthContext.tsx` (old main provider)
- ❌ Multiple conflicting error handling functions
- ❌ Inconsistent type definitions

#### What Was Added
- ✅ `src/shared/types/auth.ts` (unified types)
- ✅ `src/shared/libs/auth/context.tsx` (main provider)
- ✅ `src/shared/libs/auth/validation.ts` (unified validation)
- ✅ `src/features/auth/hooks/use-auth.ts` (simple hook)
- ✅ Enhanced security measures
- ✅ Consistent error handling

#### Breaking Changes
1. **Import Changes**: All components must now import from `@/features/auth/hooks/use-auth`
2. **Type Changes**: `UserProfile` interface updated with unified structure
3. **Property Names**: `displayName` → `full_name`, `avatarUrl` → `avatar_url`
4. **Hook Signature**: Enhanced with additional methods and type safety

#### Migration Checklist
- [x] All old auth imports updated
- [x] Type definitions unified across codebase
- [x] Security vulnerabilities addressed
- [x] Error handling standardized
- [x] Tests updated to use new architecture
- [x] Documentation updated

### Performance Improvements
- Reduced bundle size by eliminating duplicate providers
- Improved type checking prevents runtime errors
- Cleaner re-render patterns with unified state management
- Better memory usage with single context provider

---

## Conclusion

The Arm Power Arena authentication system now provides a secure, type-safe, and maintainable foundation for user authentication and authorization. The unified architecture eliminates previous complexity while providing enhanced security and developer experience.

### Key Principles to Remember
1. **Security First**: Always validate inputs and sanitize outputs
2. **Type Safety**: Use TypeScript throughout the authentication flow
3. **Single Source of Truth**: Use the unified auth system, not custom implementations
4. **Server-Side Safety**: Follow Supabase SSR patterns exactly
5. **Role-Based Access**: Implement permissions at multiple layers for defense in depth

For questions or issues not covered in this documentation, refer to the implementation in the codebase or consult the team leads. 