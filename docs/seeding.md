# Database Seeding Strategy

This document describes the database seeding approach for Armwrestling Power Arena, covering both static SQL and dynamic TypeScript-based seeding.

---

## Overview

Seeding is split into two parts:

1. **Static Data (SQL):**

   - File: `supabase/seed.sql`
   - Loaded automatically by `supabase db reset` after migrations.
   - Seeds initial exercises (core reference data).

2. **Dynamic Data (TypeScript):**
   - Script: `scripts/seed.ts`
   - Run manually via `npm run db:seed`
   - Seeds users, profiles, and (optionally) submissions for development/testing.

---

## 1. SQL Seed File (`supabase/seed.sql`)

- **Purpose:**
  Seeds the `public.exercises` table with 3 sample exercises.
- **Idempotency:**
  Uses `ON CONFLICT (title) DO NOTHING` to allow safe re-running.
- **Execution:**
  Automatically run after migrations by `supabase db reset`.

**Example:**

```sql
insert into public.exercises
  (title, description, evaluation_criteria, medal_thresholds, video_tutorial_url, equipment_required)
values
  ('Pronation Lift', ...),
  ('Cup Progression', ...),
  ('Wrist Roller', ...)
on conflict (title) do nothing;
```

---

## 2. TypeScript Seed Script (`scripts/seed.ts`)

- **Purpose:**
  Seeds user accounts and profiles for development/testing.
- **How to Run:**
  ```
  npm run db:seed
  ```
- **What it does:**

  - Creates three users:
    - `<EMAIL>` (role: admin)
    - `<EMAIL>` (role: grandmaster)
    - `<EMAIL>` (role: athlete)
  - Ensures each user has a unique username and correct profile.
  - Optionally seeds a sample submission for the athlete (see script comments).

- **Idempotency:**

  - Handles "user already exists" gracefully.
  - Upserts profiles by user ID.

- **Environment Variables Required:**

  - `SUPABASE_SERVICE_ROLE_KEY` (never commit this key)
  - `NEXT_PUBLIC_SUPABASE_URL`

- **Security:**
  - `.env` is in `.gitignore` by default.
  - Never commit the Service Role Key.

---

## 3. How to Use

### To reset the database and seed static data:

```bash
npx supabase db reset
```

- Runs all migrations and then `supabase/seed.sql`.

### To seed users and profiles:

```bash
npm run db:seed
```

- Runs `scripts/seed.ts` using `ts-node`.

---

## 4. What Gets Seeded

- **Exercises:**

  - 3 sample exercises (see `supabase/seed.sql` for details).

- **Users/Profiles:**

  - 1 admin, 1 grandmaster, 1 athlete (see above).
  - Each with a unique username and full name.

- **(Optional) Submissions:**
  - The script can be extended to seed sample submissions.

---

## 5. Troubleshooting

- Ensure your `.env` file contains valid `SUPABASE_SERVICE_ROLE_KEY` and `NEXT_PUBLIC_SUPABASE_URL`.
- If you see "user already exists" errors, the script will attempt to upsert the profile.
- For any issues, check the console output for error details.

---

## 6. Extending the Seeder

- Add more users or exercises by editing the respective files.
- To seed more complex data (e.g., submissions, notifications), extend `scripts/seed.ts` as needed.

---

## 7. Security Reminder

- **Never commit your `.env` file or Service Role Key.**
- `.env` is already in `.gitignore` for safety.
