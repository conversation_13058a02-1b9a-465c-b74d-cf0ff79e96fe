# Supabase Client Usage

This document explains how to use the Supabase client utilities configured with `@supabase/ssr` in different Next.js App Router contexts.

## Environment Variables

Ensure your `.env.local` file contains the necessary Supabase URL and Anon Key. The utilities will automatically pick the correct keys based on the `NEXT_PUBLIC_SUPABASE_ENV` variable (`local` or `production`):

```
NEXT_PUBLIC_SUPABASE_ENV=local # or production

# Local Keys (example)
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key

# Production Keys (example)
NEXT_PUBLIC_SUPABASE_URL_PROD=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD=your-prod-anon-key

# Service Role Keys (Needed for Admin Client - SERVER SIDE ONLY)
SUPABASE_SERVICE_ROLE_KEY=your-local-service-role-key
SUPABASE_SERVICE_ROLE_KEY_PROD=your-prod-service-role-key
```

## 1. Client Components

For components marked with `'use client'`, import the pre-configured singleton instance.

**File:** `src/libs/supabase/client.ts`

```typescript
'use client';

import { supabase } from '@/libs/supabase/client';
import { useEffect, useState } from 'react';

export default function ClientSideComponent() {
  const [data, setData] = useState<any[] | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // Example: Fetch public data
      const { data: items, error } = await supabase
        .from('your_public_table')
        .select('*');

      if (error) console.error('Error fetching data:', error);
      else setData(items);
    };

    fetchData();
  }, []);

  return (
    <div>
      <h2>Client Component Data</h2>
      {/* Render data */}
    </div>
  );
}
```

Alternatively, you can create a new instance if needed:

```typescript
import { createClient } from "@/libs/supabase/client";

const supabase = createClient();
// Use supabase instance
```

## 2. Server Components (Read-Only)

For Server Components that _only need to read data_ and do not need to manage user sessions or set cookies.

**File:** `src/libs/supabase/server.ts`

```typescript
import { createClient } from '@/libs/supabase/server';

export default async function ServerSideComponentReadOnly() {
  const supabase = createClient();

  // Example: Fetch public data (RLS rules still apply based on anon key)
  const { data, error } = await supabase
    .from('your_public_table')
    .select('*');

  if (error) console.error('Error fetching data:', error);

  return (
    <div>
      <h2>Server Component Data (Read-Only)</h2>
      {/* Render data */}
    </div>
  );
}
```

## 3. Server Actions & Route Handlers (with Cookie Handling)

For Server Actions and Route Handlers where you need to read/write data _and_ potentially manage the user's session (read/write cookies).

**File:** `src/libs/supabase/server.ts`

```typescript
// Example: Server Action in app/actions.ts
"use server";

import { cookies } from "next/headers";
import { getSupabaseRouteHandlerClient } from "@/libs/supabase/server";

export async function someServerAction() {
  const cookieStore = cookies();
  const supabase = getSupabaseRouteHandlerClient(cookieStore);

  // Example: Get current user session
  const {
    data: { session },
    error: sessionError,
  } = await supabase.auth.getSession();

  if (sessionError || !session) {
    throw new Error("User not authenticated");
  }

  // Example: Insert data associated with the user
  const { error: insertError } = await supabase
    .from("user_specific_table")
    .insert({ user_id: session.user.id, content: "..." });

  if (insertError) {
    console.error("Insert error:", insertError);
    // Handle error
  }

  // ... other logic
}
```

```typescript
// Example: Route Handler in app/api/some-route/route.ts
import { cookies } from "next/headers";
import { getSupabaseRouteHandlerClient } from "@/libs/supabase/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const cookieStore = cookies();
  const supabase = getSupabaseRouteHandlerClient(cookieStore);

  // Example: Fetch data based on user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { data, error } = await supabase
    .from("some_table")
    .select("*")
    .eq("user_id", user.id);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}
```

## 4. Middleware

The middleware (`middleware.ts` at the project root) automatically handles session refreshing using `createServerClient` with the appropriate cookie handling for request/response objects. You generally don't need to interact with this client directly unless customizing the middleware logic.

```typescript
// middleware.ts (Simplified - see full file for complete implementation)
import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({ request });
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          /* ... */
        },
        setAll(cookiesToSet) {
          /* ... */
        },
      },
    },
  );

  // Crucial: Refresh session token
  await supabase.auth.getUser();

  // ... (Add route protection logic here if needed)

  return supabaseResponse;
}

// ... config
```

## 5. Admin Operations (Server-Side Only)

For server-side operations (e.g., in scripts, Server Actions, or Route Handlers) that require bypassing Row Level Security, use the admin client which utilizes the Service Role Key.

**WARNING:** Never expose the Service Role Key or this client to the browser.

**File:** `src/libs/supabase/server.ts`

```typescript
// Example: Server Action requiring admin privileges
"use server";

import { createAdminClient } from "@/libs/supabase/server";
import { cookies } from "next/headers";
import { getSupabaseRouteHandlerClient } from "@/libs/supabase/server";

export async function performAdminTask() {
  // 1. Verify the calling user IS an admin (using the standard client)
  const cookieStore = cookies();
  const supabaseUserClient = getSupabaseRouteHandlerClient(cookieStore);
  const {
    data: { user },
  } = await supabaseUserClient.auth.getUser();

  // Fetch user profile/role - implement getRoleForUser function
  // const userRole = await getRoleForUser(user?.id);
  // if (userRole !== 'admin') {
  //   throw new Error('Permission denied');
  // }

  // 2. Use the admin client for the privileged operation
  const supabaseAdmin = createAdminClient();

  const { error } = await supabaseAdmin
    .from("some_table")
    .delete()
    .eq("some_condition", true); // Bypasses RLS

  if (error) {
    console.error("Admin operation failed:", error);
    // Handle error
  }

  // ...
}
```

## Avatar Upload & Storage RLS

### Avatar Upload Process

- User selects an image file in the `/account/edit` form.
- The file is uploaded to the Supabase Storage bucket `avatars` at path `public/{user_id}` (upsert).
- After upload, the public URL is retrieved and used as the user's `avatar_url` in the profile.
- The URL is cache-busted with a timestamp query param to ensure the latest image is shown.

### Storage RLS Policies

- **Public SELECT**: Anyone (anon or authenticated) can read avatars at `public/*`.
- **Authenticated INSERT/UPDATE/DELETE**: Only the authenticated user can write to their own path `public/{uid}/*`.

#### Example SQL

```sql
-- Public read
create policy "Public can read all avatars"
on storage.objects
for select
to authenticated, anon
using (
  bucket_id = 'avatars'
  and (storage.foldername(name) = 'public' or name like 'public/%')
);

-- Authenticated user write
create policy "User can write their own avatar"
on storage.objects
for insert
to authenticated
with check (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);

create policy "User can update their own avatar"
on storage.objects
for update
to authenticated
using (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
)
with check (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);

create policy "User can delete their own avatar"
on storage.objects
for delete
to authenticated
using (
  bucket_id = 'avatars'
  and name like ('public/' || auth.uid() || '%')
);
```

- See the migrations in `supabase/migrations/` for the full policy definitions.
