# PWA Implementation - Armwrestling Power Arena

This document provides a comprehensive overview and guide for the Progressive Web App (PWA) implementation for Armwrestling Power Arena, covering both development setup and production deployment.

## Table of Contents

1. [Features Implemented](#features-implemented)
2. [Quick Start (Local Testing)](#quick-start-local-testing)
3. [Testing on Mobile Devices](#testing-on-mobile-devices)
4. [User Interface Features](#user-interface-features)
5. [Production Deployment Guide](#production-deployment-guide)
   - [Prerequisites](#prerequisites)
   - [HTTPS Setup](#https-setup)
   - [Web Push Notifications](#web-push-notifications)
   - [User Experience Optimization](#user-experience-optimization)
   - [Service Worker Optimization](#service-worker-optimization)
   - [Testing and Validation](#testing-and-validation)
   - [Monitoring and Maintenance](#monitoring-and-maintenance)
6. [Troubleshooting](#troubleshooting)
7. [Resources](#resources)

## Features Implemented

- ✅ **Offline Support**: The app can function without an internet connection (basic fallback page, potential for more with background sync).
- ✅ **App Installation**: Users can install the app on their devices via a user-friendly prompt.
- ✅ **Push Notifications**: Infrastructure for real-time notifications (requires production setup).
- ✅ **Service Worker**: Background caching and offline fallback.
- ✅ **User-Friendly UI**: Minimizable and dismissible installation prompt.

## Quick Start (Local Testing)

To test the PWA locally:

1. Build the app for production:

   ```bash
   npm run build
   ```

2. Start the production server:

   ```bash
   npm run start
   ```

3. Validate the PWA implementation (optional, requires script setup):
   ```bash
   npm run check:pwa
   ```

## Testing on Mobile Devices

For the full PWA experience, test on actual mobile devices:

1. Ensure your development machine and mobile device are on the same network.
2. Find your computer's local IP address (e.g. `ipconfig getifaddr en0` on macOS, `ipconfig` on Windows).
3. Start the production server:
   ```bash
   npm run start
   ```
4. On your mobile device, go to `http://YOUR_LOCAL_IP:3000` (replace `YOUR_LOCAL_IP` with your actual IP).

### iOS Testing Notes

- iOS requires the app to be added to the home screen for full PWA capabilities.
- Push notifications only work on iOS 16.4+ for installed PWAs.

### Android Testing Notes

- Chrome on Android provides the best PWA experience.
- Look for the "Add to Home Screen" prompt or use the menu option.

## User Interface Features

### Installation Prompt

The PWA installation prompt provides a user-friendly experience:

- **Minimize**: Users can minimize the prompt to a small icon if they want to consider installation later.
- **Dismiss**: Users can dismiss the prompt completely if they're not interested.
- **Persistence**: The user's preference (dismissed) is saved in localStorage to avoid repeated prompts.
- **Responsive**: The prompt is fully responsive and adapts to different screen sizes.

## Production Deployment Guide

This section outlines the steps required to finalize the PWA implementation for Armwrestling Power Arena in a production environment.

### Prerequisites

Before deploying your PWA to production, ensure you have:

- A production hosting environment with SSL/TLS support (HTTPS is mandatory).
- Access to your domain's DNS settings.
- Node.js version 16+ installed.
- Administrator access to the database (Supabase).
- Generated VAPID keys for push notifications.

### HTTPS Setup

PWAs require HTTPS to function properly. This is essential for security and for features like service workers and push notifications.

1. **Obtain an SSL certificate**:

   - Use a service like [Let's Encrypt](https://letsencrypt.org/) for free SSL certificates.
   - For managed hosting (Vercel, Netlify), SSL is usually provided automatically. Ensure it's enabled and enforced.

2. **Configure your domain**:

   - Ensure your hosting redirects HTTP traffic to HTTPS.

   ```bash
   # Example for setting up SSL with Certbot (Let's Encrypt) if managing your own server
   # sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
   ```

3. **Test your SSL configuration**:
   - Use [SSL Labs](https://www.ssllabs.com/ssltest/) to verify correct implementation.
   - Ensure you get at least an "A" rating.

### Web Push Notifications

#### Generating VAPID Keys

VAPID (Voluntary Application Server Identification) keys are required to securely send push notifications.

1. **Install the web-push library globally (if not already done)**:

   ```bash
   npm install -g web-push
   ```

2. **Generate VAPID keys**:

   ```bash
   web-push generate-vapid-keys
   ```

   This will output a public and a private key.

3. **Add keys to your environment variables**:
   Update your production environment variables (e.g., in Vercel, Netlify, or your `.env.production` file):

   ```
   NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_public_key_here
   VAPID_PRIVATE_KEY=your_private_key_here
   ```

   **Important:** Keep the `VAPID_PRIVATE_KEY` secret and secure.

4. **Update the sender email**:
   In `src/app/actions.ts` (or wherever `setVapidDetails` is called), update the mailto address:
   ```typescript
   webpush.setVapidDetails(
     "mailto:<EMAIL>", // Use a real contact email
     process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
     process.env.VAPID_PRIVATE_KEY!,
   );
   ```

#### Database Integration for Subscriptions

Push notification subscriptions need to be stored securely.

1. **Create a `push_subscriptions` table in Supabase**:
   Run the following SQL in your Supabase SQL Editor:

   ```sql
   CREATE TABLE push_subscriptions (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     endpoint TEXT NOT NULL UNIQUE, -- Endpoint must be unique
     p256dh TEXT NOT NULL,
     auth TEXT NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Create index for faster user lookups
   CREATE INDEX idx_push_subscriptions_user_id ON push_subscriptions(user_id);

   -- Enable Row Level Security
   ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;

   -- Policies:
   -- 1. Users can manage their own subscriptions (insert, select, delete based on endpoint or user_id)
   CREATE POLICY "Allow users to manage their own subscriptions"
     ON push_subscriptions
     FOR ALL
     USING (auth.uid() = user_id)
     WITH CHECK (auth.uid() = user_id);

   -- 2. Allow server (using service_role key) to read subscriptions for sending notifications
   -- Note: Sending logic should be in secure server-side actions/functions.
   CREATE POLICY "Allow service role to read all subscriptions"
     ON push_subscriptions
     FOR SELECT
     USING (true); -- Or restrict further if needed
   ```

2. **Update `actions.ts` for Subscription Management**:
   Ensure the `subscribeUser`, `unsubscribeUser`, and `sendNotification` functions in `src/app/actions.ts` use the Supabase client to interact with the `push_subscriptions` table. Replace any in-memory storage with database operations.

   ```typescript
   // Example snippet for subscribeUser in actions.ts
   "use server";
   import { createClient } from "@/libs/supabase/server"; // Use server client for actions
   import webpush from "web-push";
   import { cookies } from "next/headers"; // Needed for server client

   // ... (VAPID setup) ...

   export async function subscribeUser(subscription: PushSubscriptionJSON) {
     const cookieStore = cookies();
     const supabase = createClient(cookieStore);

     try {
       const {
         data: { user },
       } = await supabase.auth.getUser();
       if (!user) throw new Error("User not authenticated");

       const { endpoint, keys } = subscription;
       if (!endpoint || !keys?.p256dh || !keys?.auth) {
         throw new Error("Invalid subscription object");
       }

       const { error } = await supabase.from("push_subscriptions").upsert(
         {
           user_id: user.id,
           endpoint: endpoint,
           p256dh: keys.p256dh,
           auth: keys.auth,
           updated_at: new Date().toISOString(),
         },
         {
           onConflict: "endpoint", // Use endpoint as the conflict target
         },
       );

       if (error) throw error;
       console.log("Subscription saved for user:", user.id);
       return { success: true };
     } catch (error) {
       console.error("Error saving subscription:", error);
       return { success: false, error: (error as Error).message };
     }
   }

   // Implement unsubscribeUser and sendNotification similarly using Supabase
   ```

### User Experience Optimization

1. **Installation Prompt Best Practices**:
   The current implementation includes a minimizable and dismissible prompt. Consider these enhancements for production:

   - **Analytics Tracking**: Track interactions (shown, installed, minimized, dismissed) to understand user behavior.
     ```javascript
     // Example function to call on interaction
     function trackPromptInteraction(action: 'shown' | 'installed' | 'minimized' | 'dismissed') {
       // Send event to your analytics service (e.g., Google Analytics, Plausible)
       console.log(`PWA Prompt: ${action}`);
       // window.gtag?.('event', 'pwa_prompt', { 'action': action });
     }
     ```
   - **Contextual Triggering**: Instead of showing immediately, trigger the prompt after specific user engagement (e.g., completing a task, visiting multiple times). The `beforeinstallprompt` event allows you to defer the prompt.

2. **Intelligent Prompting**:
   Avoid showing the prompt too aggressively.

   ```typescript
   // In PWAInstallPrompt component logic
   useEffect(() => {
     const dismissed = localStorage.getItem("pwa-prompt-dismissed") === "true";
     const sessionCount = parseInt(
       localStorage.getItem("pwa-session-count") || "0",
       10,
     );
     localStorage.setItem("pwa-session-count", (sessionCount + 1).toString());

     // Example: Show only after 2nd session and if not dismissed
     if (sessionCount > 1 && !dismissed && installPromptEvent) {
       // Logic to make the prompt visible
     }
     // ... rest of the effect
   }, [installPromptEvent]); // Add dependencies
   ```

3. **A/B Testing Different Prompt Styles/Timing**:
   Experiment with different UI text, button placement, or trigger points to optimize installation rates.

### Service Worker Optimization

1. **Configure Caching Strategies**:
   Review and refine the caching strategies in your service worker (`public/sw.js` or configured via `next-pwa` in `next.config.ts`). Use appropriate strategies (e.g., `CacheFirst`, `NetworkFirst`, `StaleWhileRevalidate`) for different types of assets (pages, API calls, static assets).

   ```javascript
   // Example using Workbox in sw.js (if customizing)
   importScripts(
     "https://storage.googleapis.com/workbox-cdn/releases/6.5.4/workbox-sw.js",
   );

   workbox.routing.registerRoute(
     ({ request }) => request.destination === "image",
     new workbox.strategies.CacheFirst({
       cacheName: "images-cache",
       plugins: [
         /* Expiration, etc. */
       ],
     }),
   );

   workbox.routing.registerRoute(
     ({ url }) => url.pathname.startsWith("/api/"),
     new workbox.strategies.NetworkFirst({
       cacheName: "api-cache",
       // ... options
     }),
   );
   ```

   If using `next-pwa` defaults, review its configuration options in `next.config.ts`. Ensure `cleanupOutdatedCaches` is true.

2. **Implement Background Sync for Offline Submissions**:
   Allow users to submit forms offline, syncing when connectivity returns.

   - **Store Data**: Use IndexedDB in the client-side form submission logic to store data when offline.
   - **Register Sync**: In the client, after storing data, register a sync event:
     ```javascript
     navigator.serviceWorker.ready.then((registration) => {
       return registration.sync.register("submission-sync");
     });
     ```
   - **Handle Sync in Service Worker**: Add a 'sync' event listener in `public/sw.js`:

     ```javascript
     // In public/sw.js
     self.addEventListener("sync", function (event) {
       if (event.tag === "submission-sync") {
         event.waitUntil(syncPendingSubmissions()); // Define this async function
       }
     });

     async function syncPendingSubmissions() {
       // 1. Retrieve pending submissions from IndexedDB
       // 2. Loop through them and attempt to POST to your API endpoint
       // 3. If successful, remove from IndexedDB
       // 4. Handle errors (retry logic might be complex, basic sync retries automatically)
       console.log("Sync event triggered for submissions");
       // Add IndexedDB and fetch logic here
     }
     ```

### Testing and Validation

Thorough testing is crucial before and after production deployment.

1. **Run Lighthouse Audit**:

   - Open Chrome DevTools > Lighthouse tab.
   - Select 'Progressive Web App' category.
   - Run the audit against your production URL. Aim for a high score and address any reported issues.

2. **Test Offline Functionality**:

   - Use Chrome DevTools > Network tab > Select 'Offline'.
   - Navigate the app. Ensure core content is available and the offline fallback page works.
   - Test offline form submissions if background sync is implemented.

3. **Test Push Notifications**:

   - Subscribe on multiple devices/browsers.
   - Trigger notifications from your backend/server action.
   - Verify notifications are received correctly.
   - Test notification click behavior (opening the correct URL).
   - Test unsubscribing.

4. **Test Installation**:

   - Verify the installation prompt appears under the right conditions.
   - Install the app on different platforms:
     - Desktop (Chrome, Edge)
     - Android (Chrome)
     - iOS (Safari - "Add to Home Screen")
   - Ensure the installed app functions correctly.

5. **Create a PWA Test Script (Optional)**:
   Add a script to `package.json` combining checks:
   ```json
   "scripts": {
     // ... other scripts
     "test:pwa:prod": "lighthouse https://your-production-domain.com --output-path=./lighthouse-report.html --only-categories=pwa --view"
   }
   ```
   Run `npm run test:pwa:prod` as part of your deployment checks.

### Monitoring and Maintenance

1. **Set Up Analytics**:

   - Track PWA-specific events: `appinstalled`, prompt interactions (`shown`, `dismissed`, `accepted`), service worker updates/errors, push notification subscription/permission changes.

2. **Add Error Tracking**:

   - Implement error tracking (e.g., Sentry, LogRocket) in both the main app and the service worker.

   ```javascript
   // In service worker (public/sw.js)
   self.addEventListener("error", function (event) {
     // Log to your monitoring service
     console.error("Service Worker error:", event.error);
     // Sentry?.captureException(event.error);
   });

   self.addEventListener("unhandledrejection", function (event) {
     console.error("Unhandled rejection in Service Worker:", event.reason);
     // Sentry?.captureException(event.reason);
   });
   ```

3. **Version Your Service Worker Cache**:
   If manually managing caches in `sw.js`, include a version number in cache names. This helps in clearing old caches during updates.

   ```javascript
   const CACHE_VERSION = "v1.0.1";
   const STATIC_CACHE_NAME = `static-cache-${CACHE_VERSION}`;
   const DYNAMIC_CACHE_NAME = `dynamic-cache-${CACHE_VERSION}`;
   ```

4. **Implement a Service Worker Update Strategy**:
   Ensure users get the latest version of the app smoothly. `next-pwa` handles this with `skipWaiting: true` and `clientsClaim: true` by default, forcing activation of the new worker. If customizing `sw.js`:

   ```javascript
   // In service worker (public/sw.js)
   self.addEventListener("install", (event) => {
     self.skipWaiting(); // Force activation
   });

   self.addEventListener("activate", (event) => {
     event.waitUntil(
       caches
         .keys()
         .then((cacheNames) => {
           return Promise.all(
             cacheNames
               .filter((cacheName) => {
                 // Define condition to identify old caches (e.g., based on version)
                 return (
                   cacheName.startsWith("your-app-cache-prefix-") &&
                   cacheName !== STATIC_CACHE_NAME && // Example cache names
                   cacheName !== DYNAMIC_CACHE_NAME
                 );
               })
               .map((cacheName) => {
                 console.log("Deleting outdated cache:", cacheName);
                 return caches.delete(cacheName);
               }),
           );
         })
         .then(() => {
           console.log("Service Worker activated and old caches cleaned.");
           return self.clients.claim(); // Take control immediately
         }),
     );
   });
   ```

## Troubleshooting

### Common issues and solutions:

1. **Service worker not registering/updating**:

   - **HTTPS**: Ensure the site is served over HTTPS.
   - **Scope**: Check the service worker's scope. It should typically be `/`. Verify the `sw.js` file is served from the root.
   - **Path**: Ensure the path to `sw.js` in the registration script is correct.
   - **Browser Cache**: Try a hard refresh (Cmd/Ctrl+Shift+R) or clearing site data in DevTools > Application > Storage.
   - **DevTools**: Check the Console for errors and the Application > Service Workers tab for status. Look for errors during installation or activation.
   - **`next-pwa` Config**: If using `next-pwa`, ensure `disable: process.env.NODE_ENV === 'development'` is correctly set (or removed if testing PWA in dev).

2. **Push notifications not working**:

   - **Permissions**: Verify notification permission is granted in browser settings for your site. Re-request if needed.
   - **VAPID Keys**: Double-check that `NEXT_PUBLIC_VAPID_PUBLIC_KEY` (client-side) and `VAPID_PRIVATE_KEY` (server-side) are correctly set in the environment and match.
   - **Subscription Object**: Ensure the `PushSubscription` object is correctly obtained and stored in the database (check `endpoint`, `p256dh`, `auth` fields).
   - **Server Logs**: Check server-side logs (e.g., in your Server Action or API route sending the notification) for errors from the `web-push` library.
   - **Payload Format**: Ensure the payload sent via `webpush.sendNotification` is a valid JSON string or Buffer.

3. **Installation prompt not showing**:

   - **Manifest**: Validate your `manifest.json` using DevTools > Application > Manifest. Check for errors and ensure all required fields (name, short_name, icons, start_url, display) are present.
   - **Icons**: Ensure icon paths in the manifest are correct and the icons are accessible. At least 192x192 and 512x512 are usually needed.
   - **Service Worker**: A registered and active service worker with a `fetch` event handler is required.
   - **HTTPS**: Must be served over HTTPS.
   - **Engagement Heuristics**: Browsers have engagement criteria (e.g., visited twice with >5 seconds between visits). The prompt won't show on the first visit. Test by interacting with the site and revisiting.
   - **Already Installed**: Check if the app is already installed on the device.
   - **`beforeinstallprompt`**: Ensure you are correctly capturing and using the `beforeinstallprompt` event if you defer the prompt.

4. **Offline functionality issues**:
   - **Caching Strategy**: Review your service worker caching strategy. Are the necessary assets (HTML, CSS, JS, API data) being cached correctly?
   - **Cache Storage**: Inspect DevTools > Application > Cache Storage to see what's actually cached.
   - **Fetch Handlers**: Ensure your service worker's `fetch` handler correctly intercepts requests and serves from cache when offline.
   - **Offline Fallback**: Verify the offline fallback page is configured and cached.
   - **Background Sync**: If using background sync, check IndexedDB for pending data and the service worker console for sync event logs/errors.

## Resources

- [PWA Features Page](/pwa) - View and test PWA capabilities in the app (if implemented)
- [Next.js PWA Documentation](https://nextjs.org/docs/app/building-your-application/configuring/progressive-web-apps) (Note: Official Next.js docs might evolve, `next-pwa` is a community solution)
- [Web.dev PWA Checklist](https://web.dev/pwa-checklist/)
- [MDN Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Workbox Documentation](https://developer.chrome.com/docs/workbox/) (Used by `next-pwa`)
- [Web Push Libraries](https://github.com/web-push-libs)
- [PWA Builder Tools](https://www.pwabuilder.com/)
